server:
  port: ${SERVER_PORT:18801}
spring:
  application:
    name: ${APPLICATION_NAME:product-device-server}
  task:
    execution:
      websocket-pool:
        core-size: 20
        max-size: 60
        queue-capacity: 120
        keep-alive: 60
        thread-name-prefix: websocket-
      property-consumer-pool:
        core-size: 10
        max-size: 20
        queue-capacity: 160
        keep-alive: 60
        thread-name-prefix: property-consumer-
      label-consumer-pool:
        core-size: 8
        max-size: 16
        queue-capacity: 16
        keep-alive: 60
        thread-name-prefix: label-consumer-
      subscription-pool:
        core-size: 80
        max-size: 160
        queue-capacity: 1600
        keep-alive: 60
        thread-name-prefix: subscription-job-
      log-pool:
        core-size: 8
        max-size: 16
        queue-capacity: 16
        keep-alive: 60
        thread-name-prefix: log-job-
      statusChange-pool:
        core-size: 4
        max-size: 8
        queue-capacity: 128
        keep-alive: 60
        thread-name-prefix: statusChange-job-
  redis:
    host: ${REDIS_HOST:***********}
    database: ${REDIS_DB:0}
    port: ${REDIS_PORT:21111}
    password: nti56
    lettuce:
      pool:
        max-active: 100       # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 32          # 连接池中的最大空闲连接
        max-wait: 10s          # 连接池最大阻塞等待时间（使用负值表示没有限制）
        min-idle: 4           # 连接池中的最小空闲连接
    redisson:
      config: |
        singleServerConfig:
          address: ${REDIS_ADDRESS:redis://***********:21111}
          database: ${REDIS_DB:0}
          password: nti56
  cache:
    type: redis
  datasource:
    hikari:
      driver-class-name: com.mysql.cj.jdbc.Driver
      minimum-idle: 5
      maximum-pool-size: 50
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
    driver-class-name: com.mysql.cj.jdbc.Driver          # mysql驱动
    url: jdbc:mysql://${MYSQL_HOST:***********}:${MYSQL_PORT:20113}/${PRODUCT_DEVICE_DBNAME:nlink_product_device_dev}?createDatabaseIfNotExist=true&useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&useSSL=false&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true&allowPublicKeyRetrieval=true&serverTimezone=GMT%2B8&rewriteBatchedStatements=true
    username: ${MYSQL_USER:root}
    password: ${MYSQL_PASSWD:Nti56789}
  liquibase:
    change-log: classpath:/db/changelog/db.changelog-master.yml
  jackson:
    default-property-inclusion: ALWAYS
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  freemarker:
    check-template-location: false
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB
mybatis-plus:
  mapper-locations: classpath:/mapper/*.xml
  type-aliases-package: com.nti56.nlink.product.device.server.entity
  type-handlers-package: com.nti56.nlink.product.device.server.type.handler
  configuration:
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
  global-config:
    db-config:
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)

logging:
  config: classpath:logback-delay.xml
  level:
    com.nti56.csplice.tool.signle.logic.sql.remote: ERROR
    com.xxl.job: ERROR
    com.nti56.nlink.common.util.JwtUserInfoUtils: OFF
    # com.nti56.nlink.product.device.server.verticle.post.processor.label: DEBUG
    # com.nti56.nlink.product.device.server.verticle.post.processor.property: DEBUG
    # com.nti56.nlink.product.device.server.domain.thing.device: DEBUG
    # com.nti56.nlink.product.device.server.service.impl: DEBUG
feign:
  client:
    config:
      ds-center:
        loggerLevel: NONE
      default:
        connectTimeout: 300000
        readTimeout: 300000

  httpclient:
    enabled: true
ot:
  publicHost: ***********
  publicPort: 28801
traffic5g:
  appKey: LN19200199
  appScrect: 96bd40794
mqtt:
  ssl: ${MQTT_SSL:true}
  host: ${MQTT_HOST:***********}
  port: ${MQTT_PORT:11884}
  username: ${MQTT_USERNAME:emqx}
  password: ${MQTT_PASSWORD:emqx}
  publicHost: ${MQTT_PUBLIC_HOST:***********}
  publicPort: ${MQTT_PUBLIC_PORT:11884}
  wsHost: ${MQTT_WS_HOST:wss://ot-dev.nti56.com}
  wsPort: ${MQTT_WS_PORT:443}
  wsUrl: ${MQTT_WS_URL:wss://ot-dev.nti56.com:443/mqtt}

influx:
  url: http://***********:20257?readTimeout=240000
  token: faKUqgrhXzh6p_dGZQt9sEEsGheEnPBp_vR506vs7othSI4-lvMyX6AdqrMdeZRqOlWEVg8uHA4zTeT-5-AKvw==
  org: nti
  bucket: nlink-dev
# InfluxDB缓存配置
influxdb:
  cache:
    enabled: true
    max-points-per-tenant: 10000
    max-total-points: 100000
    batch-size: 10000
    write-interval: 10000
    cleanup-interval: 60000
log:
  level: INFO
springdoc:
  version: 1.0.0
  api-docs:
    enabled: true
  swagger-ui:
    path: /swagger
  show-actuator: true
  packages-to-scan: com.nti56.nlink.product.device.server

nlink:
  swagger:
    serverUrl: http://ot-dev.nti56.com/api/product-device
  pd:
    export:
      edge-gateway-config-path: /data/export/edge-gateway/config
      tenant-config-path: /data/export/tenant/config
      sql-export: /data/export/pd/dbfile
      name: export.sql
      jar-path: /data/export/pd/product-device-server.jar
      bootstrap-path: /data/export/pd/config/bootstrap.yml
      application-path: /data/export/pd/config/application.yml
      bat: /data/export/pd/bin/startup.cmd
      shell: /data/export/pd/bin/startup.sh
      runtime-publish: false
  gateway:
    export:
      jar-path: /data/export/gateway/gateway.jar
      bootstrap-path: /data/export/gateway/config/bootstrap.yml
      application-path: /data/export/gateway/config/application.yml
      bat: /data/export/gateway/bin/startup.cmd
      shell: /data/export/gateway/bin/startup.sh
      appName: gateway
nti:
  service:
    log-reserve: 30
  cloudnest:
    sdk:
      appKey: kYdGm2mW
      appSecret: 36d7db09bae8d17e120226f90ca03305e650b7a8
      mode: dev
job:
  enabled: ${JOB_ENABLED:true}
  server:
    admin:
      addresses: http://***********:23456/xxl-job-admin
    userName: admin
    password: nti56@job
    jobGroup: 13
    accessToken: default_token
    executor:
      appName: product-device-server-dev
fault:
  expireTime: 10
  commonModel: '{"id":0,"name":"基础故障模型","descript":"基础故障模型","properties":[{"deviceId":"","deviceName":"","thingModelId":0,"thingModelName":"","name":"start_time","descript":"故障开始时间","labelId":"","labelName":"","dataType":{"type":"Long","isArray":false,"spec":null},"reportType":1,"bindLabel":false,"readOnly":false,"persist":false,"required":false},{"deviceId":"","deviceName":"","thingModelId":0,"thingModelName":"","name":"end_time","descript":"故障结束时间","labelId":"","labelName":"","dataType":{"type":"Long","isArray":false,"spec":null},"reportType":1,"bindLabel":false,"readOnly":false,"persist":false,"required":false},{"deviceId":"","deviceName":"","thingModelId":0,"thingModelName":"","name":"event_status","descript":"故障状态","labelId":"","labelName":"","dataType":{"type":"Short","isArray":false,"spec":null},"reportType":1,"bindLabel":false,"readOnly":false,"persist":false,"required":false}],"events":[],"services":[{"id":0,"thingModelId":0,"thingModelName":"","serviceName":"fault_length","override":false,"async":false,"inputData":[{"name":"startTime","dataType":Long,"isArray":false,"descript":"","dataModelId":0,"editable":false},{"name":"endTime","dataType":Long,"isArray":false,"descript":"","dataModelId":0,"editable":false},{"name":"overTime","dataType":Long,"isArray":false,"descript":"","dataModelId":0,"editable":false},{"name":"faultStatus","dataType":Integer,"isArray":false,"descript":"","dataModelId":0,"editable":false}],"outputData":{"dataType":String,"isArray":false,"descript":"result","dataModelId":0,"outputDataDescript":""},"serviceCode":"","covered":false,"serviceType":0}],"subscriptions":[],"modelType":3}'
platformVersion: 1.14.0
otaDownloadUrl: https://ot-dev.nti56.com/ota/download/otaZip/
management:
  endpoints:
    web:
      exposure:
        include: loggers
device:
  base:
    model: '{"name":"设备基础模型","descript":"设备基础模型","properties":[{"name":"id","descript":"设备资源ID","dataType":{"type":"Long","isArray":false},"reportType":0,"bindLabel":false,"readOnly":true,"persist":false,"required":false},{"name":"name","descript":"设备名称","dataType":{"type":"String","isArray":false},"reportType":0,"bindLabel":false,"readOnly":false,"persist":false,"required":false},{"name":"description","descript":"设备描述","dataType":{"type":"String","isArray":false},"reportType":0,"bindLabel":false,"readOnly":false,"persist":false,"required":false},{"name":"status","descript":"设备状态","dataType":{"type":"Short","isArray":false},"reportType":0,"bindLabel":false,"readOnly":false,"persist":false,"required":false}],"events":[],"services":[],"subscriptions":[],"modelType":5}'

vertx:
  workerPoolSize: 200


sms:
  aliyun:
    accessKeyId: LTAI4G528qGjxsg7e8tm37BU
    accessKeySecret: ******************************
    signName: 今天云
user-center:
  base-url: ${USER_CENTER_URL:https://cloud.nti56.com/cloudnest-develop/api/moduleApi/platform}
  check-token: /ucenter/auth/checkUCenterToken
#  department-list: /sysOrg/list
  tenant-list: /ucenter/sysClient/list
#  user-list: /sysUser/page
#  tenant-detail: /sysClient/getById
#  user-detail: /sysUser/getById
#  user-info-list: /sysUser/list
  add-member: /permission/sysRole/addMember
  role-list: /permission/sysRole/list
  menu-list: /permission/sysMenu/list
  update-role-menu: /permission/sysRole/updateRoleMenu
  role-member-list: /permission/sysRole/memberList
  add-role: /permission/sysRole/sdk/save



urls:
  anon:
    - /swagger-ui/**
    - /swagger
    - /v3/api-docs/**
    - /device/debug/**
    - /it/**
    - /device/template/list
    - /api/websocket/**
    - /actuator/loggers/**

