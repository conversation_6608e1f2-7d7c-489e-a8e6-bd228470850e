<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.ThingModelMapper">
    
    <select id="listThingModel" resultType="com.nti56.nlink.product.device.server.model.ThingModelVo">
        SELECT id,name,model_type,descript,create_time,update_time,tenant_id
        FROM thing_model
        <where>
            DELETED = 0
            <if test="ew.sqlSegment !='' ">
                and
                ${ew.sqlSegment}
            </if>
        </where>
        ORDER BY update_time DESC, create_time DESC
    </select>
    
    <select id="listAllThingModelSimpleBo" resultType="com.nti56.nlink.product.device.server.model.ThingModelSimpleBo">
        SELECT name, id , model_type
        FROM thing_model 
        WHERE deleted = 0
          AND (tenant_id = #{tenantId} or tenant_id = 999)
        ORDER BY id DESC
    </select>

    <select id="listThingModelSimpleBoNotIn" resultType="com.nti56.nlink.product.device.server.model.ThingModelSimpleBo">
        SELECT name, id , model_type
        FROM thing_model 
        WHERE deleted = 0
        AND (tenant_id = #{tenantId} or tenant_id = 999)
            AND id NOT IN (
                <foreach item="id" collection="idList" separator="," >
                    #{id}
                </foreach>
            )
        ORDER BY id DESC
    </select>
    <select id="listDeviceInheritModel"
            resultType="com.nti56.nlink.product.device.server.entity.ThingModelEntity">
        select tm.* from device d
        left join device_model_inherit dmi on d.id = dmi.device_id
        left join thing_model tm on dmi.inherit_thing_model_id = tm.id
        where tm.deleted = 0 and dmi.deleted = 0 and d.deleted = 0
          AND (tm.tenant_id = #{tenantIsolation.tenantId} or tm.tenant_id = 999)
        and d.id = #{deviceId}
        ORDER BY dmi.CREATE_TIME desc
    </select>
    <select id="listInheritModel" resultType="com.nti56.nlink.product.device.server.entity.ThingModelEntity">
        select tm1.* from thing_model tm
        left join thing_model_inherit tmi on tm.id = tmi.thing_model_id
        left join thing_model tm1 on tmi.inherit_thing_model_id = tm1.id
        where tm.deleted = 0 and tmi.deleted = 0 and tm1.deleted = 0
        and (tm1.tenant_id = #{tenantIsolation.tenantId} or tm1.tenant_id = 999)
        and tm.id = #{thingModelId}
        ORDER BY tmi.sort_no
    </select>

    <select id="listAllCommonThingModel"
            resultType="com.nti56.nlink.product.device.server.model.ThingModelSimpleBo">
        SELECT name, id , model_type
        FROM thing_model
        WHERE deleted = 0
          AND model_type = #{modelType}
        ORDER BY id DESC

    </select>
</mapper>