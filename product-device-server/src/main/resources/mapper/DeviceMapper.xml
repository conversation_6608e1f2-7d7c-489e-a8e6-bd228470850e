<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nti56.nlink.product.device.server.mapper.DeviceMapper">
    <update id="updateSyncStatusByLabelIdList">
        update device d
        LEFT JOIN label_bind_relation lbr ON lbr.device_id = d.id
        set d.sync_status = #{syncStatus}
        where
        lbr.label_id
        in (
        <foreach item="labelId" collection="labelIdList" separator=",">
            #{labelId}
        </foreach>
        )
        and d.DELETED = 0
    </update>
    <sql id="selectDevice">
        SELECT d.id,d.name,d.status,d.sync_status,d.channel,d.source,d.edge_gateway_id,d.tenant_id,d.descript,d.last_sync_time,d.resource_id
        <if test="_databaseId=='mysql'"> ,e.`name` edge_gateway_name,e.`type` edge_gateway_type  </if>
        <if test="_databaseId=='dm'"> ,e.name edge_gateway_name,e."type" edge_gateway_type  </if>
        <if test="_databaseId=='oracle'"> ,e."name" edge_gateway_name,e."type" edge_gateway_type  </if>
        <if test="_databaseId=='sqlserver'"> ,e.name edge_gateway_name,e.type edge_gateway_type </if>

        FROM device d
        LEFT JOIN edge_gateway e ON d.edge_gateway_id = e.id
        <where>
            d.DELETED = 0
            <if test="ew.sqlSegment !='' ">
                and
                ${ew.sqlSegment}
            </if>
        </where>
    </sql>
    <select id="pageDevice" resultType="com.nti56.nlink.product.device.server.model.DeviceDto">
        <include refid="selectDevice"/>
    </select>

    <select id="getDevice" resultType="com.nti56.nlink.product.device.server.model.DeviceDto">
        SELECT d.*
        <if test="_databaseId=='mysql'"> ,e.`name` edge_gateway_name,e.`type` edge_gateway_type  </if>
        <if test="_databaseId=='dm'"> ,e.name edge_gateway_name,e."type" edge_gateway_type  </if>
        <if test="_databaseId=='oracle'"> ,e.name edge_gateway_name,e."type" edge_gateway_type  </if>
        <if test="_databaseId=='sqlserver'"> ,e.name edge_gateway_name,e.type edge_gateway_type </if>
        FROM device d
            LEFT JOIN edge_gateway e ON d.edge_gateway_id = e.id
        <where>
            d.DELETED = 0
            <if test="ew.sqlSegment !='' ">
                and
                ${ew.sqlSegment}
            </if>
        </where>
    </select>
    <select id="listDevice" resultType="com.nti56.nlink.product.device.server.entity.DeviceEntity">
        select id,name,status,create_time,update_time,edge_gateway_id from device
        where deleted = 0
         and tenant_id = #{tenantIsolation.tenantId}
        <if test="request.edgeGatewayId !=null ">
        and edge_gateway_id = ${request.edgeGatewayId}
        </if>
        <if test="request.deviceName != null and request.deviceName != '' ">
            and name like CONCAT('%',#{request.deviceName},'%')
        </if>
    </select>



    <select id="listGatewayDevices" resultType="com.nti56.nlink.product.device.server.model.DeviceChannelBo">
        SELECT
            g.channel_id AS channelId,
            r.device_id AS deviceId
        FROM
            device d
                INNER JOIN label_bind_relation r ON ( d.id = r.device_id )
                INNER JOIN label l ON ( r.label_id = l.id )
                INNER JOIN label_group g ON ( l.label_group_id = g.id )
        WHERE
        <if test="_databaseId=='mysql'">  d.`status` = 2 </if>
        <if test="_databaseId=='dm'">  d."status" = 2  </if>
        <if test="_databaseId=='oracle'">  d."status" = 2  </if>
        <if test="_databaseId=='sqlserver'">  d.status = 2  </if>
          AND d.edge_gateway_id = #{edgeGatewayId}
          AND l.tenant_id = #{tenantId}
          AND d.DELETED = 0
          AND r.DELETED = 0
          AND l.DELETED = 0
          AND g.DELETED = 0
        GROUP BY channel_id,device_id
    </select>
    <select id="listExportDevice" resultType="com.nti56.nlink.product.device.server.model.DeviceDto">
        <include refid="selectDevice"/>
    </select>

</mapper>
