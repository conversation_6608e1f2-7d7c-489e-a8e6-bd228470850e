UPDATE `thing_service` SET `service_code` = 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n\'import \"experimental/table\"\' + \"\\n\" +\n\'import \"date\"\' + \"\\n\" +\n\'import \"contrib/tomhollingworth/events\"\' + \"\\n\" +\n\n\'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n\'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n\'nowTime = now()\' + \"\\n\" +\n\'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n\'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n\'t0 =\' + \"\\n\" +\n\'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n\'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n\'|> filter(fn: (r) => (\' + idsStr + \') )\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"property\"] == \"State\" )\' + \"\\n\" +\n\'|> last(column: \"_value\")\' + \"\\n\" +\n\'|> map(fn: (r) => ({ r with _time: queryBegin  }))\' + \"\\n\" +\n\n\'t1 =\' + \"\\n\" +\n\'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n\'|> range(start: queryBegin, stop: nowOrEnd)\' + \"\\n\" +\n\'|> filter(fn: (r) => (\' + idsStr + \') )\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"property\"] == \"State\")\' + \"\\n\" +\n\'|> window(every: 1d, createEmpty: true, offset: -8h)\' + \"\\n\" +\n\'|> table.fill()\' + \"\\n\" +\n\'|> fill(usePrevious: true)\' + \"\\n\" +\n\n\'union(tables: [t0, t1])|> group()\' + \"\\n\" +\n\'|> map (fn: (r) => ({ r with _time: if not(exists r._value) then r._start else r._time}))\' + \"\\n\" +\n\'|> sort(columns:[\"deviceId\",\"_start\"])\' + \"\\n\" +\n\'|> events.duration(unit: 1s,stop:nowOrEnd)\' + \"\\n\" +\n\'|> map (fn: (r) => ({ r with\' + \"\\n\" +\n\'       time_to_stop: (int(v: r._stop) - int(v: r._time))/1000/1000/1000 ,\' + \"\\n\" +\n\'       start_to_time: (int(v: r._time) - int(v: r._start))/1000/1000/1000,\' + \"\\n\" +\n\'       duration: if r.duration<0 then  (int(v: nowOrEnd) - int(v: r._time))/1000/1000/1000 else r.duration\' + \"\\n\" +\n\'        }))\' + \"\\n\" +\n\'|> fill(usePrevious: true)\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"_start\"] > time(v: 0))\' + \"\\n\" +\n\'|> keep(columns: [\"_time\", \"_value\",\"_start\",\"_stop\",\"duration\",\"start_to_time\",\"startTime\",\"stopTime\",\"time_to_stop\",\"timeTime\",\"deviceId\"])\' + \"\\n\" +\n\'|> window(every: 1d, offset: -8h)\' + \"\\n\" +\n\'|> group(columns:[\"_start\",\"deviceId\"])\' + \"\\n\" +\n\'|> reduce(fn: (r, accumulator) => ({\' + \"\\n\" +\n\'  onlineSum:if (r._value ==2 or r._value ==1 ) and r.duration>=r.time_to_stop then accumulator.onlineSum + r.time_to_stop else\' + \"\\n\" +\n\'        if (r._value ==2 or r._value ==1 ) and r.duration<r.time_to_stop then accumulator.onlineSum + r.duration else\' + \"\\n\" +\n\'        accumulator.onlineSum,\' + \"\\n\" +\n\'  taskSum:if r._value ==1  and r.duration>=r.time_to_stop then accumulator.taskSum + r.time_to_stop else\' + \"\\n\" +\n\'        if r._value ==1  and r.duration<r.time_to_stop then accumulator.taskSum + r.duration else\' + \"\\n\" +\n\'        accumulator.taskSum,\' + \"\\n\" +\n\'  preValue:r._value,\' + \"\\n\" +\n\'  Time:r._time,\' + \"\\n\" +\n\'}),identity: {onlineSum:0,taskSum:0,preValue:0,Time:time(v: 2000-01-01T00:00:00+08:00)})\' + \"\\n\" +\n\'|> group(columns:[\"_start\"])\' + \"\\n\" +\n\'|> reduce(fn: (r, accumulator) => ({\' + \"\\n\" +\n\'  onlineSum:accumulator.onlineSum + r.onlineSum,\' + \"\\n\" +\n\'  taskSum:accumulator.taskSum + r.taskSum,\' + \"\\n\" +\n\'  ctime:r.Time,\' + \"\\n\" +\n\'}),identity: {onlineSum:0,taskSum:0, ctime:queryBegin})\' + \"\\n\" +\n\'|>map(fn: (r) => ({r with useRate: if r.onlineSum!=0 then float(v:r.taskSum)/float(v:r.onlineSum) else 0.0,_time: r.ctime}))\' + \"\\n\" +\n\'|>drop(columns:[\"ctime\"])\' + \"\\n\" +\n\'|>group()\';\n\nvar r = me.queryData(q);\nreturn r; ' WHERE `service_name` = 'queryDevicesUseRateByWindow' AND `thing_model_id` = 999;

UPDATE `thing_service` SET `service_code` = 'var q =\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State\")\' + \"\\n\" +\n    \'|> group()\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_value\"] == 3)\' + \"\\n\" +\n    \'|> window(every: 1d, offset: -8h)\' + \"\\n\" +\n    \'|> count()\' + \"\\n\" +\n    \'|> group()\' + \"\\n\" +\n    \'|> map(fn: (r) => ({r with _time: r._stop,faultCount:r._value}))\';\nvar r = me.queryData(q);\nreturn r; '  WHERE `service_name` = 'queryFaultCountByWindow' AND `thing_model_id` = 999;