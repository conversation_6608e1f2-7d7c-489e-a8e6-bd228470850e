UPDATE `thing_service` SET `service_code` = 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'import \"experimental/table\"\' + \"\\n\" +\n    \'import \"date\"\' + \"\\n\" +\n    \'import \"contrib/tomhollingworth/events\"\' + \"\\n\" +\n    \'normTime= time(v: 2020-01-01T16:00:00+08:00)\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: normTime, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + idsStr + \')\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State\" )\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: queryBegin  }))\' + \"\\n\" +\n\n    \'t1 =\' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: nowOrEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => (\' + idsStr + \') )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State\")\' + \"\\n\" +\n    \'|> window(every: 1d, createEmpty: true, offset: -8h)\' + \"\\n\" +\n    \'|> table.fill()\' + \"\\n\" +\n    \'|> fill(usePrevious: true)\' + \"\\n\" +\n\n    \'tFallback =\' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: normTime, stop: nowOrEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => (\' + idsStr + \') )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State\" )\' + \"\\n\" +\n    \'|> last()\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with\' + \"\\n\" +\n    \'    _time: nowOrEnd\' + \"\\n\" +\n    \'}))\' + \"\\n\" +\n    \'|> window(every: 1d, createEmpty: true, offset: -8h)\' + \"\\n\" +\n    \'|> table.fill()\' + \"\\n\" +\n    \'|>filter(fn:(r)=> r._start>queryBegin)\' + \"\\n\" +\n\n    \'tFinal=union(tables: [ t1, tFallback])\' + \"\\n\" +\n    \'|>group(columns:[\"_start\",\"_stop\",\"deviceId\"])\' + \"\\n\" +\n    \'|>unique(column:\"_time\")\' + \"\\n\" +\n\n    \'union(tables: [t0, tFinal])|> group()\' + \"\\n\" +\n    \'|> map (fn: (r) => ({ r with _time: if not(exists r._value) then r._start else r._time}))\' + \"\\n\" +\n    \'|> sort(columns:[\"_start\",\"deviceId\",\"_time\"])\' + \"\\n\" +\n    \'|> group(columns:[\"deviceId\"])\' + \"\\n\" +\n    \'|> events.duration(unit: 1s,stop:nowOrEnd)\' + \"\\n\" +\n    \'|> map (fn: (r) => ({ r with\' + \"\\n\" +\n    \'       time_to_stop: (int(v: r._stop) - int(v: r._time))/1000/1000/1000 ,\' + \"\\n\" +\n    \'       start_to_time: (int(v: r._time) - int(v: r._start))/1000/1000/1000,\' + \"\\n\" +\n    \'       duration: if r.duration<0 then  (int(v: nowOrEnd) - int(v: r._time))/1000/1000/1000 else r.duration\' + \"\\n\" +\n    \'        }))\' + \"\\n\" +\n    \'|> fill(usePrevious: true)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_start\"] > time(v: 0))\' + \"\\n\" +\n    \'|> window(every: 1d, offset: -8h)\' + \"\\n\" +\n    \'|> group(columns:[\"_start\",\"deviceId\"])\' + \"\\n\" +\n    \'|> reduce(fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'  spend:if (r._value ==2 or r._value==3 or r._value==1) and r.duration==0 and r._time==r._stop then accumulator.spend + r.start_to_time else\' + \"\\n\" +\n    \'        if (r._value ==2 or r._value==3 or r._value==1) and r.duration>=r.time_to_stop  then accumulator.spend + r.time_to_stop else\' + \"\\n\" +\n    \'        if (r._value ==2 or r._value==3 or r._value==1) and r.duration<r.time_to_stop then accumulator.spend + r.duration else\' + \"\\n\" +\n    \'        accumulator.spend,\' + \"\\n\" +\n    \'  faultSpend:if r._value==3  and r.duration==0 and r._time==r._stop then accumulator.faultSpend + r.start_to_time else\' + \"\\n\" +\n    \'        if r._value==3 and r.duration>=r.time_to_stop  then accumulator.faultSpend + r.time_to_stop else\' + \"\\n\" +\n    \'        if r._value==3  and r.duration<r.time_to_stop then accumulator.faultSpend + r.duration else\' + \"\\n\" +\n    \'        accumulator.faultSpend,\' + \"\\n\" +\n    \'  preValue:r._value,\' + \"\\n\" +\n    \'  Time:r._time,\' + \"\\n\" +\n    \'}),identity: {spend:0,preValue:0,faultSpend:0,Time:normTime})\' + \"\\n\" +\n    \'|> group(columns:[\"_start\"])\' + \"\\n\" +\n    \'|> reduce(fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'  spend:accumulator.spend + r.spend,\' + \"\\n\" +\n    \'  faultSpend:accumulator.faultSpend + r.faultSpend,\' + \"\\n\" +\n    \'  ctime:r.Time\' + \"\\n\" +\n    \'}),identity: {spend:0,faultSpend:0, ctime:queryBegin})\' + \"\\n\" +\n    \'|>map(fn: (r) => ({r with faultRate: if r.spend==0 then 0.0 else float(v:r.faultSpend)/float(v:r.spend)}))\' + \"\\n\" +\n    \'|>map(fn: (r) => ({r with _time: r._start}))\' + \"\\n\" +\n    \'|>group()\';\n\nvar r = me.queryData(q);\nreturn r;' WHERE `service_name` = 'queryDevicesUseRateByWindow' AND `thing_model_id` = 999;