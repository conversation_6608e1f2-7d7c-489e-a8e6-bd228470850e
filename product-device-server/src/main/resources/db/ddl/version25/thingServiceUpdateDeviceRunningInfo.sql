-- 更新第一条记录的service_code
UPDATE `thing_service`
SET `service_code` = 'var ids = input.deviceIds;\r\nvar idArray = ids.split(\',\');\r\n\r\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\r\n\r\n// 构建 Flux 查询字符串\r\nvar q =\r\n    \'import \"date\"\\n\' +\r\n    \'import \"contrib/tomhollingworth/events\"\\n\' +\r\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\\n\' +\r\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\\n\' +\r\n    \'nowTime = now()\\n\' +\r\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\\n\' +\r\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\\n\' +\r\n\r\n    // 查询 state 数据   // 查询初始状态（合并所有状态）\r\n    \'initialState = from(bucket: \"\' + me.influxBucket + \'\")\\n\' +\r\n    \'  |> range(start: 0, stop: queryBegin)\\n\' +\r\n    \'  |> filter(fn: (r) => \' + idsStr + \')\\n\' +\r\n    \'  |> filter(fn: (r) => r[\"property\"] == \"State\")\\n\' +\r\n    \'  |> last()\\n\' +\r\n    // 计算该状态在查询开始前的持续时间，并截断到queryBegin\r\n    \'|> map (fn: (r) => ({ r with _time: if r._time < queryBegin  then queryBegin else r._time})) \\n\' +\r\n\r\n    // 查询实时状态数据\r\n    \'state1 = from(bucket: \"\' + me.influxBucket + \'\")\\n\' +\r\n    \'  |> range(start: queryBegin, stop: nowOrEnd)\\n\' +\r\n    \'  |> filter(fn: (r) => \' + idsStr + \')\\n\' +\r\n    \'  |> filter(fn: (r) => r[\"property\"] == \"State\")\\n\' +\r\n\r\n    // 合并数据并计算持续时间\r\n    \'state = union(tables: [initialState, state1])\\n\' +\r\n    \' |> group(columns:[\"deviceId\"])\\n\' +\r\n    \' |> events.duration(unit: 1s,stop:nowOrEnd)\\n\' +\r\n\r\n    // 按状态分类\r\n    \'taskSpendData = state |> group() \' + \"\\n\" +\r\n    \'   |> group(columns:[\"deviceId\",\"_value\"])\\n\' +\r\n    \'  |> filter(fn: (r) => ( r[\"_value\"] == 1 ) )\\n\' +\r\n    \'  |> sum(column: \"duration\")\\n\' +\r\n\r\n    \'freeData = state |> group() \' + \"\\n\" +\r\n    \'   |> group(columns:[\"deviceId\",\"_value\"])\\n\' +\r\n    \'  |> filter(fn: (r) => ( r[\"_value\"] == 2 ) )\\n\' +\r\n    \'  |> sum(column: \"duration\")\\n\' +\r\n\r\n    \'faultData = state |> group() \' + \"\\n\" +\r\n    \'   |> group(columns:[\"deviceId\",\"_value\"])\\n\' +\r\n    \'  |> filter(fn: (r) => ( r[\"_value\"] == 3 ) )\\n\' +\r\n    \'  |> sum(column: \"duration\")\\n\' +\r\n\r\n    \'offLineData = state |> group() \' + \"\\n\" +\r\n    \'   |> group(columns:[\"deviceId\",\"_value\"])\\n\' +\r\n    \'  |> filter(fn: (r) => ( r[\"_value\"] == 4 ) )\\n\' +\r\n    \'  |> sum(column: \"duration\")\\n\' +\r\n\r\n    \'faultCountData = state |> group() \' + \"\\n\" +\r\n    \'   |> group(columns:[\"deviceId\",\"_value\"])\\n\' +\r\n    \'  |> filter(fn: (r) => ( r[\"_value\"] == 3 ) )\\n\' +\r\n\r\n\r\n\r\n    \'taskSpendFunc = (tables=<-) => tables\\n\' +\r\n    \'|> group(columns: [\"deviceId\"])\\n\' +\r\n    \'|> reduce(fn: (r, accumulator) => ({ spend: if r._value == 1 then accumulator.spend + r.duration else accumulator.spend }), identity: { spend: 0 })\\n\' +\r\n\r\n    \'freeFunc = (tables=<-) => tables\\n\' +\r\n    \'|> group(columns: [\"deviceId\"])\\n\' +\r\n    \'|> reduce(fn: (r, accumulator) => ({ free: if r._value == 2 then accumulator.free + r.duration else accumulator.free }), identity: { free: 0 })\\n\' +\r\n\r\n    \'faultFunc = (tables=<-) => tables\\n\' +\r\n    \'|> group(columns: [\"deviceId\"])\\n\' +\r\n    \'|> reduce(fn: (r, accumulator) => ({ fault: if r._value == 3 then accumulator.fault + r.duration else accumulator.fault }), identity: { fault: 0 })\\n\' +\r\n\r\n    \'offLineFunc = (tables=<-) => tables\\n\' +\r\n    \'|> group(columns: [\"deviceId\"])\\n\' +\r\n    \'|> reduce(fn: (r, accumulator) => ({ offLine: if r._value == 4 then accumulator.offLine + r.duration else accumulator.offLine }), identity: { offLine: 0 })\\n\' +\r\n\r\n\r\n    \'faultCountFunc = (tables=<-) => tables\\n\' +\r\n    \'|> group(columns: [\"deviceId\"])\\n\' +\r\n    \'|> reduce(fn: (r, accumulator) => ({ faultCount: accumulator.faultCount + (if r._value == 3 then 1 else 0) }), identity: { faultCount: 0 })\\n\' +\r\n\r\n    // 执行计算\r\n    \'taskSpend = taskSpendFunc(tables: taskSpendData)\\n\' +\r\n    \'freeTime = freeFunc(tables: freeData)\\n\' +\r\n    \'faultTime = faultFunc(tables: faultData)\\n\' +\r\n    \'offLineTime = offLineFunc(tables: offLineData)\\n\' +\r\n    \'faultCount = faultCountFunc(tables: faultCountData)\\n\' +\r\n\r\n    \'result = union(tables: [\\n\' +\r\n    \'    taskSpend    |> map(fn: (r) => ({ r with _field: \"spend\", _value: r.spend })),\\n\' +\r\n    \'    freeTime     |> map(fn: (r) => ({ r with _field: \"free\", _value: r.free })),\\n\' +\r\n    \'    faultTime    |> map(fn: (r) => ({ r with _field: \"fault\", _value: r.fault })),\\n\' +\r\n    \'    offLineTime    |> map(fn: (r) => ({ r with _field: \"offLine\", _value: r.offLine })),\\n\' +\r\n    \'    faultCount   |> map(fn: (r) => ({ r with _field: \"faultCount\", _value: r.faultCount }))\\n\' +\r\n    \'])\\n\' +\r\n\r\n    // 按设备分组并聚合数据\r\n    \'|> group(columns: [\"deviceId\"])\\n\' +\r\n    \'|> reduce(\\n\' +\r\n    \'    fn: (r, accumulator) => ({\\n\' +\r\n    \'        spend:     accumulator.spend + (if r._field == \"spend\" then r._value else 0),\\n\' +\r\n    \'        free:      accumulator.free + (if r._field == \"free\" then r._value else 0),\\n\' +\r\n    \'        fault:     accumulator.fault + (if r._field == \"fault\" then r._value else 0),\\n\' +\r\n    \'        offLine:     accumulator.offLine + (if r._field == \"offLine\" then r._value else 0),\\n\' +\r\n    \'        faultCount:accumulator.faultCount + (if r._field == \"faultCount\" then r._value else 0)\\n\' +\r\n    \'    }),\\n\' +\r\n    \'    identity: { offLine: 0, spend: 0, free: 0, fault: 0, faultCount: 0 }\\n\' +\r\n    \')\\n\' +\r\n\r\n    // 计算最终指标\r\n    \'|> map(fn: (r) => ({\\n\' +\r\n    \'    deviceId: r.deviceId,\\n\' +\r\n    \'    onlineTime: r.spend + r.free,\\n\' +\r\n    \'    taskSpend: r.spend,\\n\' +\r\n    \'    freeTime: r.free,\\n\' +\r\n    \'    faultTime: r.fault,\\n\' +\r\n    \'    offLineTime: r.offLine,\\n\' +\r\n    \'    faultCount: r.faultCount,\\n\' +\r\n    \'    useRate:  if ( r.spend + r.free) == 0 then 0.0 else float(v: r.spend) / float(v: (r.spend + r.free)),\\n\' +\r\n    \'    faultRate:if (r.spend + r.free + r.fault) == 0 then 0.0 else float(v: r.fault) / float(v: (r.spend + r.free + r.fault))\\n\' +\r\n    \'}))\\n\' +\r\n    \'result\\n\';\r\n\r\n\r\n\r\n\r\n// 执行查询并返回结果\r\nvar r = me.queryData(q);\r\nreturn r;'
WHERE `service_name` = 'queryDeviceListRunningInfo' AND `thing_model_id` = 999;
