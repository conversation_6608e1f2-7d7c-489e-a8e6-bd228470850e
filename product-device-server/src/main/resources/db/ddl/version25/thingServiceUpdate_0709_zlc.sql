-- 更新第一条记录的service_code
UPDATE `thing_service`
SET `service_code` = 'var q =\n    \'import "experimental/table"\' + "\\n" +\n    \'import "date"\' + "\\n" +\n    \'import "contrib/tomhollingworth/events"\' + "\\n" +\n\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + "\\n" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + "\\n" +\n    \'nowTime = now()\' + "\\n" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + "\\n" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + "\\n" +\n    \'t0 =\' + "\\n" +\n    \'from(bucket:"\' + me.influxBucket + \'")\' + "\\n" +\n    \'|> range(start: 0, stop: queryBegin)\' + "\\n" +\n    \'|> filter(fn: (r) => r["deviceId"] == "\' + me.id + \'")\' + "\\n" +\n    \'|> filter(fn: (r) => r["property"] == "State_OnLine" )\' + "\\n" +\n    \'|> last(column: "_value")\' + "\\n" +\n    \'|> map(fn: (r) => ({ r with _time: queryBegin  }))\' + "\\n" +\n\n\n    \'t1 =\' + "\\n" +\n    \'from(bucket:"\' + me.influxBucket + \'")\' + "\\n" +\n    \'|> range(start: queryBegin, stop: nowOrEnd)\' + "\\n" +\n    \'|> filter(fn: (r) => r["deviceId"] == "\' + me.id + \'")\' + "\\n" +\n    \'|> filter(fn: (r) => r["property"] == "State_OnLine")\' + "\\n" +\n    \'|> window(every: 1d, createEmpty: true)\' + "\\n" +\n    \'|> table.fill()\' + "\\n" +\n    \'|> fill(usePrevious: true)\' + "\\n" +\n\n\n    \'union(tables: [t0, t1])|> group()\' + "\\n" +\n    \'|> map (fn: (r) => ({ r with _time: if not(exists r._value) then r._start else r._time}))\' + "\\n" +\n    \'|> sort(columns:["deviceId","_start"])\' + "\\n" +\n    \'|> events.duration(unit: 1s,stop:nowOrEnd)\' + "\\n" +\n    \'|> map (fn: (r) => ({ r with\' + "\\n" +\n    \'       startTime: uint(v: r._start),\' + "\\n" +\n    \'       stopTime: uint(v: r._stop),\' + "\\n" +\n    \'       timeTime: uint(v: r._time),\' + "\\n" +\n    \'       time_to_stop: (int(v: r._stop) - int(v: r._time))/1000/1000/1000 ,\' + "\\n" +\n    \'       start_to_time: (int(v: r._time) - int(v: r._start))/1000/1000/1000,\' + "\\n" +\n    \'       duration: if r.duration<0 then  (int(v: nowOrEnd) - int(v: r._time))/1000/1000/1000 else r.duration\' + "\\n" +\n    \'        }))\' + "\\n" +\n    \'|> fill(usePrevious: true)\' + "\\n" +\n    \'|> filter(fn: (r) => r["_start"] > time(v: 0) and (r["_value"]==true or r["_value"]==false) )\' + "\\n" +\n    \'|> keep(columns: ["_time", "_value","_start","_stop","duration","start_to_time","startTime","stopTime","time_to_stop","timeTime","deviceId"])\' + "\\n" +\n    \'|> group(columns:["_start","deviceId"])\' + "\\n" +\n    \'|> reduce( fn: (r, accumulator) => ( {\' + "\\n" +\n    \'    start:r._start,\' + "\\n" +\n    \'    preItem: r._value,\' + "\\n" +\n    \'    deviceId: r.deviceId,\' + "\\n" +\n    \'    spend:\' + "\\n" +\n    \'        if r._value ==false and accumulator.preItem==false and r.start_to_time!=0 then accumulator.spend + r.start_to_time else\' + "\\n" +\n    \'        if r._value ==false and accumulator.preItem==true and r.start_to_time!=0 then accumulator.spend else\' + "\\n" +\n    \'        if r._value ==true  and r._time==r._start and r.start_to_time==0 then\' + "\\n" +\n    \'               if  r.duration>r.time_to_stop  then accumulator.spend+r.time_to_stop else accumulator.spend+r.duration\' + "\\n" +\n    \'        else\' + "\\n" +\n    \'        if r._value ==true  and accumulator.preItem==false and r.start_to_time!=0  then\' + "\\n" +\n    \'          if  r.duration>r.time_to_stop  then accumulator.spend+r.time_to_stop else accumulator.spend+r.duration\' + "\\n" +\n    \'        else\' + "\\n" +\n    \'          accumulator.spend\' + "\\n" +\n    \'} ), identity: {preItem:false,spend: 0,start:time(v: 2025-06-03T14:53:39+08:00),deviceId:""})\' + "\\n" +\n    \'|> group(columns:["_start"])\' + "\\n" +\n    \'|> mean(column:"spend")\' + "\\n" +\n    \'|> map(fn: (r) => ({r with _time: r._start,onlineSumSeconds:int(v:r.spend),onlineSumStr:string(v:duration(v:int(v:r.spend)*1000*1000*1000))}))\' + "\\n" +\n    \'|> group()\';\n\n\n\nvar r = me.queryData(q);\nreturn r;'
WHERE `service_name` = 'queryOnLineTimeByWindow' AND `thing_model_id` = 999;

-- 更新第二条记录的service_code
UPDATE `thing_service`
SET `service_code` = 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\nvar idsStr = \'r["deviceId"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n\'import "experimental/table"\' + "\\n" +\n\'import "date"\' + "\\n" +\n\'import "contrib/tomhollingworth/events"\' + "\\n" +\n\n\'queryBegin = time(v: \' + input.begin + \'+08:00)\' + "\\n" +\n\'queryEnd = time(v: \' + input.end + \'+08:00)\' + "\\n" +\n\'nowTime = now()\' + "\\n" +\n\'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + "\\n" +\n\'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + "\\n" +\n\'t0 =\' + "\\n" +\n\'from(bucket:"\' + me.influxBucket + \'")\' + "\\n" +\n\'|> range(start: 0, stop: queryBegin)\' + "\\n" +\n\'|> filter(fn: (r) =>  (\' + idsStr + \') )\' + "\\n" +\n\'|> filter(fn: (r) => r["property"] == "State_OnLine" )\' + "\\n" +\n\'|> last(column: "_value")\' + "\\n" +\n\'|> map(fn: (r) => ({ r with _time: queryBegin  }))\' + "\\n" +\n\n\n\'t1 =\' + "\\n" +\n\'from(bucket:"\' + me.influxBucket + \'")\' + "\\n" +\n\'|> range(start: queryBegin, stop: nowOrEnd)\' + "\\n" +\n\'|> filter(fn: (r) =>  (\' + idsStr + \') )\' + "\\n" +\n\'|> filter(fn: (r) => r["property"] == "State_OnLine")\' + "\\n" +\n\'|> window(every: 1d, createEmpty: true)\' + "\\n" +\n\'|> table.fill()\' + "\\n" +\n\'|> fill(usePrevious: true)\' + "\\n" +\n\n\n\'union(tables: [t0, t1])|> group()\' + "\\n" +\n\'|> map (fn: (r) => ({ r with _time: if not(exists r._value) then r._start else r._time}))\' + "\\n" +\n\'|> sort(columns:["deviceId","_start"])\' + "\\n" +\n\'|> events.duration(unit: 1s,stop:nowOrEnd)\' + "\\n" +\n\'|> map (fn: (r) => ({ r with\' + "\\n" +\n\'       startTime: uint(v: r._start),\' + "\\n" +\n\'       stopTime: uint(v: r._stop),\' + "\\n" +\n\'       timeTime: uint(v: r._time),\' + "\\n" +\n\'       time_to_stop: (int(v: r._stop) - int(v: r._time))/1000/1000/1000 ,\' + "\\n" +\n\'       start_to_time: (int(v: r._time) - int(v: r._start))/1000/1000/1000,\' + "\\n" +\n\'       duration: if r.duration<0 then  (int(v: nowOrEnd) - int(v: r._time))/1000/1000/1000 else r.duration\' + "\\n" +\n\'        }))\' + "\\n" +\n\'|> fill(usePrevious: true)\' + "\\n" +\n\'|> filter(fn: (r) => r["_start"] > time(v: 0) and (r["_value"]==true or r["_value"]==false) )\' + "\\n" +\n\'|> keep(columns: ["_time", "_value","_start","_stop","duration","start_to_time","startTime","stopTime","time_to_stop","timeTime","deviceId"])\' + "\\n" +\n\'|> group(columns:["_start","deviceId"])\' + "\\n" +\n\'|> reduce( fn: (r, accumulator) => ( {\' + "\\n" +\n\'    start:r._start,\' + "\\n" +\n\'    preItem: r._value,\' + "\\n" +\n\'    deviceId: r.deviceId,\' + "\\n" +\n\'    spend:\' + "\\n" +\n\'        if r._value ==false and accumulator.preItem==false and r.start_to_time!=0 then accumulator.spend + r.start_to_time else\' + "\\n" +\n\'        if r._value ==false and accumulator.preItem==true and r.start_to_time!=0 then accumulator.spend else\' + "\\n" +\n\'        if r._value ==true  and r._time==r._start and r.start_to_time==0 then\' + "\\n" +\n\'               if  r.duration>r.time_to_stop  then accumulator.spend+r.time_to_stop else accumulator.spend+r.duration\' + "\\n" +\n\'        else\' + "\\n" +\n\'        if r._value ==true  and accumulator.preItem==false and r.start_to_time!=0  then\' + "\\n" +\n\'          if  r.duration>r.time_to_stop  then accumulator.spend+r.time_to_stop else accumulator.spend+r.duration\' + "\\n" +\n\'        else\' + "\\n" +\n\'          accumulator.spend\' + "\\n" +\n\'} ), identity: {preItem:false,spend: 0,start:time(v: 2025-06-03T14:53:39+08:00),deviceId:""})\' + "\\n" +\n\'|> group(columns:["_start"])\' + "\\n" +\n\'|> mean(column:"spend")\' + "\\n" +\n\'|> map(fn: (r) => ({r with _time: r._start,onlineSumSeconds:int(v:r.spend),onlineSumStr:string(v:duration(v:int(v:r.spend)*1000*1000*1000))}))\' + "\\n" +\n\'|> group()\';\n\n\n\nvar r = me.queryData(q);\nreturn r;'
WHERE `service_name` = 'queryDevicesOnLineTimeByWindow' AND `thing_model_id` = 999;