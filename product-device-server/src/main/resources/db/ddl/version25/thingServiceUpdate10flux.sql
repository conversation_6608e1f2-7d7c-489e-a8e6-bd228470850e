UPDATE `thing_service` SET `service_code` = 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n\'import \"experimental/table\"\' + \"\\n\" +\n\'import \"date\"\' + \"\\n\" +\n\'import \"contrib/tomhollingworth/events\"\' + \"\\n\" +\n\n\'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n\'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n\'nowTime = now()\' + \"\\n\" +\n\'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n\'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n\'t0 =\' + \"\\n\" +\n\'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n\'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n\'|> filter(fn: (r) => (\' + idsStr + \') )\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"property\"] == \"State\" )\' + \"\\n\" +\n\'|> last(column: \"_value\")\' + \"\\n\" +\n\'|> map(fn: (r) => ({ r with _time: queryBegin  }))\' + \"\\n\" +\n\n\'t1 =\' + \"\\n\" +\n\'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n\'|> range(start: queryBegin, stop: nowOrEnd)\' + \"\\n\" +\n\'|> filter(fn: (r) => (\' + idsStr + \') )\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"property\"] == \"State\")\' + \"\\n\" +\n\'|> window(every: 1d, createEmpty: true, offset: -8h)\' + \"\\n\" +\n\'|> table.fill()\' + \"\\n\" +\n\'|> fill(usePrevious: true)\' + \"\\n\" +\n\n\'union(tables: [t0, t1])|> group()\' + \"\\n\" +\n\'|> map (fn: (r) => ({ r with _time: if not(exists r._value) then r._start else r._time}))\' + \"\\n\" +\n\'|> sort(columns:[\"deviceId\",\"_start\"])\' + \"\\n\" +\n\'|> events.duration(unit: 1s,stop:nowOrEnd)\' + \"\\n\" +\n\'|> map (fn: (r) => ({ r with\' + \"\\n\" +\n\'       time_to_stop: (int(v: r._stop) - int(v: r._time))/1000/1000/1000 ,\' + \"\\n\" +\n\'       start_to_time: (int(v: r._time) - int(v: r._start))/1000/1000/1000,\' + \"\\n\" +\n\'       duration: if r.duration<0 then  (int(v: nowOrEnd) - int(v: r._time))/1000/1000/1000 else r.duration\' + \"\\n\" +\n\'        }))\' + \"\\n\" +\n\'|> fill(usePrevious: true)\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"_start\"] > time(v: 0))\' + \"\\n\" +\n\'|> keep(columns: [\"_time\", \"_value\",\"_start\",\"_stop\",\"duration\",\"start_to_time\",\"startTime\",\"stopTime\",\"time_to_stop\",\"timeTime\",\"deviceId\"])\' + \"\\n\" +\n\'|> window(every: 1d, offset: -8h)\' + \"\\n\" +\n\'|> group(columns:[\"_start\",\"deviceId\"])\' + \"\\n\" +\n\'|> reduce(fn: (r, accumulator) => ({\' + \"\\n\" +\n\'  onlineSum:if (r._value ==2 or r._value ==1 ) and r.duration>=r.time_to_stop then accumulator.onlineSum + r.time_to_stop else\' + \"\\n\" +\n\'        if (r._value ==2 or r._value ==1 ) and r.duration<r.time_to_stop then accumulator.onlineSum + r.duration else\' + \"\\n\" +\n\'        accumulator.onlineSum,\' + \"\\n\" +\n\'  taskSum:if r._value ==1  and r.duration>=r.time_to_stop then accumulator.taskSum + r.time_to_stop else\' + \"\\n\" +\n\'        if r._value ==1  and r.duration<r.time_to_stop then accumulator.taskSum + r.duration else\' + \"\\n\" +\n\'        accumulator.taskSum,\' + \"\\n\" +\n\'  preValue:r._value,\' + \"\\n\" +\n\'  Time:r._time,\' + \"\\n\" +\n\'}),identity: {onlineSum:0,taskSum:0,preValue:0,Time:time(v: 2000-01-01T00:00:00+08:00)})\' + \"\\n\" +\n\'|> group(columns:[\"_start\"])\' + \"\\n\" +\n\'|> reduce(fn: (r, accumulator) => ({\' + \"\\n\" +\n\'  onlineSum:accumulator.onlineSum + r.onlineSum,\' + \"\\n\" +\n\'  taskSum:accumulator.taskSum + r.taskSum,\' + \"\\n\" +\n\'  ctime:r.Time,\' + \"\\n\" +\n\'}),identity: {onlineSum:0,taskSum:0, ctime:queryBegin})\' + \"\\n\" +\n\'|>map(fn: (r) => ({r with useRate: float(v:r.taskSum)/float(v:r.onlineSum),_time: r.ctime}))\' + \"\\n\" +\n\'|>drop(columns:[\"ctime\"])\' + \"\\n\" +\n\'|>group()\';\n\nvar r = me.queryData(q);\nreturn r; '  WHERE `service_name` = 'queryDevicesUseRateByWindow' AND `thing_model_id` = 999;
UPDATE `thing_service` SET `service_code` = 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n\'import \"experimental/table\"\' + \"\\n\" +\n\'import \"date\"\' + \"\\n\" +\n\'import \"contrib/tomhollingworth/events\"\' + \"\\n\" +\n\n\'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n\'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n\'nowTime = now()\' + \"\\n\" +\n\'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n\'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n\'t0 =\' + \"\\n\" +\n\'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n\'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n\'|> filter(fn: (r) => (\' + idsStr + \') )\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"property\"] == \"State\" )\' + \"\\n\" +\n\'|> last(column: \"_value\")\' + \"\\n\" +\n\'|> map(fn: (r) => ({ r with _time: queryBegin  }))\' + \"\\n\" +\n\n\'t1 =\' + \"\\n\" +\n\'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n\'|> range(start: queryBegin, stop: nowOrEnd)\' + \"\\n\" +\n\'|> filter(fn: (r) => (\' + idsStr + \') )\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"property\"] == \"State\")\' + \"\\n\" +\n\'|> window(every: 1d, createEmpty: true, offset: -8h)\' + \"\\n\" +\n\'|> table.fill()\' + \"\\n\" +\n\'|> fill(usePrevious: true)\' + \"\\n\" +\n\n\'union(tables: [t0, t1])|> group()\' + \"\\n\" +\n\'|> map (fn: (r) => ({ r with _time: if not(exists r._value) then r._start else r._time}))\' + \"\\n\" +\n\'|> sort(columns:[\"deviceId\",\"_start\"])\' + \"\\n\" +\n\'|> events.duration(unit: 1s,stop:nowOrEnd)\' + \"\\n\" +\n\'|> map (fn: (r) => ({ r with\' + \"\\n\" +\n\'       time_to_stop: (int(v: r._stop) - int(v: r._time))/1000/1000/1000 ,\' + \"\\n\" +\n\'       start_to_time: (int(v: r._time) - int(v: r._start))/1000/1000/1000,\' + \"\\n\" +\n\'       duration: if r.duration<0 then  (int(v: nowOrEnd) - int(v: r._time))/1000/1000/1000 else r.duration\' + \"\\n\" +\n\'        }))\' + \"\\n\" +\n\'|> fill(usePrevious: true)\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"_start\"] > time(v: 0))\' + \"\\n\" +\n\'|> keep(columns: [\"_time\", \"_value\",\"_start\",\"_stop\",\"duration\",\"start_to_time\",\"startTime\",\"stopTime\",\"time_to_stop\",\"timeTime\",\"deviceId\"])\' + \"\\n\" +\n\'|> window(every: 1d, offset: -8h)\' + \"\\n\" +\n\'|> group(columns:[\"_start\",\"deviceId\"])\' + \"\\n\" +\n\'|> reduce(fn: (r, accumulator) => ({\' + \"\\n\" +\n\'  spend:if r._value ==1 and r.duration>=r.time_to_stop  then accumulator.spend + r.time_to_stop else\' + \"\\n\" +\n\'        if r._value ==1 and r.duration<r.time_to_stop then accumulator.spend + r.duration else\' + \"\\n\" +\n\'        accumulator.spend,\' + \"\\n\" +\n\'  preValue:r._value,\' + \"\\n\" +\n\'  Time:r._time,\' + \"\\n\" +\n\'}),identity: {spend:0,preValue:0,Time:time(v: 2000-01-01T00:00:00+08:00)})\' + \"\\n\" +\n\'|> group(columns:[\"_start\"])\' + \"\\n\" +\n\'|> reduce(fn: (r, accumulator) => ({\' + \"\\n\" +\n\'  spend:accumulator.spend + r.spend,\' + \"\\n\" +\n\'  ctime:r.Time,\' + \"\\n\" +\n\'  count:accumulator.count+1\' + \"\\n\" +\n\'}),identity: {spend:0, ctime:queryBegin,count:0})\' + \"\\n\" +\n\'|>map(fn: (r) => ({r with spend: int(v:r.spend/r.count)}))\' + \"\\n\" +\n\'|>map(fn: (r) => ({r with _time: r._start,taskSumSeconds:int(v:r.spend),taskSumStr:string(v:duration(v:int(v:r.spend)*1000*1000*1000))}))\' + \"\\n\" +\n\'|>group()\';\n\nvar r = me.queryData(q);\nreturn r;' WHERE `service_name` = 'queryDevicesTaskTimeByWindow' AND `thing_model_id` = 999;
UPDATE `thing_service` SET `service_code` = 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n\'import \"experimental/table\"\' + \"\\n\" +\n\'import \"date\"\' + \"\\n\" +\n\'import \"contrib/tomhollingworth/events\"\' + \"\\n\" +\n\n\'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n\'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n\'nowTime = now()\' + \"\\n\" +\n\'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n\'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n\'t0 =\' + \"\\n\" +\n\'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n\'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n\'|> filter(fn: (r) => (\' + idsStr + \') )\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"property\"] == \"State\" )\' + \"\\n\" +\n\'|> last(column: \"_value\")\' + \"\\n\" +\n\'|> map(fn: (r) => ({ r with _time: queryBegin  }))\' + \"\\n\" +\n\n\'t1 =\' + \"\\n\" +\n\'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n\'|> range(start: queryBegin, stop: nowOrEnd)\' + \"\\n\" +\n\'|> filter(fn: (r) => (\' + idsStr + \') )\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"property\"] == \"State\")\' + \"\\n\" +\n\'|> window(every: 1d, createEmpty: true, offset: -8h)\' + \"\\n\" +\n\'|> table.fill()\' + \"\\n\" +\n\'|> fill(usePrevious: true)\' + \"\\n\" +\n\n\'union(tables: [t0, t1])|> group()\' + \"\\n\" +\n\'|> map (fn: (r) => ({ r with _time: if not(exists r._value) then r._start else r._time}))\' + \"\\n\" +\n\'|> sort(columns:[\"deviceId\",\"_start\"])\' + \"\\n\" +\n\'|> events.duration(unit: 1s,stop:nowOrEnd)\' + \"\\n\" +\n\'|> map (fn: (r) => ({ r with\' + \"\\n\" +\n\'       time_to_stop: (int(v: r._stop) - int(v: r._time))/1000/1000/1000 ,\' + \"\\n\" +\n\'       start_to_time: (int(v: r._time) - int(v: r._start))/1000/1000/1000,\' + \"\\n\" +\n\'       duration: if r.duration<0 then  (int(v: nowOrEnd) - int(v: r._time))/1000/1000/1000 else r.duration\' + \"\\n\" +\n\'        }))\' + \"\\n\" +\n\'|> fill(usePrevious: true)\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"_start\"] > time(v: 0))\' + \"\\n\" +\n\'|> keep(columns: [\"_time\", \"_value\",\"_start\",\"_stop\",\"duration\",\"start_to_time\",\"startTime\",\"stopTime\",\"time_to_stop\",\"timeTime\",\"deviceId\"])\' + \"\\n\" +\n\'|> window(every: 1d, offset: -8h)\' + \"\\n\" +\n\'|> group(columns:[\"_start\",\"deviceId\"])\' + \"\\n\" +\n\'|> reduce(fn: (r, accumulator) => ({\' + \"\\n\" +\n\'  spend:if r._value ==2 and r.duration>=r.time_to_stop  then accumulator.spend + r.time_to_stop else\' + \"\\n\" +\n\'        if r._value ==2 and r.duration<r.time_to_stop then accumulator.spend + r.duration else\' + \"\\n\" +\n\'        accumulator.spend,\' + \"\\n\" +\n\'  preValue:r._value,\' + \"\\n\" +\n\'  Time:r._time,\' + \"\\n\" +\n\'}),identity: {spend:0,preValue:0,Time:time(v: 2000-01-01T00:00:00+08:00)})\' + \"\\n\" +\n\'|> group(columns:[\"_start\"])\' + \"\\n\" +\n\'|> reduce(fn: (r, accumulator) => ({\' + \"\\n\" +\n\'  spend:accumulator.spend + r.spend,\' + \"\\n\" +\n\'  ctime:r.Time,\' + \"\\n\" +\n\'  count:accumulator.count+1\' + \"\\n\" +\n\'}),identity: {spend:0, ctime:queryBegin,count:0})\' + \"\\n\" +\n\'|>map(fn: (r) => ({r with spend: int(v:r.spend/r.count)}))\' + \"\\n\" +\n\'|>map(fn: (r) => ({r with _time: r._start,standBySumSeconds:int(v:r.spend),standBySumStr:string(v:duration(v:int(v:r.spend)*1000*1000*1000))}))\' + \"\\n\" +\n\'|>group()\';\n\nvar r = me.queryData(q);\nreturn r; ' WHERE `service_name` = 'queryDevicesStandByTimeByWindow' AND `thing_model_id` = 999;
UPDATE `thing_service` SET `service_code` = 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n\'import \"experimental/table\"\' + \"\\n\" +\n\'import \"date\"\' + \"\\n\" +\n\'import \"contrib/tomhollingworth/events\"\' + \"\\n\" +\n\n\'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n\'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n\'nowTime = now()\' + \"\\n\" +\n\'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n\'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\'t0 =\' + \"\\n\" +\n\'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n\'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n\'|> filter(fn: (r) =>  (\' + idsStr + \') )\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"property\"] == \"State\" )\' + \"\\n\" +\n\'|> last(column: \"_value\")\' + \"\\n\" +\n\'|> map(fn: (r) => ({ r with _time: queryBegin  }))\' + \"\\n\" +\n\n\'t1 =\' + \"\\n\" +\n\'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n\'|> range(start: queryBegin, stop: nowOrEnd)\' + \"\\n\" +\n\'|> filter(fn: (r) =>  (\' + idsStr + \') )\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"property\"] == \"State\")\' + \"\\n\" +\n\'|> window(every: 1d, createEmpty: true, offset: -8h)\' + \"\\n\" +\n\'|> table.fill()\' + \"\\n\" +\n\'|> fill(usePrevious: true)\' + \"\\n\" +\n\n\'union(tables: [t0, t1])|> group()\' + \"\\n\" +\n\'|> map (fn: (r) => ({ r with _time: if not(exists r._value) then r._start else r._time}))\' + \"\\n\" +\n\'|> sort(columns:[\"deviceId\",\"_start\"])\' + \"\\n\" +\n\'|> events.duration(unit: 1s,stop:nowOrEnd)\' + \"\\n\" +\n\'|> map (fn: (r) => ({ r with\' + \"\\n\" +\n\'       startTime: uint(v: r._start),\' + \"\\n\" +\n\'       stopTime: uint(v: r._stop),\' + \"\\n\" +\n\'       timeTime: uint(v: r._time),\' + \"\\n\" +\n\'       time_to_stop: (int(v: r._stop) - int(v: r._time))/1000/1000/1000 ,\' + \"\\n\" +\n\'       start_to_time: (int(v: r._time) - int(v: r._start))/1000/1000/1000,\' + \"\\n\" +\n\'       duration: if r.duration<0 then  (int(v: nowOrEnd) - int(v: r._time))/1000/1000/1000 else r.duration\' + \"\\n\" +\n\'        }))\' + \"\\n\" +\n\'|> fill(usePrevious: true)\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"_start\"] > time(v: 0))\' + \"\\n\" +\n\'|> keep(columns: [\"_time\", \"_value\",\"_start\",\"_stop\",\"duration\",\"start_to_time\",\"startTime\",\"stopTime\",\"time_to_stop\",\"timeTime\",\"deviceId\"])\' + \"\\n\" +\n\'|> window(every: 1d, offset: -8h)\' + \"\\n\" +\n\'|> group(columns:[\"_start\",\"deviceId\"])\' + \"\\n\" +\n\'|> reduce(fn: (r, accumulator) => ({\' + \"\\n\" +\n\'  spend:if (r._value ==2 or r._value ==1 ) and r.duration>=r.time_to_stop then accumulator.spend + r.time_to_stop else\' + \"\\n\" +\n\'        if (r._value ==2 or r._value ==1 ) and accumulator.preValue==0 then accumulator.spend + r.start_to_time else\' + \"\\n\" +\n\'        if (r._value ==2 or r._value ==1 ) and r.duration<r.time_to_stop then accumulator.spend + r.duration else\' + \"\\n\" +\n\'        accumulator.spend,\' + \"\\n\" +\n\'  preValue:r._value,\' + \"\\n\" +\n\'  Time:r._time,\' + \"\\n\" +\n\'}),identity: {spend:0,preValue:0,Time:time(v: 2000-01-01T00:00:00+08:00)})\' + \"\\n\" +\n\'|> group(columns:[\"_start\"])\' + \"\\n\" +\n\'|> reduce(fn: (r, accumulator) => ({\' + \"\\n\" +\n\'  spend:accumulator.spend + r.spend,\' + \"\\n\" +\n\'  ctime:r.Time,\' + \"\\n\" +\n\'  count:accumulator.count+1\' + \"\\n\" +\n\'}),identity: {spend:0, ctime:queryBegin,count:0})\' + \"\\n\" +\n\'|>map(fn: (r) => ({r with spend: int(v:r.spend/r.count)}))\' + \"\\n\" +\n\'|>map(fn: (r) => ({r with _time: r._start,onlineSumSeconds:int(v:r.spend),onlineSumStr:string(v:duration(v:int(v:r.spend)*1000*1000*1000))}))\' + \"\\n\" +\n\'|>group()\';\n\nvar r = me.queryData(q);\nreturn r; ' WHERE `service_name` = 'queryDevicesOnLineTimeByWindow' AND `thing_model_id` = 999;
UPDATE `thing_service` SET `service_code` = 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar n = input.n;\nvar q =\n    \'import \"experimental/table\"\' + \"\\n\" +\n    \'import \"date\"\' + \"\\n\" +\n    \'import \"contrib/tomhollingworth/events\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 =\' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + idsStr + \')\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State\" )\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: queryBegin  }))\' + \"\\n\" +\n\n    \'t1 =\' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: nowOrEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + idsStr + \')\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State\")\' + \"\\n\" +\n    \'|> window(every: 1d, createEmpty: true, offset: -8h)\' + \"\\n\" +\n    \'|> table.fill()\' + \"\\n\" +\n    \'|> fill(usePrevious: true)\' + \"\\n\" +\n\n    \'union(tables: [t0, t1])|>group(columns:[\"deviceId\"])\' + \"\\n\" +\n    \'|> map (fn: (r) => ({ r with _time: if not(exists r._value) then r._start else r._time}))\' + \"\\n\" +\n    \'|> sort(columns:[\"deviceId\",\"_start\"])\' + \"\\n\" +\n    \'|> events.duration(unit: 1s,stop:nowOrEnd)\' + \"\\n\" +\n    \'|> map (fn: (r) => ({ r with\' + \"\\n\" +\n    \'       time_to_stop: (int(v: r._stop) - int(v: r._time))/1000/1000/1000 ,\' + \"\\n\" +\n    \'       start_to_time: (int(v: r._time) - int(v: r._start))/1000/1000/1000,\' + \"\\n\" +\n    \'       duration: if r.duration<0 then  (int(v: nowOrEnd) - int(v: r._time))/1000/1000/1000 else r.duration\' + \"\\n\" +\n    \'        }))\' + \"\\n\" +\n\n    \'|> fill(usePrevious: true)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_start\"] > time(v: 0) and exists r[\"_value\"])\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\", \"_value\",\"_start\",\"_stop\",\"duration\",\"start_to_time\",\"startTime\",\"stopTime\",\"time_to_stop\",\"timeTime\",\"deviceId\"])\' + \"\\n\" +\n    \'|> window(every: 1d, offset: -8h)\' + \"\\n\" +\n    \'|> group(columns:[\"_start\",\"deviceId\"])\' + \"\\n\" +\n    \'|> reduce(fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'  spend:if r._value ==3 and r.duration>=r.time_to_stop  then accumulator.spend + r.time_to_stop else\' + \"\\n\" +\n    \'        if r._value ==3 and r.duration<r.time_to_stop then accumulator.spend + r.duration else\' + \"\\n\" +\n    \'        accumulator.spend,\' + \"\\n\" +\n    \'  preValue:r._value,\' + \"\\n\" +\n    \'  Time:r._time,\' + \"\\n\" +\n    \'  deviceId:r.deviceId\' + \"\\n\" +\n    \'}),identity: {spend:0,preValue:0,Time:time(v: 2000-01-01T00:00:00+08:00),deviceId:\"\"})\' + \"\\n\" +\n    \'|>group(columns:[\"deviceId\"])\' + \"\\n\" +\n    \'|>sum(column:\"spend\")\' + \"\\n\" +\n    \'|>filter(fn: (r) => r[\"spend\"] > 0)\' + \"\\n\" +\n    \'|>map(fn: (r) => ({r with durStr:string(v:duration(v:int(v:r.spend)*1000*1000*1000)),duration:r.spend}))\' + \"\\n\" +\n    \'|>group()\' + \"\\n\" +\n    \'|> sort(columns: [\"duration\"], desc: true)\' + \"\\n\" +\n    \'|> limit(n: \' + n + \')\';\n\nvar r = me.queryData(q);\nreturn r;' WHERE `service_name` = 'queryDevicesFaultTimeSumTopn' AND `thing_model_id` = 999;
UPDATE `thing_service` SET `service_code` = 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'import \"experimental/table\"\' + \"\\n\" +\n    \'import \"date\"\' + \"\\n\" +\n    \'import \"contrib/tomhollingworth/events\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n    \n    \'t0 =\' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + idsStr + \' )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State\" )\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: queryBegin  }))\' + \"\\n\" +\n    \n    \'t1 =\' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: nowOrEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + idsStr + \' )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State\")\' + \"\\n\" +\n    \'|> window(every: 1d, createEmpty: true, offset: -8h)\' + \"\\n\" +\n    \'|> table.fill()\' + \"\\n\" +\n    \'|> fill(usePrevious: true)\' + \"\\n\" +\n    \n    \'union(tables: [t0, t1])|> group()\' + \"\\n\" +\n    \'|> map (fn: (r) => ({ r with _time: if not(exists r._value) then r._start else r._time}))\' + \"\\n\" +\n    \'|> sort(columns:[\"deviceId\",\"_start\"])\' + \"\\n\" +\n    \'|> events.duration(unit: 1s,stop:nowOrEnd)\' + \"\\n\" +\n    \n    \'|> map (fn: (r) => ({ r with\' + \"\\n\" +\n    \'       time_to_stop: (int(v: r._stop) - int(v: r._time))/1000/1000/1000 ,\' + \"\\n\" +\n    \'       start_to_time: (int(v: r._time) - int(v: r._start))/1000/1000/1000,\' + \"\\n\" +\n    \'       duration: if r.duration<0 then  (int(v: nowOrEnd) - int(v: r._time))/1000/1000/1000 else r.duration\' + \"\\n\" +\n    \'        }))\' + \"\\n\" +\n    \'|> fill(usePrevious: true)\' + \"\\n\" +\n    \n    \'|> filter(fn: (r) => r[\"_start\"] > time(v: 0))\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\", \"_value\",\"_start\",\"_stop\",\"duration\",\"start_to_time\",\"time_to_stop\",\"deviceId\"])\' + \"\\n\" +\n    \'|> window(every: 1d, offset: -8h)\' + \"\\n\" +\n    \'|> group(columns:[\"_start\",\"deviceId\"])\' + \"\\n\" +\n    \'|> reduce(fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'  spend:if r._value ==3 and r.duration>=r.time_to_stop  then accumulator.spend + r.time_to_stop else\' + \"\\n\" +\n    \'        if r._value ==3 and r.duration<r.time_to_stop then accumulator.spend + r.duration else\' + \"\\n\" +\n    \'        accumulator.spend,\' + \"\\n\" +\n    \'  preValue:r._value,\' + \"\\n\" +\n    \'  Time:r._time,\' + \"\\n\" +\n    \'}),identity: {spend:0,preValue:0,Time:time(v: 2000-01-01T00:00:00+08:00)})\' + \"\\n\" +\n    \'|> group(columns:[\"_start\"])\' + \"\\n\" +\n    \'|> reduce(fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'  spend:accumulator.spend + r.spend,\' + \"\\n\" +\n    \'  ctime:r.Time,\' + \"\\n\" +\n    \'  count:accumulator.count+1\' + \"\\n\" +\n    \'}),identity: {spend:0, ctime:queryBegin,count:0})\' + \"\\n\" +\n    \'|>map(fn: (r) => ({r with spend: int(v:r.spend/r.count)}))\' + \"\\n\" +\n    \'|>map(fn: (r) => ({r with _time: r._start,faultSumSeconds:int(v:r.spend),faultSumStr:string(v:duration(v:int(v:r.spend)*1000*1000*1000))}))\' + \"\\n\" +\n    \'|>group()\';\n\nvar r = me.queryData(q);\nreturn r; '  WHERE `service_name` = 'queryDevicesFaultTimeByWindow' AND `thing_model_id` = 999;
UPDATE `thing_service` SET `service_code` = 'var q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'lastFromT1 = t1 \' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd }))\' + \"\\n\" +\n\n    \'fallbackFromT0 = t0\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd }))\' + \"\\n\" +\n\n    \'t2Result = union(tables: [lastFromT1, fallbackFromT0])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2Result])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> group(columns: [\"deviceId\",\"type\"])\' + \"\\n\" +\n    \'|> sum(column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durSeconds: r[\"dur\"]} ))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durStr: string(v: duration( v: ( r[\"durSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\";\nvar r = me.queryData(q);\nreturn r;' WHERE `service_name` = 'queryFaultTimeSum' AND `thing_model_id` = 999;
UPDATE `thing_service` SET `service_code` = 'var q =\n    \'import \"date\"\' + \"\\n\" +\n    \'import \"contrib/tomhollingworth/events\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'lastFromT1 = t1 \' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd }))\' + \"\\n\" +\n    \'fallbackFromT0 = t0\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd }))\' + \"\\n\" +\n    \'t2 = union(tables: [lastFromT1, fallbackFromT0])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> unique(column: \"_time\")  // 去除可能的重复时间点\' + \"\\n\" +\n    \'// 使用events.duration()函数计算事件持续时间，避免elapsed()的问题\' + \"\\n\" +\n    \'|> events.duration(unit: 1s, stop: nowOrEnd)\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with\' + \"\\n\" +\n    \'    stateType: if r._value == 1 then \"task\"\' + \"\\n\" +\n    \'              else if r._value == 2 then \"idle\"\' + \"\\n\" +\n    \'              else if r._value == 3 then \"fault\"\' + \"\\n\" +\n    \'              else if r._value == 4 then \"offline\"\' + \"\\n\" +\n    \'              else \"unknown\"\' + \"\\n\" +\n    \'}))\' + \"\\n\" +\n    \'// 过滤出故障状态的记录\' + \"\\n\" +\n    \'|> filter(fn: (r) => r._value == 3 and r.stateType == \"fault\")\' + \"\\n\" +\n    \'// 计算故障的平均时长\' + \"\\n\" +\n    \'|> group(columns: [\"deviceId\"])\' + \"\\n\" +\n    \'|> mean(column: \"duration\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with\' + \"\\n\" +\n    \'    faultAvgStr:  string(v:duration( v: int(v:r[\"duration\"])*1000000000 )),\' + \"\\n\" +\n    \'}))\';\n\nvar r = me.queryData(q);\nreturn r;' WHERE `service_name` = 'queryFaultTimeAvg' AND `thing_model_id` = 999;
UPDATE `thing_service` SET `service_code` = 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar n = input.n;\nvar q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + idsStr + \')\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + idsStr + \')\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'union(tables: [t0, t1])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_value\"] == 3)\' + \"\\n\" +\n    \'|> group(columns: [\"deviceId\"])\' + \"\\n\" +\n    \'|> count(column: \"_value\")\' + \"\\n\" +\n    \'|> group()\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with dur: r[\"_value\"] }))\' + \"\\n\" +\n    \'|> drop(columns: [\"_value\"])\' + \"\\n\" +\n    \'|> sort(columns: [\"dur\"], desc: true)\' + \"\\n\" +\n    \'|> limit(n: \' + n + \')\';\n\nvar r = me.queryData(q);\nreturn r;' WHERE `service_name` = 'queryDevicesFaultCountTopn' AND `thing_model_id` = 999;
UPDATE `thing_service` SET `service_code` = 'var q =\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n    \'t0 = from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State\")\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin })) \' + \"\\n\" +\n    \'t1 = from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State\")\' + \"\\n\" +\n    \'union(tables: [t0, t1])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with\' + \"\\n\" +\n    \'    duration: if exists r.duration then r.duration else 0,\' + \"\\n\" +\n    \'    time_to_stop: if exists r.time_to_stop then r.time_to_stop else 0\' + \"\\n\" +\n    \'}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with \' + \"\\n\" +\n    \'    _time: date.truncate(t: r._time, unit: 1d),\' + \"\\n\" +\n    \'    effectiveDuration: if r.duration > 86400 then r.time_to_stop \' + \"\\n\" +\n    \'                      else if r.duration < r.time_to_stop then r.duration \' + \"\\n\" +\n    \'                      else r.time_to_stop\' + \"\\n\" +\n    \'}))\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_value\"] == 3)\' + \"\\n\" +\n    \'|> filter(fn: (r) => exists r.effectiveDuration and r.effectiveDuration >= 0)\' + \"\\n\" +\n    \'|> group(columns: [\"deviceId\", \"_time\"])\' + \"\\n\" +\n    \'|> reduce(\' + \"\\n\" +\n    \'    fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'        deviceId: r.deviceId,\' + \"\\n\" +\n    \'        _time: r._time,\' + \"\\n\" +\n    \'        faultCount: accumulator.faultCount + 1,\' + \"\\n\" +\n    \'    }),\' + \"\\n\" +\n    \'    identity: {\' + \"\\n\" +\n    \'        deviceId: \"\",\' + \"\\n\" +\n    \'        _time: 2021-01-01T00:00:00Z,\' + \"\\n\" +\n    \'        faultCount: 0,\' + \"\\n\" +\n    \'    }\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|>group()\';\nvar r = me.queryData(q);\nreturn r;' WHERE `service_name` = 'queryFaultCountByWindow' AND `thing_model_id` = 999;