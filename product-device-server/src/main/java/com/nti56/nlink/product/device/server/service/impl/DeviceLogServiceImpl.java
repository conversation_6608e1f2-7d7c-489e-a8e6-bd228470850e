package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.QueryApi;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.influxdb.query.dsl.Flux;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.DeviceServiceLogMapper;
import com.nti56.nlink.product.device.server.model.device.DeviceSelectBo;
import com.nti56.nlink.product.device.server.model.deviceLog.*;
import com.nti56.nlink.product.device.server.service.IDeviceLogService;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import com.nti56.nlink.product.device.server.service.IJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName DeviceLogServiceImpl
 * @date 2022/8/8 17:32
 * @Version 1.0
 */
@Service
@Slf4j
public class DeviceLogServiceImpl  extends BaseServiceImpl<DeviceServiceLogMapper, DeviceServiceLogEntity> implements IDeviceLogService , IJobService {

    @Autowired
    InfluxDBClient influxDBClient;

    @Value("${influx.bucket}")
    private String bucket;

    @Value("${nti.service.log-reserve}")
    Long logReserveDays;

    @Autowired
    @Lazy
    IDeviceService deviceService;
    
    @Lazy
    @Autowired
    DeviceServiceLogMapper deviceServiceLogMapper;

    @Autowired @Lazy
    RedisTemplate redisTemplate;

    @Autowired @Lazy
    StringRedisTemplate stringRedisTemplate;
    
    @Override
    public Result<Page<PropertyLogBo>>  pagePropertiesLog(Page<PropertyLogBo> page, PropertyLogConditionBo condition, TenantIsolation tenantIsolation) {
        String dataType = condition.getDataType();
        ThingDataTypeEnum thingDataTypeEnum = ThingDataTypeEnum.typeOfName(dataType);
        if (ObjectUtil.isEmpty(thingDataTypeEnum) ||
                ThingDataTypeEnum.DATA_MODEL.equals(thingDataTypeEnum) ||
                ThingDataTypeEnum.VOID.equals(thingDataTypeEnum)
        ) {
            log.error("请选择要查询的数据类型！错误数据类型：{}",condition.getDataType());
            return Result.error("请选择要查询的数据类型！");
        }
        QueryApi queryApi = influxDBClient.getQueryApi();
        String countScrip = buildPropertyPageFluxScrip(page, condition, tenantIsolation, true);
        log.info("InfluxDB查询：{}",countScrip);
        List<FluxTable> queryCount;
        try {
            queryCount = queryApi.query(countScrip);
        } catch (Exception e) {
            log.error("设备属性查询报错，ERROR:{}",e.getMessage());
            return Result.error("设备属性查询报错:"+ e.getMessage());
        }
        if (dealPage(page,queryCount)) {
            return Result.ok(page);
        }
        String scrip = buildPropertyPageFluxScrip(page, condition, tenantIsolation, false);
        log.info("InfluxDB查询：{}",scrip);
        List<PropertyLogBo> list;
        try {
            list = queryApi.query(scrip, PropertyLogBo.class);
        } catch (Exception e) {
            log.error("数据量较大建议缩短时间区间查询,ERROR:{},数据:{}",e.getMessage(),page.getTotal());
            return Result.error("数据量较大建议缩短时间区间查询,数据:"+ page.getTotal());
        }
        Map<Long,String> deviceMap = new HashMap<>();
        Result<List<DeviceEntity>> listResult = deviceService.listDeviceByIds(tenantIsolation.getTenantId(), condition.getDeviceIds(),DeviceEntity::getId, DeviceEntity::getName);
        if (listResult.getSignal()) {
            deviceMap = listResult.getResult().stream().collect(Collectors.toMap(DeviceEntity::getId, DeviceEntity::getName));
        }
        Map<Long, String> finalDeviceMap = deviceMap;
        list.forEach(propertyLogBo -> propertyLogBo.setDeviceName(finalDeviceMap.get(Long.valueOf(propertyLogBo.getDeviceId()))));
        page.setRecords(list);
        return Result.ok(page);
    }

    private String buildPropertyPageFluxScrip(Page<PropertyLogBo> page, PropertyLogConditionBo condition, TenantIsolation tenantIsolation,boolean isCount) {
        Flux flux = Flux.from(bucket);
        if (ObjectUtil.isNotEmpty(condition.getStart()) && ObjectUtil.isNotEmpty(condition.getStop())) {
            flux = flux.range(condition.getStart(),condition.getStop());
        }else {
            flux = flux.range(0L);
        }
        Restrictions restriction = Restrictions.and(
                Restrictions.measurement().equal(tenantIsolation.getTenantId().toString()), //改为分租户存储
                Restrictions.tag("dataType").equal(condition.getDataType()),
                Restrictions.tag("propertyAlias").exists()
        );
        flux = flux.filter(restriction);
        if (CollectionUtil.isNotEmpty(condition.getDeviceIds())){
            List<Restrictions> conditions = new ArrayList<>();
            condition.getDeviceIds().forEach(deviceId -> conditions.add(Restrictions.tag("deviceId").equal(deviceId.toString())));
            flux = flux.filter(Restrictions.or(conditions.toArray(new Restrictions[0])));
        }
        if (CollectionUtil.isNotEmpty(condition.getProperties())) {
            List<Restrictions> conditions = new ArrayList<>();
            condition.getProperties().forEach(property -> conditions.add(Restrictions.tag("property").equal(property)));
            flux = flux.filter(Restrictions.or(conditions.toArray(new Restrictions[0])));
        }
        if (ObjectUtil.isNotEmpty(condition.getValue())) {
            Object o = dataConversion(condition.getDataType(), condition.getValue());
            if (ObjectUtil.isNotEmpty(o)) {
                flux = flux.filter(Restrictions.value().equal(o));
            }
        }
        flux = flux.drop(new String[]{"_measurement", "_field", "isArray", "length", "type", "_start", "_stop"})
                .groupBy(new String[]{"dataType"});
        if (isCount) {
            flux = flux.count("eventName");
        }else {
            flux = flux.sort(new String[]{"_time","eventName","propertyAlias"},true).limit(Math.toIntExact(page.getSize()), Math.toIntExact(page.getSize() * (page.getCurrent() - 1)));
        }
        return flux.toString();
    }


    @Override
    public Result<List<PropertyLogBo>> propertiesLog(PropertyLogConditionBo condition, TenantIsolation tenantIsolation) {
        QueryApi queryApi = influxDBClient.getQueryApi();
        String countScrip = buildPropertyLogFluxScrip(condition, tenantIsolation);
        log.info("InfluxDB查询：{}",countScrip);
        List<PropertyLogBo> list;
        try {
            list = queryApi.query(countScrip,PropertyLogBo.class);
        } catch (Exception e) {
            log.error("设备属性查询报错，ERROR:{}",e.getMessage());
            return Result.error("设备属性查询报错:"+ e.getMessage());
        }
        Map<Long,String> deviceMap = new HashMap<>();
        Result<List<DeviceEntity>> listResult = deviceService.listDeviceByIds(tenantIsolation.getTenantId(), condition.getDeviceIds(),DeviceEntity::getId, DeviceEntity::getName);
        if (listResult.getSignal()) {
            deviceMap = listResult.getResult().stream().collect(Collectors.toMap(DeviceEntity::getId, DeviceEntity::getName));
        }
        Map<Long, String> finalDeviceMap = deviceMap;
        list.forEach(propertyLogBo -> propertyLogBo.setDeviceName(finalDeviceMap.get(Long.valueOf(propertyLogBo.getDeviceId()))));
        list = list.stream().sorted(comparing(PropertyLogBo::getTime)).collect(Collectors.toList());
        if (list.size() > condition.getNumb()) {
            list = list.subList(0,condition.getNumb());
        }
        return Result.ok(list);
    }

    @Override
    public Result<List<DeviceSelectBo>> selectDevice(TenantIsolation tenantIsolation) {
//        QueryApi queryApi = influxDBClient.getQueryApi();
//        String fluxScrip = buildTagFluxScrip( tenantIsolation,null, "deviceId");
//        log.info("InfluxDB查询：{}",fluxScrip);
//        List<PropertyLogBo> list;
//        try {
//            list = queryApi.query(fluxScrip,PropertyLogBo.class);
//        } catch (Exception e) {
//            log.error("Influx设备查询报错，ERROR:{}",e.getMessage());
//            return Result.error("Influx设备查询报错:"+ e.getMessage());
//        }
//        List<Long> deviceIds = BeanUtilsIntensifier.getSomething(list, PropertyLogBo::getValue).stream().map(value -> Long.valueOf(value.toString())).collect(Collectors.toList());
        Result<List<DeviceEntity>> listResult = deviceService.listDeviceByIds(tenantIsolation.getTenantId(), null, DeviceEntity::getId, DeviceEntity::getName);
        if (listResult.getSignal()) {
            return Result.ok(BeanUtilsIntensifier.copyBeanList(listResult.getResult(), DeviceSelectBo.class));
        }
        return Result.ok(new ArrayList<>());
    }

    private String buildTagFluxScrip(TenantIsolation tenantIsolation, List<Long> deviceIds, String tag) {
        Flux flux = Flux.from(bucket).range(0L);
        Restrictions restriction = Restrictions.and(
                Restrictions.measurement().equal(tenantIsolation.getTenantId().toString()) //改为分租户存储
        );
        flux = flux.filter(restriction);
        if (CollectionUtil.isNotEmpty(deviceIds)){
            List<Restrictions> conditions = new ArrayList<>();
            deviceIds.forEach(deviceId -> conditions.add(Restrictions.tag("deviceId").equal(deviceId.toString())));
            flux = flux.filter(Restrictions.or(conditions.toArray(new Restrictions[0])));
        }
        List<String> tags = new ArrayList<>();
        tags.add(tag);
        flux = flux.keep(tags).group().distinct(tag).sort();
        return flux.toString();

    }

    @Override
    public Result<Set<Object>> getPropertiesByDeviceIds(TenantIsolation tenantIsolation, PropertyLogConditionBo condition) {
        if (ObjectUtil.isEmpty(condition) || CollectionUtil.isEmpty(condition.getDeviceIds())) {
            return Result.ok();
        }
        Set<Object> set = new HashSet<>();
        List<Long> deviceIds = condition.getDeviceIds();
        int size = deviceIds.size();
        int count = size / 125;
        int remainder = size % 125;
        for (int i = 0; i < count; i++) {
            List<Long> subList = deviceIds.subList(i * 125, (i + 1) * 125);
            doGetPropertiesByDeviceIds(tenantIsolation,subList,set);
        }
        if (remainder > 0) {
            List<Long> subList = deviceIds.subList(count * 125, size);
            doGetPropertiesByDeviceIds(tenantIsolation, subList, set);
        }
        return Result.ok(set);
    }

    private void doGetPropertiesByDeviceIds(TenantIsolation tenantIsolation, List<Long> deviceIds, Set<Object> set) {
        QueryApi queryApi = influxDBClient.getQueryApi();
        String fluxScrip = buildTagFluxScrip(tenantIsolation,deviceIds,"property");
        log.info("InfluxDB查询：{}",fluxScrip);
        List<PropertyLogBo> list ;
        try {
            list = queryApi.query(fluxScrip,PropertyLogBo.class);
        } catch (Exception e) {
            log.error("Influx设备查询报错，ERROR:{}",e.getMessage());
            throw new BizException(Result.error("Influx设备查询报错:"+ e.getMessage()));
        }
        List<Object> something = BeanUtilsIntensifier.getSomething(list, PropertyLogBo::getValue);
        set.addAll(something);
    }

    private String buildPropertyLogFluxScrip(PropertyLogConditionBo condition, TenantIsolation tenantIsolation) {
        Flux flux = Flux.from(bucket);
        Long nowSecond = Instant.now().getEpochSecond();
        if (ObjectUtil.isNotEmpty(condition.getStart()) && nowSecond - condition.getStart() > 0) {
            flux = flux.range(condition.getStart());
        }else {
            //默认是一个小时前
            Long anHourAgo = nowSecond - (60 * 60);
            flux = flux.range(anHourAgo);
        }
        Restrictions restriction = Restrictions.and(
                Restrictions.measurement().equal(tenantIsolation.getTenantId().toString()),
                Restrictions.tag("propertyAlias").exists()
        );
        flux = flux.filter(restriction);
        if (CollectionUtil.isNotEmpty(condition.getDeviceIds())){
            List<Restrictions> conditions = new ArrayList<>();
            condition.getDeviceIds().forEach(deviceId -> {
                conditions.add(Restrictions.tag("deviceId").equal(deviceId.toString()));
            });
            flux = flux.filter(Restrictions.or(conditions.toArray(new Restrictions[0])));
        }
        if (CollectionUtil.isNotEmpty(condition.getProperties())) {
            List<Restrictions> conditions = new ArrayList<>();
            condition.getProperties().forEach(property -> {
                conditions.add(Restrictions.tag("property").equal(property));
            });
            flux = flux.filter(Restrictions.or(conditions.toArray(new Restrictions[0])));
        }
        if (ObjectUtil.isNotEmpty(condition.getValue())) {
            List<Restrictions> conditions = new ArrayList<>();
            try {
                Long l = Long.valueOf(condition.getValue());
                conditions.add(Restrictions.value().equal(l));
            } catch (NumberFormatException e) {
            }
            try {
                Double d = Double.valueOf(condition.getValue());
                conditions.add(Restrictions.value().equal(d));
            } catch (NumberFormatException e) {
            }
            if("true".equals(condition.getValue()) || "True".equals(condition.getValue()) || "TRUE".equals(condition.getValue()) || "1".equals(condition.getValue())){
                conditions.add(Restrictions.value().equal(Boolean.TRUE));
            }
            if ("false".equals(condition.getValue()) || "False".equals(condition.getValue()) || "FALSE".equals(condition.getValue()) || "0".equals(condition.getValue())) {
                conditions.add(Restrictions.value().equal(Boolean.FALSE));
            }
            conditions.add(Restrictions.value().equal(condition.getValue()));
            flux = flux.filter(Restrictions.or(conditions.toArray(new Restrictions[0])));
        }
        //将改变上报的前值去掉
        flux = flux.filter(Restrictions.field().notEqual(Restrictions.tag("dataType").custom(".preValue","+")));
        flux = flux.drop(new String[]{"_measurement", "_field", "isArray", "length", "type", "_start", "_stop"})
                .groupBy(new String[]{"dataType"}).sort(new String[]{"_time","propertyAlias"});
        if (ObjectUtil.isEmpty(condition.getNumb())) {
            condition.setNumb(200);
        }
        flux = flux.limit(condition.getNumb(), 0);
        return flux.toString();
    }

    private Object dataConversion(String dataType, String value) {
        ThingDataTypeEnum thingDataTypeEnum = ThingDataTypeEnum.typeOfName(dataType);
        Object o = null;
        try {
            switch (thingDataTypeEnum) {
                case CHAR:case BYTE:case SHORT:case WORD:case LONG:case DWORD:case LLONG:case QWORD:case BCD:case LBCD:
                    return Long.valueOf(value);
                case FLOAT:case DOUBLE:
                    return Double.valueOf(value);
                case BOOLEAN:
                    return Boolean.valueOf(value);
                case STRING:
                    return value;
                default:
                    return null;
            }
        }catch (Exception e){
            log.warn("设备属性日志查询错误，数据类型：{}，值：{}",dataType,value);
        }
        return o;
    }

    @Override
    public Result<Page<EventTriggerLogBo>> pageEventTriggerLog(Page<EventTriggerLogBo> page, EventTriggerLogBo eventTriggerLogBo, TenantIsolation tenantIsolation) {
        QueryApi queryApi = influxDBClient.getQueryApi();
        String queryCountScrip = buildEventTriggerLogFluxQueryScrip(page, eventTriggerLogBo, tenantIsolation, true);
        log.info("InfluxDB查询：{}",queryCountScrip);
        List<FluxTable> queryCount = queryApi.query(queryCountScrip);
        if (dealPage(page,queryCount)) {
            return Result.ok(page);
        }
        String queryScrip = buildEventTriggerLogFluxQueryScrip(page, eventTriggerLogBo, tenantIsolation,false);
        log.info("InfluxDB查询：{}",queryScrip);
        List<EventTriggerLogBo> list = new ArrayList<>();
        List<FluxTable> query = queryApi.query(queryScrip);
        query.forEach(fluxTable -> {
            List<FluxRecord> records = fluxTable.getRecords();
            records.forEach(fluxRecord -> list.add(fluxRecord2EventTriggerLogBo(fluxRecord)));
        });
        page.setRecords(list);
        return Result.ok(page);
    }

    private boolean dealPage(Page page, List<FluxTable> queryCount) {
        if (queryCount.size() > 1) {
            throw new BizException(ServiceCodeEnum.CODE_PARAM_ERROR);
        }else if(queryCount.size() < 1) {
            return true;
        }
        FluxTable fluxCountTable = queryCount.get(0);
        FluxRecord fluxCountRecord = fluxCountTable.getRecords().get(0);
        Integer count = Math.toIntExact((Long) fluxCountRecord.getValueByKey("eventName"));
        if (count.compareTo(0) <= 0) {
            page.setTotal(0);
            return true;
        }
        Integer start = Long.valueOf((page.getCurrent() - 1) * page.getSize()).intValue();
        if (count.compareTo(start) <= 0) {
            long last = count / page.getSize();
            if (count % page.getSize() == 0) {
                page.setCurrent(last);
            }else {
                page.setCurrent(last + 1);
            }
        }
        page.setTotal(count);
        return false;
    }

    private EventTriggerLogBo fluxRecord2EventTriggerLogBo(FluxRecord fluxRecord) {
        Map<String, Object> values = fluxRecord.getValues();
        EventTriggerLogBo build = EventTriggerLogBo.builder()
                .deviceId((String) values.get("deviceId"))
                .eventName((String) values.get("eventName"))
                .time(fluxRecord.getTime())
                .build();
        values.remove("result");
        values.remove("_time");
        values.remove("deviceId");
        values.remove("eventName");
        values.remove("table");
        values.entrySet().removeIf(next -> ObjectUtil.isEmpty(next.getValue()));
        build.setReportProperties(buildPropertiesBo(values));
        return build;
    }

    private List<PropertyVo> buildPropertiesBo(Map<String, Object> values) {
        List<PropertyVo> list = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(values)) {
            values.forEach((k,v) -> list.add(PropertyVo.builder().name(k).value(v).build()));
        }
        return list;
    }

    public String buildEventTriggerLogFluxQueryScrip(Page<EventTriggerLogBo> page, EventTriggerLogBo eventTriggerLogBo, TenantIsolation tenantIsolation,boolean isCount) {
        Flux flux = Flux.from(bucket);
        if (ObjectUtil.isNotEmpty(eventTriggerLogBo.getStart()) && ObjectUtil.isNotEmpty(eventTriggerLogBo.getStop())) {
            flux = flux.range(eventTriggerLogBo.getStart(),eventTriggerLogBo.getStop());
        }else {
            flux = flux.range(0L);
        }
        Restrictions restriction = Restrictions.and(
                Restrictions.measurement().equal(tenantIsolation.getTenantId().toString()), //改为分租户存储
//                Restrictions.measurement().equal("nti"), //当前只在nti里存储
                Restrictions.or(Restrictions.tag("type").equal("trigger"),Restrictions.tag("type").equal("fault")),
                Restrictions.tag("deviceId").equal(eventTriggerLogBo.getDeviceId()),
                Restrictions.tag("propertyAlias").exists()
        );
        if (ObjectUtil.isNotEmpty(eventTriggerLogBo.getEventName())) {
            restriction = Restrictions.and(restriction,Restrictions.tag("eventName").equal(eventTriggerLogBo.getEventName()));
        }
        flux = flux.filter(restriction)
                .drop(new String[]{"_measurement", "_field", "dataType", "isArray", "property", "length", "type", "_start", "_stop"})
                .pivot(new String[]{"_time", "deviceId", "eventName"}, new String[]{"propertyAlias"},"_value");
        if (isCount) {
            flux = flux.groupBy("deviceId").count("eventName");
        }else {
            flux = flux.groupBy("deviceId");
            flux = flux.sort(new String[]{"_time"},true).limit(Long.valueOf(page.getSize()).intValue(),Long.valueOf(page.getSize() * (page.getCurrent() - 1)).intValue());
        }
        return flux.toString();
    }

    @Override
    public Result<Page<DeviceServiceLogEntity>> pageServiceCallLogBo(Page<DeviceServiceLogEntity> page, ServiceCallLogRequestBo requestBo, TenantIsolation tenantIsolation) {

        //查询前先保存日志
        SetOperations<String,DeviceServiceLogEntity> setOperations = redisTemplate.opsForSet();
        Long size = setOperations.size(RedisConstant.DEVICE_SERVICE_LOG);
        List<DeviceServiceLogEntity> list = setOperations.pop(RedisConstant.DEVICE_SERVICE_LOG,size);
        if(CollectionUtil.isNotEmpty(list)){
            List<DeviceServiceLogEntity> saveList = new ArrayList<>();
            saveList.addAll(list);
            deviceServiceLogMapper.saveLogs(saveList);
        }
        LambdaQueryWrapper<DeviceServiceLogEntity> wrapper = new LambdaQueryWrapper<DeviceServiceLogEntity>()
                .or(ObjectUtil.isNotEmpty(requestBo.getServiceId()) || ObjectUtil.isNotEmpty(requestBo.getServiceName()),w ->
                        w.eq(ObjectUtil.isNotEmpty(requestBo.getServiceId()), DeviceServiceLogEntity::getServiceId, requestBo.getServiceId())
                         .or()
                        .eq(ObjectUtil.isNotEmpty(requestBo.getServiceName()), DeviceServiceLogEntity::getServiceName, requestBo.getServiceName())
                )
                .eq(ObjectUtil.isNotEmpty(requestBo.getDeviceId()), DeviceServiceLogEntity::getDeviceId, requestBo.getDeviceId())
                .like(ObjectUtil.isNotEmpty(requestBo.getDeviceName()), DeviceServiceLogEntity::getDeviceName, requestBo.getDeviceName())
                .eq(DeviceServiceLogEntity::getTenantId,tenantIsolation.getTenantId())
                .orderByDesc(DeviceServiceLogEntity::getCreateTime);
        if (ObjectUtil.isNotEmpty(requestBo.getStart()) && ObjectUtil.isNotEmpty(requestBo.getStop())) {
            wrapper.between(DeviceServiceLogEntity::getCreateTime, Instant.ofEpochSecond(requestBo.getStart()), Instant.ofEpochSecond(requestBo.getStop()));
        }
        Page<DeviceServiceLogEntity> selectPage = deviceServiceLogMapper.selectPage(page, wrapper);
        return Result.ok(selectPage);
    }

    private <B> void instantTime2LocalDateTime(List<B> list, Function<B, Instant> getTime, BiConsumer<B, LocalDateTime> setLocalDateTime) {
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(b -> setLocalDateTime.accept(b,LocalDateTime.ofInstant(getTime.apply(b),ZoneId.systemDefault())));
        }
    }

    @Override
    public Result<Void> jobHandler() {
        LocalDateTime now = LocalDateTime.now();
        LambdaUpdateWrapper<DeviceServiceLogEntity> wrapper = new LambdaUpdateWrapper<DeviceServiceLogEntity>()
                .between(DeviceServiceLogEntity::getCreateTime,LocalDateTime.of(2000,1,1,0,0),now.minusDays(logReserveDays));
        deviceServiceLogMapper.delete(wrapper);
        return Result.ok();
    }

    @Async
    @Override
    public void addThingServiceDailyColled(Long tenantId) {
        stringRedisTemplate.opsForValue().increment(String.format(RedisConstant.THING_SERVICE_DAILY_CALLED_COUNT_PREFIX, tenantId,
        DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN)));
    }

    @Async
    @Override
    public void insertLog(DeviceServiceLogEntity logEntity) {
        deviceServiceLogMapper.insert(logEntity);
    }

    
}
