package com.nti56.nlink.product.device.server.feign;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.device.AccessElm;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.model.ConnectResult;
import com.nti56.nlink.product.device.server.model.edgegateway.WriteParam;

import java.util.List;

public interface IEdgeGatewaySpiProxy {

    /**
     * 只传labelList的labelPath和channelId
     * @return Result的ok=true，表示可以进入data获取具体信息
     *         Result的ok=false，表示整个标签的通道都连接失败，不需要进入data
     */
    Result<List<ConnectResult>> connectLabel(Long edgeGatewayId,
                                Long tenantId,
                                List<AccessElm> labelList,
                                List<ChannelElm> channelList);

    /**
     * @return Result的ok=true，表示可以进入data获取具体信息
     *         Result的ok=false，表示整个标签的通道都连接失败，不需要进入data
     */
    Result<List<ConnectResult>> connectChannel(Long edgeGatewayId,
                                Long tenantId,
                                List<ChannelElm> channelList);

    Result<Void> writeBool(Long edgeGatewayId,
                              Long tenantId,
                              AccessElm access,
                              Boolean value);

    Result<Void> writeByte(Long edgeGatewayId,
                              Long tenantId,
                              AccessElm access,
                              Byte value);

    Result<Void> writeShort(Long edgeGatewayId,
                               Long tenantId,
                               AccessElm access,
                               Short value);

    Result<Void> writeInt(Long edgeGatewayId,
                             Long tenantId,
                             AccessElm access,
                             Integer value);

    Result<Void> writeLong(Long edgeGatewayId,
                            Long tenantId,
                            AccessElm access,
                            Long value);

    Result<Void> writeFloat(Long edgeGatewayId,
                               Long tenantId,
                               AccessElm access,
                               Float value);

    Result<Void> writeDouble(Long edgeGatewayId,
                            Long tenantId,
                            AccessElm access,
                            Double value);

    Result<Void> writeString(Long edgeGatewayId,
                                Long tenantId,
                                AccessElm access,
                                String value);

    Result<Void> writeBoolArray(Long edgeGatewayId,
                                   Long tenantId,
                                   AccessElm access,
                                   Boolean[] value);

    Result<Void> writeShortArray(Long edgeGatewayId,
                                    Long tenantId,
                                    AccessElm access,
                                    Short[] value);

    Result<Void> writeIntArray(Long edgeGatewayId,
                                  Long tenantId,
                                  AccessElm access,
                                  Integer[] value);

    Result<Void> writeLongArray(Long edgeGatewayId,
                                Long tenantId,
                                AccessElm access,
                                Long[] value);

    Result<Void> writeFloatArray(Long edgeGatewayId,
                                    Long tenantId,
                                    AccessElm access,
                                    Float[] value);

    Result<Void> writeDoubleArray(Long edgeGatewayId,
                                 Long tenantId,
                                 AccessElm access,
                                 Double[] value);

    Result<Void> writeStringArray(Long edgeGatewayId,
                                     Long tenantId,
                                     AccessElm access,
                                     String[] value);

    Result<Void> writeByteArray(Long edgeGatewayId,
                                   Long tenantId,
                                   AccessElm access,
                                   byte[] value);

    Result<List<ConnectResult>> multiWrite(Long edgeGatewayId,
                               Long tenantId,
                               List<WriteParam> params);
}
