package com.nti56.nlink.product.device.server.feign;

import com.nti56.nlink.common.util.Result;

import java.util.Map;

public interface IEdgeGatewayCustomProxy {

    /**
     * 自定义协议序列化
     */
    Result<String> customDriverSerialize(Long edgeGatewayId,
                                        Long tenantId,
                                        String customDriverName,
                                        String messageName,
                                        Map<String, Object> var);
    
    /**
     * 自定义协议反序列化
     */
    Result<Map<String, Object>> customDriverDeserialize(Long edgeGatewayId,
                                        Long tenantId,
                                        String customDriverName,
                                        String messageName,
                                        String str);
}
