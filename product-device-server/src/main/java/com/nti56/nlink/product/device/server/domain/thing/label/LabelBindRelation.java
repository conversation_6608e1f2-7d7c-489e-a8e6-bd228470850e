package com.nti56.nlink.product.device.server.domain.thing.label;

import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Property;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.model.device.dto.DeviceCheckInfoContext;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 类说明: 标签绑定领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-09-19 16:38:53
 * @since JDK 1.8
 */
@Getter
public class LabelBindRelation {

    private Long id;
    private Long labelId;
    private Long deviceId;
    private String propertyName;
    private String labelName;
    private String channelName;
    private String labelGroupName;
    private Long edgeGatewayId;

    private Long directlyModelId;

    private Label label;
    private LabelBindRelationEntity row;
    private final UniqueConstraint channelUniqueConstraint = new UniqueConstraint("name", "edge_gateway_id");
    private final UniqueConstraint groupUniqueConstraint = new UniqueConstraint("name", "channel_id");
    private final UniqueConstraint labelUniqueConstraint = new UniqueConstraint("name", "label_group_id");



    public static Result<LabelBindRelation> checkInfoToLabel(LabelBindRelationEntity entity, CommonFetcher commonFetcher){
        Result<LabelBindRelation> baseResult = checkBaseInfo(entity, commonFetcher);
        if(!baseResult.getSignal()){
            return baseResult;
        }

        LabelBindRelation labelBindRelation = baseResult.getResult();
        LabelEntity labelEntity = null;
        if (!ObjectUtils.isEmpty(labelBindRelation.labelId)) {
            labelEntity = commonFetcher.get(labelBindRelation.labelId, LabelEntity.class);
        }
        if (ObjectUtils.isEmpty(labelEntity)) {
            if (ObjectUtils.isEmpty(entity.getEdgeGatewayId()) || ObjectUtils.isEmpty(entity.getChannelName()) || ObjectUtils.isEmpty(entity.getLabelGroupName()) || ObjectUtils.isEmpty(entity.getLabelName())) {
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_NULL);
            }
            UniqueConstraint.Unique unique = labelBindRelation.channelUniqueConstraint.buildUnique(new FieldValue(entity.getChannelName()), new FieldValue(entity.getEdgeGatewayId()));
            ChannelEntity channelEntity = commonFetcher.get(unique, ChannelEntity.class);
            if (ObjectUtils.isEmpty(channelEntity)) {
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_NULL);
            }
            UniqueConstraint.Unique groupUnique = labelBindRelation.groupUniqueConstraint.buildUnique(new FieldValue(entity.getLabelGroupName()), new FieldValue(channelEntity.getId()));
            LabelGroupEntity labelGroupEntity = commonFetcher.get(groupUnique, LabelGroupEntity.class);
            if (ObjectUtils.isEmpty(labelGroupEntity)) {
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_NULL);
            }
            UniqueConstraint.Unique labelUnique = labelBindRelation.labelUniqueConstraint.buildUnique(new FieldValue(entity.getLabelName()), new FieldValue(labelGroupEntity.getId()));
            labelEntity = commonFetcher.get(labelUnique, LabelEntity.class);
            if (ObjectUtils.isEmpty(labelEntity)) {
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_NULL);
            }
        }


        Result<Label> labelResult = Label.checkInfoToEdgeGateway(labelEntity, commonFetcher);
        if(!labelResult.getSignal()){
            return Result.error(labelResult.getMessage());
        }

        labelBindRelation.label = labelResult.getResult();
        return Result.ok(labelBindRelation);
    }

    public static Result<LabelBindRelation> checkInfoToLabel2(LabelBindRelationEntity entity, DeviceCheckInfoContext context){
        Result<LabelBindRelation> baseResult = checkBaseInfo(entity, null);
        if(!baseResult.getSignal()){
            return baseResult;
        }

        LabelBindRelation labelBindRelation = baseResult.getResult();
        LabelEntity labelEntity = null;
        if (!ObjectUtils.isEmpty(labelBindRelation.labelId)) {
            labelEntity = context.getLabelEntityByLabelId(labelBindRelation.labelId);
        }
        if (ObjectUtils.isEmpty(labelEntity)) {
            if (ObjectUtils.isEmpty(entity.getEdgeGatewayId()) || ObjectUtils.isEmpty(entity.getChannelName()) || ObjectUtils.isEmpty(entity.getLabelGroupName()) || ObjectUtils.isEmpty(entity.getLabelName())) {
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_NULL);
            }
            // UniqueConstraint.Unique unique = labelBindRelation.channelUniqueConstraint.buildUnique(new FieldValue(entity.getChannelName()), new FieldValue(entity.getEdgeGatewayId()));
            // ChannelEntity channelEntity = commonFetcher.get(unique, ChannelEntity.class);
            ChannelEntity channelEntity=context.getChannelEntityByGwId(entity.getEdgeGatewayId(),entity.getChannelName());
            if (ObjectUtils.isEmpty(channelEntity)) {
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_NULL);
            }
            // UniqueConstraint.Unique groupUnique = labelBindRelation.groupUniqueConstraint.buildUnique(new FieldValue(entity.getLabelGroupName()), new FieldValue(channelEntity.getId()));
            // LabelGroupEntity labelGroupEntity = commonFetcher.get(groupUnique, LabelGroupEntity.class);
            LabelGroupEntity labelGroupEntity =context.getLabelGroupEntityByChannelId(channelEntity.getId(),entity.getLabelGroupName());
            if (ObjectUtils.isEmpty(labelGroupEntity)) {
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_NULL);
            }
            // UniqueConstraint.Unique labelUnique = labelBindRelation.labelUniqueConstraint.buildUnique(new FieldValue(entity.getLabelName()), new FieldValue(labelGroupEntity.getId()));
            // labelEntity = commonFetcher.get(labelUnique, LabelEntity.class);
            labelEntity=context.getLabelEntityByLabelGroupId(labelGroupEntity.getId(),entity.getLabelName());
            if (ObjectUtils.isEmpty(labelEntity)) {
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_NULL);
            }
        }


        Result<Label> labelResult = Label.checkInfoToEdgeGateway2(labelEntity, context);
        if(!labelResult.getSignal()){
            return Result.error(labelResult.getMessage());
        }

        labelBindRelation.label = labelResult.getResult();
        return Result.ok(labelBindRelation);
    }

    private static Result<LabelBindRelation> checkBaseInfo(LabelBindRelationEntity entity, CommonFetcher commonFetcher){
        
        if(entity == null){
            return Result.error("entity不能位空");
        }

        LabelBindRelation labelBindRelation = new LabelBindRelation();
        labelBindRelation.row = entity;
        Long id = entity.getId();
        if(id == null){
            return Result.error("id不能位空");
        }
        labelBindRelation.id = id;
        
        Long deviceId = entity.getDeviceId();
        if(deviceId == null){
            return Result.error("deviceId不能位空");
        }
        labelBindRelation.deviceId = deviceId;

        Long directlyModelId = entity.getDirectlyModelId();
        if(directlyModelId == null){
            return Result.error("directlyModelId不能位空");
        }
        labelBindRelation.directlyModelId = directlyModelId;

        String propertyName = entity.getPropertyName();
        if(propertyName == null){
            return Result.error("propertyName不能位空");
        }
        labelBindRelation.propertyName = propertyName;
        labelBindRelation.labelName = entity.getLabelName();
        labelBindRelation.labelGroupName = entity.getLabelGroupName();
        labelBindRelation.channelName = entity.getChannelName();
        labelBindRelation.edgeGatewayId = entity.getEdgeGatewayId();
        Long labelId = entity.getLabelId();
        labelBindRelation.labelId = labelId;
        return Result.ok(labelBindRelation);
    }

    public static List<LabelBindRelation> bindByGroup(Set<String> propertyNames, List<Property> properties, List<LabelEntity> labelList, String groupName, ChannelEntity channel) {
        List<LabelBindRelation> result = new ArrayList<>();
        Map<String, LabelEntity> labelMap = BeanUtilsIntensifier.collection2Map(labelList, LabelEntity::getName);
        Map<String, Property> propertyMap = BeanUtilsIntensifier.collection2Map(properties, Property::getName);
        propertyNames.forEach(propertyName -> {
            if (labelMap.containsKey(propertyName)) {
                LabelEntity labelEntity = labelMap.get(propertyName);
                Property property = propertyMap.get(propertyName);
                LabelBindRelation relation = build(labelEntity,property,groupName,channel);
                result.add(relation);
            }
        });
        return result;
    }

    private static LabelBindRelation build(LabelEntity labelEntity, Property property, String groupName,ChannelEntity channel) {
        LabelBindRelation relation = new LabelBindRelation();
        relation.deviceId = property.getDeviceId();
        relation.propertyName = property.getName();
        relation.labelId = labelEntity.getId();
        relation.labelName = labelEntity.getName();
        relation.channelName = channel.getName();
        relation.edgeGatewayId = channel.getEdgeGatewayId();
        relation.labelGroupName = groupName;
        relation.directlyModelId = property.getDirectlyModelId();
        relation.label = Label.build(labelEntity,channel);
        return relation;
    }


    public static Result<LabelBindRelation> checkInfoToLabelNew(LabelBindRelationEntity entity,
                                                                List<LabelEntity> labelEntityList,
                                                                List<ChannelEntity> channelEntitieList,
                                                                List<LabelGroupEntity> labelGroupEntityList,
                                                                Map<Long,EdgeGatewayEntity> edgeGatewayEntityMap
                                                                ){
        Result<LabelBindRelation> baseResult = checkBaseInfo(entity, null);
        if(!baseResult.getSignal()){
            return baseResult;
        }

        LabelBindRelation labelBindRelation = baseResult.getResult();
        LabelEntity labelEntity = null;
        if (!ObjectUtils.isEmpty(labelBindRelation.labelId)) {
            Map<Long,LabelEntity> labelEntityMap = labelEntityList.stream().collect(Collectors.toMap(
                    LabelEntity::getId,
                    Function.identity()
            ));
            labelEntity = labelEntityMap.get(labelBindRelation.labelId);
        }
        if (ObjectUtils.isEmpty(labelEntity)) {
            if (ObjectUtils.isEmpty(entity.getEdgeGatewayId()) || ObjectUtils.isEmpty(entity.getChannelName()) || ObjectUtils.isEmpty(entity.getLabelGroupName()) || ObjectUtils.isEmpty(entity.getLabelName())) {
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_NULL);
            }
            Map<String, ChannelEntity> channelEntityMap = channelEntitieList.stream().collect(Collectors.toMap(
                    f -> f.getName() + ":" + f.getEdgeGatewayId(),
                    Function.identity(),
                    (existing, replacement) -> replacement  // 如果出现重复键，使用后来的值替换已有的值
            ));
//            UniqueConstraint.Unique unique = labelBindRelation.channelUniqueConstraint.buildUnique(new FieldValue(entity.getChannelName()), new FieldValue(entity.getEdgeGatewayId()));
//            ChannelEntity channelEntity = commonFetcher.get(unique, ChannelEntity.class);
            ChannelEntity channelEntity = channelEntityMap.get(entity.getChannelName() + ":" + entity.getEdgeGatewayId());
            if (ObjectUtils.isEmpty(channelEntity)) {
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_NULL);
            }
            Map<String, LabelGroupEntity> labelGroupEntityMap = labelGroupEntityList.stream().collect(Collectors.toMap(
                    f -> f.getName() + ":" + f.getChannelId(),
                    Function.identity(),
                    (existing, replacement) -> replacement  // 如果出现重复键，使用后来的值替换已有的值
            ));
//            UniqueConstraint.Unique groupUnique = labelBindRelation.groupUniqueConstraint.buildUnique(new FieldValue(entity.getLabelGroupName()), new FieldValue(channelEntity.getId()));
//            LabelGroupEntity labelGroupEntity = commonFetcher.get(groupUnique, LabelGroupEntity.class);
            LabelGroupEntity labelGroupEntity = labelGroupEntityMap.get(entity.getLabelGroupName() + ":" + channelEntity.getId());
            if (ObjectUtils.isEmpty(labelGroupEntity)) {
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_NULL);
            }
            Map<String, LabelEntity> labelMap = labelEntityList.stream().collect(Collectors.toMap(
                    f -> f.getName() + ":" + f.getLabelGroupId(),
                    Function.identity(),
                    (existing, replacement) -> replacement  // 如果出现重复键，使用后来的值替换已有的值
            ));
//            UniqueConstraint.Unique labelUnique = labelBindRelation.labelUniqueConstraint.buildUnique(new FieldValue(entity.getLabelName()), new FieldValue(labelGroupEntity.getId()));
//            labelEntity = commonFetcher.get(labelUnique, LabelEntity.class);
            labelEntity = labelMap.get(entity.getLabelName() + ":" + labelGroupEntity.getId());
            if (ObjectUtils.isEmpty(labelEntity)) {
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_NULL);
            }
        }
        Map<Long, ChannelEntity> channelMap = channelEntitieList.stream().collect(Collectors.toMap(
                ChannelEntity::getId,
                Function.identity(),
                (existing, replacement) -> replacement  // 如果出现重复键，使用后来的值替换已有的值
        ));
        Map<Long, LabelGroupEntity> groupEntityMap = labelGroupEntityList.stream().collect(Collectors.toMap(
                LabelGroupEntity::getId,
                Function.identity(),
                (existing, replacement) -> replacement  // 如果出现重复键，使用后来的值替换已有的值
        ));
        Result<Label> labelResult = Label.checkInfoToEdgeGatewayNew(labelEntity,channelMap,edgeGatewayEntityMap,groupEntityMap);
        if(!labelResult.getSignal()){
            return Result.error(labelResult.getMessage());
        }

        labelBindRelation.label = labelResult.getResult();
        return Result.ok(labelBindRelation);
    }
}
