package com.nti56.nlink.product.device.server.verticle.post.processor.event;

import com.alibaba.fastjson.JSONArray;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.WriteApiBlocking;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.YesNoEnum;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpEventTopic;
import com.nti56.nlink.product.device.server.domain.thing.up.UpData;
import com.nti56.nlink.product.device.server.domain.thing.up.UpProp;
import com.nti56.nlink.product.device.server.service.cache.InfluxDBBatchCache;
import com.nti56.nlink.product.device.server.verticle.post.processor.PostProcessorHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName UpData2InfluxDBHandler
 * @date 2022/7/26 17:01
 * @Version 1.0
 */
@Component
@Slf4j
public class EventUpData2InfluxDBHandler extends PostProcessorHandler<GwUpEventTopic.TopicInfo> {

    @Autowired
    private InfluxDBClient influxDbClient;


    @Async("propertyConsumerAsyncExecutor")
    @Override
    public void process(GwUpEventTopic.TopicInfo topicInfo, UpData upData){
        super.process(topicInfo,upData);
    }

    @Override
    public void doProcess(GwUpEventTopic.TopicInfo topicInfo, UpData upData) {
        Long start = System.currentTimeMillis();
        try {
            // 使用缓存机制替代直接写入
            addToCache(topicInfo, upData);
        } catch (Exception e) {
            log.error("Error processing event data for tenant:{}, gateway:{}, device:{}", 
                topicInfo.getTenantId(), topicInfo.getEdgeGatewayId(), topicInfo.getDeviceId(), e);
            // 缓存失败时，降级为直接写入
            try {
                WriteApiBlocking writeApi = influxDbClient.getWriteApiBlocking();
                List<Point> pointList = upDataToPoints(topicInfo, upData);
                writeApi.writePoints(pointList);
                log.info("Fallback to direct write for event tenant:{}, gateway:{}, device:{}", 
                    topicInfo.getTenantId(), topicInfo.getEdgeGatewayId(), topicInfo.getDeviceId());
            } catch (Exception ex) {
                log.error("Both cache and direct write failed for event tenant:{}, gateway:{}, device:{}", 
                    topicInfo.getTenantId(), topicInfo.getEdgeGatewayId(), topicInfo.getDeviceId(), ex);
            }
        }
        
        Long spend = System.currentTimeMillis() - start;
        if (spend > 100) {
            log.info("eventUpData2InfluxDBHandler tenant:{}, edgeGwId:{}, deviceId:{}, cost: {}ms", 
                topicInfo.getTenantId(), topicInfo.getEdgeGatewayId(), topicInfo.getDeviceId(), spend);
        }
    }

    private void addToCache(GwUpEventTopic.TopicInfo topicInfo, UpData upData) {
        try {
            List<Point> pointList = upDataToPoints(topicInfo, upData);
            if (!pointList.isEmpty()) {
                boolean success = InfluxDBBatchCache.push(topicInfo, pointList);
                if (!success) {
                    log.warn("Failed to add {} event points to cache for tenant {}, cache may be full", 
                        pointList.size(), topicInfo.getTenantId());
                    throw new RuntimeException("Cache is full");
                }
            }
        } catch (NumberFormatException e) {
            log.error("Invalid tenant ID format: {}", topicInfo.getTenantId(), e);
            throw new RuntimeException("Invalid tenant ID", e);
        }
    }

    private List<Point> upDataToPoints(GwUpEventTopic.TopicInfo topicInfo, UpData upData){
        Long timestamp = upData.getTimestamp();

        List<Point> pointList = new ArrayList<>();
        for(UpProp item:upData.getProp()){
            if(item.getIsArray()){
                String propertyTag;
                for(int i=0;i<item.getLength();i++){
                    Map<String, Object> fields = new HashMap<>();
                    propertyTag = item.getProperty() + "." + i;
                    String fieldKey = item.getDataType() + "." + i;
                    JSONArray value = (JSONArray)item.getValue();
                    Object fieldValue = value.get(i);
                    fields.put(fieldKey, fieldValue);

                    Point point = Point.measurement(topicInfo.getTenantId())
                            .addTag("type", topicInfo.getEventType())
                            .addTag("eventName", topicInfo.getEventName())
                            .addTag("deviceId", topicInfo.getDeviceId())
                            .addTag("property", item.getProperty())
                            .addTag("propertyAlias", propertyTag)
                            .addTag("dataType", item.getDataType())
                            .addTag("isArray", item.getIsArray()?YesNoEnum.YES.getName():YesNoEnum.NO.getName())
//                            .addTag("length", item.getLength().toString())
                            .addFields(fields)
                            .time(timestamp, WritePrecision.MS);

                    if ("fault".equals(topicInfo.getEventType())) {
                        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
                        // 故障状态：true-开始、false-停止
                        point = point.addTag("faultStatus", upData.getFaultStatus()?"开始":"结束");
                        // 故障开始时间
                        if (upData.getFaultStatus()) {
                            point = point.addTag("faultBegin",LocalDateTime.ofInstant(Instant.ofEpochMilli(upData.getFaultBegin()), ZoneId.systemDefault()).format(df));
                            point = point.addTag("faultEnd","-");
                        }else {
                            // 故障结束时间
                            point = point.addTag("faultBegin",LocalDateTime.ofInstant(Instant.ofEpochMilli(upData.getFaultBegin()), ZoneId.systemDefault()).format(df));
                            point = point.addTag("faultEnd",LocalDateTime.ofInstant(Instant.ofEpochMilli(upData.getFaultEnd()), ZoneId.systemDefault()).format(df));
                        }
                    }

                    pointList.add(point);
                }
            }else{
                Map<String, Object> fields = new HashMap<>();

                String fieldKey = item.getDataType();
                Object fieldValue = item.getValue();
                fields.put(fieldKey, fieldValue);

                Point point = Point.measurement(topicInfo.getTenantId())
                        .addTag("type", topicInfo.getEventType())
                        .addTag("eventName", topicInfo.getEventName())
                        .addTag("deviceId", topicInfo.getDeviceId())
                        .addTag("property", item.getProperty())
                        .addTag("propertyAlias", item.getProperty())
                        .addTag("dataType", item.getDataType())
                        .addTag("isArray", item.getIsArray()?YesNoEnum.YES.getName():YesNoEnum.NO.getName())
//                        .addTag("length", item.getLength().toString())
                        .addFields(fields)
                        .time(timestamp, WritePrecision.MS);

                if ("fault".equals(topicInfo.getEventType())) {
                    DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
                    // 故障状态：true-开始、false-停止
                    point = point.addTag("faultStatus", upData.getFaultStatus()?"开始":"结束");
                    // 故障开始时间
                    if (upData.getFaultStatus()) {
                        point = point.addTag("faultBegin",LocalDateTime.ofInstant(Instant.ofEpochMilli(upData.getFaultBegin()), ZoneId.systemDefault()).format(df));
                        point = point.addTag("faultEnd","-");
                    }else {
                        // 故障结束时间
                        point = point.addTag("faultBegin",LocalDateTime.ofInstant(Instant.ofEpochMilli(upData.getFaultBegin()), ZoneId.systemDefault()).format(df));
                        point = point.addTag("faultEnd",LocalDateTime.ofInstant(Instant.ofEpochMilli(upData.getFaultEnd()), ZoneId.systemDefault()).format(df));
                    }
                }
                pointList.add(point);
            }
        }
        return pointList;
    }
}
