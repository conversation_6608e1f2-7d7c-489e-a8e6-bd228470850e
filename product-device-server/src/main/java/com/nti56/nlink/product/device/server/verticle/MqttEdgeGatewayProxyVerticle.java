package com.nti56.nlink.product.device.server.verticle;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static org.springframework.beans.factory.config.ConfigurableBeanFactory.SCOPE_PROTOTYPE;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Random;


import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.MqttTopicEnum;
import com.nti56.nlink.product.device.server.domain.thing.topic.OtTopic;
import com.nti56.nlink.product.device.server.model.edgegateway.EventBusResponse;

import io.netty.handler.codec.mqtt.MqttQoS;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.EventBus;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonObject;
import io.vertx.core.shareddata.LocalMap;
import io.vertx.core.shareddata.SharedData;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

/**
 * 类说明: mqtt网关代理
 * 
 * 代理网关接口，将请求通过mqtt发送给边缘网关，
 * 并用mqtt接收响应数据
 * 
 * 接口支持feign调用
 * 
 * 当ot可以直连网关，则使用feign直接请求
 * 当ot无法直连网关，则使feign请求代理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-04-06 09:59:05
 * @since JDK 1.8
 */
@Component
@Scope(SCOPE_PROTOTYPE)
@Slf4j
public class MqttEdgeGatewayProxyVerticle extends MqttBaseVerticle{

    public static final String REQUESTID_RESPONSE_MAP_LOCAL_MAP_KEY = "REQUESTID_RESPONSE_MAP_LOCAL_MAP_KEY";
    
    // EventBus地址常量
    public static final String EVENT_BUS_NOT_ASSIGN_TOPIC = "mqtt.proxy.notAssign";
    public static final String EVENT_BUS_ASSIGN_TOPIC = "mqtt.proxy.assign";

    private static String balanceId;
    private static String instanceId;

    {
        instanceId = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli()
                            + ":"
                            + new Random().nextInt(100000);
        balanceId = DigestUtils.md5Hex(instanceId);
    }
    
    @Getter
    @Value("${mqtt.host}")
    private String host;

    @Getter
    @Value("${mqtt.port}")
    private Integer port;

    @Getter
    @Value("${mqtt.username}")
    private String username;

    @Getter
    @Value("${mqtt.password}")
    private String password;

    @Getter
    @Value("${mqtt.ssl:true}")
    private Boolean ssl;
    
    @Getter
    @Value("${spring.application.name}")
    private String applicationName;
    
    @Getter
    private Integer reconnectGapTime;
    
    private Integer proxyRequestTimeout = 60_000; //ms

    private LocalMap<Long, EventBusResponse> requestIdResponseMap;

    private long requestIdIncreaser;
    
    private EventBus eventBus;
    
    @Override
    public void start(Promise<Void> startPromise) throws Exception {
        //这里的xxx即为nacos控制面板上的服务名
        log.debug("start-verticle");
        
        log.info("instanceId: {}, balanceId: {}", instanceId, balanceId);

        Promise<Void> superPromise = Promise.promise();
        super.start(superPromise);
        
        SharedData sharedData = vertx.sharedData();
        String deploymentId = deploymentID();

        //初始化请求id响应map
        requestIdResponseMap = sharedData.getLocalMap(REQUESTID_RESPONSE_MAP_LOCAL_MAP_KEY);
        requestIdIncreaser = 0L;

        // 获取EventBus实例
        eventBus = vertx.eventBus();

        // 注册EventBus消费者 - 处理未分配网关请求
        eventBus.consumer(EVENT_BUS_NOT_ASSIGN_TOPIC, this::handleNotAssignRequest);
        
        // 注册EventBus消费者 - 处理已分配网关请求
        eventBus.consumer(EVENT_BUS_ASSIGN_TOPIC, this::handleAssignRequest);
        
        superPromise.future().onComplete(r -> {
            log.info("mqtt edge gateway proxy verticle started successfully");
            startPromise.complete();
        });
    }

    /**
     * 处理未分配网关请求
     */
    private void handleNotAssignRequest(Message<JsonObject> message) {
        JsonObject body = message.body();
        String api = body.getString("api");
        String topicTypeStr = body.getString("topicType");
        String imeiOrHost = body.getString("imeiOrHost");
        String adminPort = body.getString("adminPort");
        String ioType = body.getString("ioType");
        String requestBody = body.getString("body");
        
        log.info("handleNotAssignRequest, api: {}, imeiOrHost: {}, adminPort: {} ", api, imeiOrHost, adminPort);
        MqttTopicEnum topicType = MqttTopicEnum.typeOfType(topicTypeStr);

        if (requestBody != null) {
            final long requestId = requestIdIncreaser++;
            String topic = OtTopic.createNotAssignTopic(
                topicType,
                imeiOrHost,
                adminPort, 
                api,
                requestId,
                balanceId
            );
            
            if (ioType.equals("send")) {
                send(topic, Buffer.buffer(requestBody), requestId, message);
            } else if (ioType.equals("request")) {
                request(topic, Buffer.buffer(requestBody), requestId, message);
            }
        } else {
            log.error("request body should not be null");
            message.reply(Result.error("请求体不能为空"));
        }
    }

    /**
     * 处理已分配网关请求
     */
    private void handleAssignRequest(Message<JsonObject> message) {
        JsonObject body = message.body();
        String edgeGatewayId = body.getString("edgeGatewayId");
        String api = body.getString("api");
        String topicTypeStr = body.getString("topicType");
        String tenantId = body.getString("tenantId");
        String ioType = body.getString("ioType");
        String requestBody = body.getString("body");
        
        log.info("handleAssignRequest, api: {}, tenantId: {}, edgeGatewayId: {} ", api, tenantId, edgeGatewayId);
        MqttTopicEnum topicType = MqttTopicEnum.typeOfType(topicTypeStr);

        if (requestBody != null) {
            final long requestId = requestIdIncreaser++;
            String topic = OtTopic.createAssignTopic(
                topicType,
                tenantId,
                edgeGatewayId, 
                api,
                requestId,
                balanceId
            );
            
            if (ioType.equals("send")) {
                send(topic, Buffer.buffer(requestBody), requestId, message);
            } else if (ioType.equals("request")) {
                request(topic, Buffer.buffer(requestBody), requestId, message);
            }
        } else {
            log.error("request body should not be null");
            message.reply(Result.error("请求体不能为空"));
        }
    }

    private final Result<Void> successResult = Result.ok();
    private final String successStr = JSONObject.toJSONString(successResult);
    private void send(String topic, Buffer bodyBuffer, Long requestId, Message<JsonObject> message) {
        client.publish(topic, bodyBuffer, MqttQoS.AT_LEAST_ONCE, false, false);
        message.reply(successStr);
    }

    private final Result<Void> timeoutResult = Result.error("请求超时");
    private final String timeoutStr = JSONObject.toJSONString(timeoutResult);
    private void request(String topic, Buffer bodyBuffer, Long requestId, Message<JsonObject> message) {
        client.publish(topic, bodyBuffer, MqttQoS.AT_LEAST_ONCE, false, false);
        requestIdResponseMap.put(requestId, new EventBusResponse(message.replyAddress(), requestId.toString()));
        
        //超时处理
        vertx.setTimer(proxyRequestTimeout, id -> {
            EventBusResponse eventBusResponse = requestIdResponseMap.get(requestId);
            if (eventBusResponse != null) {
                log.info("请求超时, topic: {}, requestId: {} ", topic, requestId);
                eventBus.send(eventBusResponse.getReplyAddress(), timeoutStr);
                requestIdResponseMap.remove(requestId);
            }
        });
    }

    @Override
    protected void subscribe() {
        //监听mqtt返回
        String eventTopic = OtTopic.createResponseSubscribeTopic(MqttBaseVerticle.shareSubscribeGroup, balanceId);
        client.publishHandler(s1 -> {
            //返回EventBus响应
            String topicName = s1.topicName();
            String payload = s1.payload().toString();
            log.debug("response topic: {}, msg: {}", topicName, payload);
            OtTopic.TopicInfo topicInfo = OtTopic.parseTopic(topicName);
            
            EventBusResponse eventBusResponse = requestIdResponseMap.get(topicInfo.getRequestId());
            if (eventBusResponse != null) {
                eventBus.send(eventBusResponse.getReplyAddress(), payload);
                requestIdResponseMap.remove(topicInfo.getRequestId());
            }
        });
        client.subscribe(eventTopic, 1);
    }

    @Override
    protected void handleConnectStatusChange(Boolean connected) {
       
    }
}
