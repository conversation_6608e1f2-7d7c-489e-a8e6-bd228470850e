package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.ThingServiceEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 物服务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13 10:28:52
 */
@Mapper
public interface ThingServiceMapper extends CommonMapper<ThingServiceEntity> {

    @Select("SELECT * FROM thing_service WHERE deleted = 0 AND ( tenant_id = #{tenantId} or tenant_id = 999) AND thing_model_id = #{thingModelId}")
    List<ThingServiceEntity> listByThingModelId(@Param("tenantId") Long tenantId, 
                                                @Param("thingModelId") Long thingModelId);

    List<ThingServiceEntity> listByDataModelId(@Param("tenantId") Long tenantId,
                                                @Param("dataModelId") Long dataModelId);

    @Delete("UPDATE thing_service SET deleted = 1 WHERE tenant_id = #{tenantId} AND thing_model_id = #{thingModelId}")
    Integer deleteByThingModelId(@Param("tenantId") Long tenantId,
                                @Param("thingModelId") Long thingModelId);

    List<ThingServiceEntity> listAllCommonThingService(@Param("serviceType") Integer serviceType);
}
