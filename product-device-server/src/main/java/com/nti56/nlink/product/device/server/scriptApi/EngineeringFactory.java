package com.nti56.nlink.product.device.server.scriptApi;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName EngineeringFactory
 * @date 2022/12/9 13:35
 * @Version 1.0
 */
@Component
@Slf4j
public class EngineeringFactory {

    private final ConcurrentHashMap<Long,Engineering> engineeringAssociations = new ConcurrentHashMap<>();

    public Engineering getEngineering(Long tenantId){
        if(tenantId == null){
            return null;
        }
        
        Engineering engineering = this.engineeringAssociations.get(tenantId);
        if (engineering == null) {
            synchronized (this.engineeringAssociations) {
                // 双重检查锁定模式，再次检查是否已经有其他线程创建了Engineering实例
                engineering = this.engineeringAssociations.get(tenantId);
                if (engineering == null) {
                    engineering = new Engineering(tenantId);
                    this.engineeringAssociations.put(tenantId, engineering);
                }
            }
        }
        return engineering;
    }

    public void resetEngineering(Long tenantId) {
        Engineering engineering = this.engineeringAssociations.get(tenantId);
        if(engineering != null){
            engineering.close();
        }
        this.engineeringAssociations.remove(tenantId);
    }
}
