package com.nti56.nlink.product.device.server.service.impl;

import com.google.common.base.Stopwatch;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.*;
import com.nti56.nlink.product.device.client.model.dto.engineering.ProductDeviceServerDataDTO;
import com.nti56.nlink.product.device.server.domain.event.DeleteDataEvent;
import com.nti56.nlink.product.device.server.domain.event.ImportDataEvent;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.ChannelMapper;
import com.nti56.nlink.product.device.server.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/4 9:49<br/>
 * @since JDK 1.8
 */
@Service
@Slf4j
public class EngineeringProductServiceImpl implements IEngineeringProductService {
    @Autowired
    private IChannelService channelService;

    @Autowired
    private IChannelParamService channelParamService;

    @Autowired
    private ITaskService taskService;

    @Autowired
    private IDataModelService dataModelService;

    @Autowired
    private IDataModelPropertyService dataModelPropertyService;

    @Autowired
    @Lazy
    private IDeviceService deviceService;

    @Autowired
    private IDeviceModelInheritService deviceModelInheritService;

    @Autowired
    private IDeviceServiceService deviceServiceService;

    @Autowired
    private IEdgeGatewayService edgeGatewayService;

    @Autowired
    private ILabelBindRelationService labelBindRelationService;

    @Autowired
    private ILabelService labelService;

    @Autowired
    private ILabelGroupService labelGroupService;

    @Autowired
    private IResourceRelationService resourceRelationService;

    @Autowired
    private ISubscriptionService subscriptionService;

    @Autowired
    private ITagBindRelationService tagBindRelationService;

    @Autowired
    private IThingModelService thingModelService;

    @Autowired
    private IThingModelInheritService thingModelInheritService;

    @Autowired
    private IThingServiceService thingServiceService;

    @Autowired
    private ICustomDriverService customDriverService;

    @Autowired
    private ICustomMessageService customMessageService;

    @Autowired
    private ICustomFieldService customFieldService;

    @Autowired
    private IDeviceLogService deviceLogService;

    @Autowired
    private ChannelMapper channelMapper;

    //@Autowired
    //private IModelGraphService modelGraphService;

    @Autowired
    ApplicationContext applicationContext;
    
    @Autowired
    private IConnectorService connectorService;
    
    @Autowired
    private IConnectorItemService connectorItemService;


    @Override
    public Result<Void> deleteProductDataByTenantId(Long tenantId) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("tenant_id", tenantId);
        channelMapper.deleteAllByTenantId(tenantId);
        applicationContext.publishEvent(new DeleteDataEvent(this, tenantId));
        return Result.ok();
    }

    @Override
    public Result<ProductDeviceServerDataDTO> getProductDataByTenantId(Long tenantId) {
        Stopwatch sw = Stopwatch.createStarted();
        HashMap<String, Object> map = new HashMap<>();
        map.put("tenant_id", tenantId);


        CompletableFuture<List<LabelBindRelationEntity>> labelBindRelation = CompletableFuture.supplyAsync(() ->
                labelBindRelationService.listByMap(map));
        CompletableFuture<List<LabelEntity>> label = CompletableFuture.supplyAsync(() ->
                labelService.listByMap(map));

        CompletableFuture<List<LabelGroupEntity>> labelGroup = CompletableFuture.supplyAsync(() ->
                labelGroupService.listByMap(map));

        CompletableFuture<List<ChannelEntity>> channel = CompletableFuture.supplyAsync(() ->
                channelService.listByMap(map));

        CompletableFuture<List<ChannelParamEntity>> channelParam = CompletableFuture.supplyAsync(() ->
                channelParamService.listByMap(map));

//        CompletableFuture<List<ComputeTaskEntity>> task = CompletableFuture.supplyAsync(() ->
//                taskService.listByMap(map));

        CompletableFuture<List<DataModelEntity>> dataModel = CompletableFuture.supplyAsync(() ->
                dataModelService.listByMap(map));

        CompletableFuture<List<DataModelPropertyEntity>> dataModelProperty = CompletableFuture.supplyAsync(() ->
                dataModelPropertyService.listByMap(map));

        CompletableFuture<List<DeviceEntity>> device = CompletableFuture.supplyAsync(() ->
                deviceService.listByMap(map));

        CompletableFuture<List<DeviceModelInheritEntity>> deviceModelInherit = CompletableFuture.supplyAsync(() ->
                deviceModelInheritService.listByMap(map));

        CompletableFuture<List<DeviceServiceEntity>> deviceService = CompletableFuture.supplyAsync(() ->
                deviceServiceService.listByMap(map));

        CompletableFuture<List<EdgeGatewayEntity>> edgeGateway = CompletableFuture.supplyAsync(() ->
                edgeGatewayService.listByMap(map));

        CompletableFuture<List<ResourceRelationEntity>> resourceRelation = CompletableFuture.supplyAsync(() ->
                resourceRelationService.listByMap(map));

        CompletableFuture<List<SubscriptionEntity>> subscription = CompletableFuture.supplyAsync(() ->
                subscriptionService.listByMap(map));

        CompletableFuture<List<TagBindRelationEntity>> tagBindRelation = CompletableFuture.supplyAsync(() ->
                tagBindRelationService.listByMap(map));

        CompletableFuture<List<ThingModelEntity>> thingModel = CompletableFuture.supplyAsync(() ->
                thingModelService.listByMap(map));

        CompletableFuture<List<ThingModelInheritEntity>> thingModelInherit = CompletableFuture.supplyAsync(() ->
                thingModelInheritService.listByMap(map));

        CompletableFuture<List<ThingServiceEntity>> thingService = CompletableFuture.supplyAsync(() ->
                thingServiceService.listByMap(map));

        CompletableFuture<List<CustomDriverEntity>> customDriver = CompletableFuture.supplyAsync(() ->
                customDriverService.listByMap(map));

        CompletableFuture<List<CustomMessageEntity>> customMessage = CompletableFuture.supplyAsync(() ->
                customMessageService.listByMap(map));

        CompletableFuture<List<CustomFieldEntity>> customField = CompletableFuture.supplyAsync(() ->
                customFieldService.listByMap(map));
    
        CompletableFuture<List<ConnectorEntity>> connector = CompletableFuture.supplyAsync(() ->
            connectorService.listByMap(map));
    
        CompletableFuture<List<ConnectorItemEntity>> connectorItem = CompletableFuture.supplyAsync(() ->
            connectorItemService.listByMap(map));

        try {
            return Result.ok(ProductDeviceServerDataDTO.builder()
                    .labelBindRelationList(BeanUtilsIntensifier.copyBeanList(labelBindRelation.get(), LabelBindRelationDTO.class))
                    .labelList(BeanUtilsIntensifier.copyBeanList(label.get(), LabelDTO.class))
                    .channelList(BeanUtilsIntensifier.copyBeanList(channel.get(), ChannelDTO.class))
                    .channelParamList(BeanUtilsIntensifier.copyBeanList(channelParam.get(), ChannelParamDTO.class))
//                    .computeTaskList(BeanUtilsIntensifier.copyBeanList(task.get(), ComputeTaskDTO.class))
                    .dataModelList(BeanUtilsIntensifier.copyBeanList(dataModel.get(), DataModelDTO.class))
                    .dataModelPropertyList(BeanUtilsIntensifier.copyBeanList(dataModelProperty.get(), DataModelPropertyDTO.class))
                    .deviceList(BeanUtilsIntensifier.copyBeanList(device.get(), DeviceDTO.class))
                    .deviceModelInheritList(BeanUtilsIntensifier.copyBeanList(deviceModelInherit.get(), DeviceModelInheritDTO.class))
                    .deviceServiceList(BeanUtilsIntensifier.copyBeanList(deviceService.get(), DeviceServiceDTO.class))
                    .edgeGatewayList(BeanUtilsIntensifier.copyBeanList(edgeGateway.get(), EdgeGatewayDTO.class))
                    .labelGroupList(BeanUtilsIntensifier.copyBeanList(labelGroup.get(), LabelGroupDTO.class))
                    .resourceRelationList(BeanUtilsIntensifier.copyBeanList(resourceRelation.get(), ResourceRelationDTO.class))
                    .subscriptionList(BeanUtilsIntensifier.copyBeanList(subscription.get(), SubscriptionDTO.class))
                    .tagBindRelationList(BeanUtilsIntensifier.copyBeanList(tagBindRelation.get(), TagBindRelationDTO.class))
                    .thingModelList(BeanUtilsIntensifier.copyBeanList(thingModel.get(), ThingModelDTO.class))
                    .thingModelInheritList(BeanUtilsIntensifier.copyBeanList(thingModelInherit.get(), ThingModelInheritDTO.class))
                    .thingServiceList(BeanUtilsIntensifier.copyBeanList(thingService.get(), ThingServiceDTO.class))
                    .customDriverList(BeanUtilsIntensifier.copyBeanList(customDriver.get(), CustomDriverDTO.class))
                    .customMessageList(BeanUtilsIntensifier.copyBeanList(customMessage.get(), CustomMessageDTO.class))
                    .customFieldList(BeanUtilsIntensifier.copyBeanList(customField.get(), CustomFieldDTO.class))
                    .connectorDTOList(BeanUtilsIntensifier.copyBeanList(connector.get(), ConnectorDTO.class))
                    .connectorItemDTOList(BeanUtilsIntensifier.copyBeanList(connectorItem.get(), ConnectorItemDTO.class))
                    .build()
            );
        } catch (Exception e) {
            throw new BizException("查询失败");
        }finally {
            log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>> 工程文件：获取productDevice信息 :"  + sw.elapsed(TimeUnit.SECONDS) + "s<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
            sw.stop();
        }
    }

    @Override
    public Result<Void> createProductData(ProductDeviceServerDataDTO dto) {
        labelBindRelationService.jdbcTemplateBatchSave(BeanUtilsIntensifier.copyBeanList(dto.getLabelBindRelationList(), LabelBindRelationEntity.class),50000);
        labelService.jdbcTemplateBatchSave(BeanUtilsIntensifier.copyBeanList(dto.getLabelList(), LabelEntity.class), 50000);
        labelGroupService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getLabelGroupList(), LabelGroupEntity.class));
        channelService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getChannelList(), ChannelEntity.class));
        channelParamService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getChannelParamList(), ChannelParamEntity.class));
//        taskService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getComputeTaskList(), ComputeTaskEntity.class));
        dataModelService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getDataModelList(), DataModelEntity.class));
        dataModelPropertyService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getDataModelPropertyList(), DataModelPropertyEntity.class));
        deviceService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getDeviceList(), DeviceEntity.class));
        deviceModelInheritService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getDeviceModelInheritList(), DeviceModelInheritEntity.class));
        deviceServiceService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getDeviceServiceList(), DeviceServiceEntity.class));
        edgeGatewayService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getEdgeGatewayList(), EdgeGatewayEntity.class));
        resourceRelationService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getResourceRelationList(), ResourceRelationEntity.class));
        subscriptionService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getSubscriptionList(), SubscriptionEntity.class));
        tagBindRelationService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getTagBindRelationList(), TagBindRelationEntity.class));
        thingModelService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getThingModelList(), ThingModelEntity.class));
        thingModelInheritService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getThingModelInheritList(), ThingModelInheritEntity.class));
        thingServiceService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getThingServiceList(), ThingServiceEntity.class));
        customDriverService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getCustomDriverList(), CustomDriverEntity.class));
        customMessageService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getCustomMessageList(), CustomMessageEntity.class));
        customFieldService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getCustomFieldList(), CustomFieldEntity.class));
        connectorService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getConnectorDTOList(), ConnectorEntity.class));
        connectorItemService.insertBatchSomeColumn(BeanUtilsIntensifier.copyBeanList(dto.getConnectorItemDTOList(), ConnectorItemEntity.class));
        applicationContext.publishEvent(new ImportDataEvent(this, dto));
        return Result.ok();
    }

    @Override
    @Transactional
    public Result<Void> initProductData(ProductDeviceServerDataDTO dto, Long tenantId) {
        Stopwatch sw = Stopwatch.createStarted();
        this.deleteProductDataByTenantId(tenantId);
        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>> 工程文件：删除productDevice信息 :"  + sw.elapsed(TimeUnit.SECONDS) + "s<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
        sw.stop();
        Stopwatch sw1 = Stopwatch.createStarted();
        this.createProductData(dto);
        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>> 工程文件：插入productDevice信息 :"  + sw1.elapsed(TimeUnit.SECONDS) + "s<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
        sw1.stop();
        return Result.ok();
    }


}
