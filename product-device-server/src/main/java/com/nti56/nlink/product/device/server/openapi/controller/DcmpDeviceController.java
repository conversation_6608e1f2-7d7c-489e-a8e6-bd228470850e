package com.nti56.nlink.product.device.server.openapi.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.it.ITResult;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.model.DeviceRequestBo;
import com.nti56.nlink.product.device.server.model.DeviceRespondBo;
import com.nti56.nlink.product.device.server.model.device.vo.DcmpDeviceVO;
import com.nti56.nlink.product.device.server.openapi.convertor.ITResultConvertor;
import com.nti56.nlink.product.device.server.openapi.domain.request.ListDcmpDeviceRequest;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import com.nti56.nlink.product.device.server.service.IDeviceStatusManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 类说明: 设备controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:32:04
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/it/dcmp/device")
@Tag(name = "云管设备模块")
public class DcmpDeviceController {

    @Autowired
    private IDeviceService deviceService;


    @PostMapping(path = "list-basic")
    @Operation(summary = "获取设备列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DcmpDeviceVO.class)
                    )})
    })
    public ITResult<List<DcmpDeviceVO>> listBasicDeviceForDcmp(@RequestHeader("ot_headers") TenantIsolation tenantIsolation
            ,@RequestBody ListDcmpDeviceRequest request) {

        return ITResultConvertor.convert(R.result(deviceService.listBasicDeviceForDcmp(tenantIsolation,request)));
    }

    @Autowired
    IDeviceStatusManagementService deviceStatusManagementService;

    @PutMapping("batch/sync")
    @Operation(summary = "按条件批量同步设备",parameters = {
            @Parameter(name = "ids",description = "设备ID列表",required = true)
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DeviceRespondBo.class)
                    )})
    })
    public ITResult<List<DeviceRespondBo>> deviceBatchSync(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody DeviceRequestBo requestBo) {
        Result<List<DeviceRespondBo>> result = deviceStatusManagementService.deviceBatchSync(tenantIsolation,requestBo.getIds());
        return ITResultConvertor.convert(R.result(result));
    }


}
