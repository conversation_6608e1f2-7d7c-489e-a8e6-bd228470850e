package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.model.DeviceTwinRequestBo;
import com.nti56.nlink.product.device.server.model.EventCheckResult;
import com.nti56.nlink.product.device.server.model.EventDebugResult;
import com.nti56.nlink.product.device.server.model.PropertyValueWithTime;
import com.nti56.nlink.product.device.server.model.deviceLog.DeviceStateVo;
import com.nti56.nlink.product.device.server.model.product.vo.DeviceVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.tuple.Pair;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:11:44
 * @since JDK 1.8
 */
public interface IDeviceDebugService{

    Result<EventCheckResult> getRuntimeData(Long tenantId, Long deviceId, String eventName);

    Result<EventDebugResult> eventDebug(Long tenantId, Long deviceId, String eventName, Map<String, Object> input);

    Result<List<ChannelElm>> getUsefullyChannel(Long tenantId, Long edgeGatewayId, CommonFetcher commonFetcher);

    Result getByTenantId(TenantIsolation tenantIsolation, DeviceTwinRequestBo request);

    Result<Set<Long>> getSubscriptionState(Long tenantId, Long deviceId);

    Result<DeviceVO> getActual(Long tenantId, Long deviceId);

    Result<Map<String, Object>> getActuralPropertyOfDevices(Long tenantId, String property, Long[] deviceIds);

    Result<Map<String, Object>> getActuralPropertyOfDevices(Long tenantId, String property, List<String> deviceNames);

    Result<Map<String, Object>> getActuralPropertyOfDevicesByTenantIds(Set<Long> tenantIds, String property, List<String> deviceNames);

    Result<Map<String, Object>> getActuralPropertyOfDevices(String property, List<String> deviceNames, List<Long> tenantIds);
    
    Result<Map<String, PropertyValueWithTime>> getActuralPropertyAndTimeOfDevices(Long tenantId, String property, List<String> deviceNames);

    Result<Map<String, PropertyValueWithTime>> getActuralPropertyAndTimeOfDevices(String property, List<String> deviceNames, List<Long> tenantIds);

    Result<Map<String, Object>> getActuralPropertiesOfDevice(Long tenantId, List<String> properties, String deviceName);
    
    Result<Map<String, PropertyValueWithTime>> getActuralPropertiesAndTimesOfDevice(Long tenantId, List<String> properties, String deviceName);

    Result<Page<DeviceStateVo>> pageStateOfDevice(Long tenantId, String deviceName, Integer size, Integer current, String begin, String end);

}
