package com.nti56.nlink.product.device.server.openapi.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ResourceTypeEnum;
import com.nti56.nlink.product.device.server.model.DeviceDto;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import com.nti56.nlink.product.device.server.service.ITagBindRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


/**
 * <p>
 * 标志关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-11 17:27:49
 * @since JDK 1.8
 */
@RestController
@RequestMapping("it/tag_bind_relation")
@Tag(name = "标志关系表")
public class ITTagBindRelationController {

    @Autowired
    ITagBindRelationService service;

    @Autowired
    @Lazy
    IDeviceService deviceService;

    @GetMapping("list/{resourceType}")
    @Operation(summary = "获取标记列表")
    public R listTag(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable @Param("资源类型:1-产品 2-设备 3-网关 4-标签 5-物模型") Integer resourceType){
        ResourceTypeEnum type = ResourceTypeEnum.typeOfValue(resourceType);
        if (!Optional.ofNullable(type).isPresent()) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        return service.getTagList(tenantIsolation,type.getValue());
    }

    @GetMapping("list/device/{tagId}")
    @Operation(summary = "根据标记 ID 获取设备列表")
    public R listDevice(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable @Param("tagId") String tagId){
        if (!Optional.ofNullable(tagId).isPresent()) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        List<String> list = new ArrayList<>();
        list.add(tagId);
        DeviceDto build = new DeviceDto();
        build.setTagIds(list);
        build.setTenantId(tenantIsolation.getTenantId());
        Result result = deviceService.listDeviceByTag(build);
        return R.result(result);
    }

    @PostMapping("list/device")
    @Operation(summary = "根据标记 ID列表 获取设备列表")
    public R listDeviceByTagIds(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody @Param("tagIds") List<String> tagIds){
        if (CollectionUtil.isEmpty(tagIds)) {
            return R.ok();
        }
        DeviceDto build = new DeviceDto();
        build.setTagIds(tagIds);
        build.setTenantId(tenantIsolation.getTenantId());
        Result result = deviceService.listDeviceByTag(build);
        return R.result(result);
    }

}
