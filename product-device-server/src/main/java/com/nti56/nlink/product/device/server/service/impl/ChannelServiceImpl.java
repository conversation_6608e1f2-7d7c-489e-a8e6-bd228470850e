package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.device.AccessElm;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.annotation.AuditLog;
import com.nti56.nlink.product.device.server.config.websocket.WsSessionManager;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.channel.Channel;
import com.nti56.nlink.product.device.server.domain.thing.channel.ChannelParam;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ChannelStatusEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DriverEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ModelTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.label.LabelGroup;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwDebugTopic;
import com.nti56.nlink.product.device.server.domain.thing.topic.ThirdTopic;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;
import com.nti56.nlink.product.device.server.entity.LabelEntity;
import com.nti56.nlink.product.device.server.entity.LabelGroupEntity;
import com.nti56.nlink.product.device.server.enums.ActionEnum;
import com.nti56.nlink.product.device.server.enums.AuditTargetEnum;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewayCacheProxy;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewaySpiProxy;
import com.nti56.nlink.product.device.server.mapper.ChannelMapper;
import com.nti56.nlink.product.device.server.mapper.ChannelParamMapper;
import com.nti56.nlink.product.device.server.model.ChannelDto;
import com.nti56.nlink.product.device.server.model.ChannelRequestBo;
import com.nti56.nlink.product.device.server.model.ConnectResult;
import com.nti56.nlink.product.device.server.model.LabelGroupDto;
import com.nti56.nlink.product.device.server.model.channel.dto.*;
import com.nti56.nlink.product.device.server.model.channel.vo.*;
import com.nti56.nlink.product.device.server.model.label.dto.LabelGroupDTO;
import com.nti56.nlink.product.device.server.service.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.dozer.Mapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.handler.ConcurrentWebSocketSessionDecorator;

import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-15 14:11:44
 * @since JDK 1.8
 */
@Service
@Slf4j
@Validated
public class ChannelServiceImpl extends BaseServiceImpl<ChannelMapper,ChannelEntity> implements IChannelService {

    @Autowired
    private ChannelMapper channelMapper;

    @Autowired
    private ChannelParamMapper channelParamMapper;

    @Autowired
    private Mapper dozerMapper;

    @Autowired
    private IChannelParamService channelParamService;

    @Autowired
    private ILabelGroupService labelGroupService;

    @Autowired
    private IEdgeGatewayService edgeGatewayService;
    
    @Autowired
    private IEdgeGatewaySpiProxy edgeGatewaySpiProxy;

    @Autowired
    private ILabelBindRelationService labelBindRelationService;

    @Autowired
    private IConnectChannelService connectChannelService;

    @Autowired
    private IEdgeGatewayCacheProxy edgeGatewayCacheProxy;

    @Autowired
    private ILabelService labelService;

    @Getter
    @Value("${mqtt.wsHost}")
    private String wsHost;

    @Getter
    @Value("${mqtt.wsPort}")
    private Integer wsPort;

    @Getter
    @Value("${mqtt.wsUrl}")
    private String wsUrl;

    @Getter
    @Value("${mqtt.username}")
    private String mqttUsername;

    @Getter
    @Value("${mqtt.password}")
    private String mqttPassword;

    @Autowired @Lazy
    private StringRedisTemplate stringRedisTemplate;
    
    @Autowired @Lazy
    private ISubscriptionService subscriptionService;

    @Override
    public Result<Page<ChannelDto>> getChannelPage(ChannelDto channel, Page<ChannelDto> page, TenantIsolation tenantIsolation) {
        Page<ChannelDto> channelDtoPage = channelMapper.getChannelByPage(page, channel, tenantIsolation);
        return Result.ok(channelDtoPage);
    }

    @Override
    public Result<List<ChannelVO>> listChannel(ChannelEntity channel, TenantIsolation tenantIsolation) {
        LambdaQueryChainWrapper<ChannelEntity> wrapper = new LambdaQueryChainWrapper<>(channelMapper);
        if (Optional.ofNullable(channel.getName()).isPresent()) {
            wrapper.like(ChannelEntity::getName, channel.getName());
        }
        wrapper.or();
        if (Optional.ofNullable(channel.getEdgeGatewayId()).isPresent()) {
            wrapper.eq(ChannelEntity::getEdgeGatewayId, channel.getEdgeGatewayId());
        }
        wrapper.eq(ChannelEntity::getTenantId, tenantIsolation.getTenantId());
        List<ChannelEntity> channelEntities = wrapper.list();
        if (CollectionUtil.isEmpty(channelEntities)){
            return Result.ok();
        }
        List<ChannelVO> channelVOS = BeanUtilsIntensifier.copyBeanList(channelEntities, ChannelVO.class);
        for (ChannelVO channelVO : channelVOS) {
            channelVO.setDriverName(DriverEnum.getNameByValue(channelVO.getDriver()));
        }

        return Result.ok(Optional.of(channelVOS).orElse(new ArrayList<>())
                .stream()
                .sorted(Comparator.comparing(ChannelVO::getName))
                .collect(Collectors.toList()));
    }

    @Override
    public Result<ChannelEntity> getChannelById(Long channelId) {
        LambdaQueryWrapper<ChannelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ChannelEntity::getId, channelId);
        return Result.ok(channelMapper.selectOne(lqw));
    }

    @Override
    public Result<ChannelEntity> getByIdAndTenantIsolation(Long channelId, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<ChannelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ChannelEntity::getId, channelId)
                .eq(ChannelEntity::getTenantId, tenantIsolation.getTenantId());
        return Result.ok(channelMapper.selectOne(lqw));
    }

    @Override
    public Result<List<ChannelVO>> listChannelNoEdgeGateway(TenantIsolation tenantIsolation) {
        List<ChannelEntity> channelEntities =
                new LambdaQueryChainWrapper<>(channelMapper)
                        .isNull(ChannelEntity::getEdgeGatewayId)
                        .eq(ChannelEntity::getTenantId, tenantIsolation.getTenantId())
                        .list();
        List<ChannelVO> channels = BeanUtilsIntensifier.copyBeanList(channelEntities, ChannelVO.class);
        for (ChannelVO channelVO : channels) {
            channelVO.setDriverName(DriverEnum.getNameByValue(channelVO.getDriver()));
        }
        return Result.ok(channels);
    }

    @Override
    public Result unbindEdgeGateway(List<Long> oldChannelIds) {
        if (Optional.ofNullable(oldChannelIds).isPresent() && oldChannelIds.size() > 0) {
            new LambdaUpdateChainWrapper<>(channelMapper).set(ChannelEntity::getEdgeGatewayId, null).in(ChannelEntity::getId, oldChannelIds).update();
        }
        return Result.ok();
    }

    @Override
    public Result bingEdgeGateway(List<Long> newChannelIds, Long edgeGatewayId) {
        if (Optional.ofNullable(newChannelIds).isPresent() && newChannelIds.size() > 0 && Optional.ofNullable(edgeGatewayId).isPresent()) {
            new LambdaUpdateChainWrapper<>(channelMapper).set(ChannelEntity::getEdgeGatewayId, edgeGatewayId).in(ChannelEntity::getId, newChannelIds).update();
        }
        return Result.ok();
    }

    /**
     * 校对
     * @param channelId
     */
    public void proofreadChannel(Long channelId, TenantIsolation tenantIsolation) {
        ProofreadChannelDTO proofreadChannelDTO = new ProofreadChannelDTO();
        proofreadChannelDTO.setChannelParamList(channelParamService.listProofreadDataByChannelId(channelId,tenantIsolation).getResult());

        String md5Proofread = DigestUtils.md5Hex(JSON.toJSONString(proofreadChannelDTO, SerializerFeature.MapSortField));
        ChannelEntity channel = new ChannelEntity();
        channel.setId(channelId);
        channel.setMd5Proofread(md5Proofread);

        if (channelMapper.updateById(channel) == 0) {
            throw new BizException(ServiceCodeEnum.CODE_UPDATE_FAIL);
        }
    }



    public void proofreadChannel(ChannelEntity oldChannel, ChannelEntity newEntity, TenantIsolation tenantIsolation) {
        ProofreadChannelDTO proofreadChannelDTO = new ProofreadChannelDTO();
        proofreadChannelDTO.setChannelParamList(channelParamService.listProofreadDataByChannelId(newEntity.getId(),tenantIsolation).getResult());

        String md5Proofread = DigestUtils.md5Hex(JSON.toJSONString(proofreadChannelDTO, SerializerFeature.MapSortField));
        newEntity.setMd5Proofread(md5Proofread);

        if (oldChannel.getEdgeGatewayId() != null && !md5Proofread.equals(oldChannel.getMd5Proofread())) {
            edgeGatewayService.setNotSyncById(oldChannel.getEdgeGatewayId());
        }

        if (channelMapper.updateById(newEntity) == 0) {
            throw new BizException(ServiceCodeEnum.CODE_UPDATE_FAIL);
        }
    }

    @Override
    @Transactional
    @AuditLog(action = ActionEnum.DELETE,target = AuditTargetEnum.CHANNEL, details = "删除通道")
    public Result deleteChannel(Long id, TenantIsolation tenantIsolation) {
        ChannelEntity channelEntity = this.getByIdAndTenantIsolation(id, tenantIsolation).getResult();
        if (channelEntity == null) {
            throw new BizException("该租户下找不到该通道");
        }

        Result<Void> deleteChannelParamByChannelIdResult = channelParamService.deleteChannelParamByChannelId(id);
        if (!deleteChannelParamByChannelIdResult.getSignal()) {
            throw new BizException(deleteChannelParamByChannelIdResult.getMessage());
        }

        Result<Void> delete = labelGroupService.delete(id,tenantIsolation,new LabelGroupDTO());
        if (!delete.getSignal()) {
            throw new BizException(delete.getMessage());
        }

        channelMapper.deleteById(id);
        if (channelEntity.getEdgeGatewayId() != null){
            edgeGatewayService.setNotSyncById(channelEntity.getEdgeGatewayId());
        }
        String delKey = RedisConstant.EDGE_CHANNEL_STATUS_CACHE_PREFIX + channelEntity.getTenantId() + ":" + channelEntity.getEdgeGatewayId();
        if(stringRedisTemplate.opsForHash().hasKey(delKey,String.valueOf(channelEntity.getId()))){
            stringRedisTemplate.opsForHash().delete(delKey,String.valueOf(channelEntity.getId()));
        }
        subscriptionService.delByDirectlyModelId(id,tenantIsolation.getTenantId(), ModelTypeEnum.CHANNEL_MODEL);
    
        return Result.ok();
    }

    @Override
    public Result<Integer> countByEdgeGatewayId(Long edgeGatewayId) {
        LambdaQueryWrapper<ChannelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ChannelEntity::getEdgeGatewayId, edgeGatewayId);
        return Result.ok(channelMapper.selectCount(lqw));
    }

    @Override
    public Result<List<ChannelEntity>> listByEdgeGatewayId(Long edgeGatewayId,Long tenantId,boolean onlyOnline) {
        LambdaQueryWrapper<ChannelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ChannelEntity::getEdgeGatewayId, edgeGatewayId)
            .eq(onlyOnline,ChannelEntity::getStatus, ChannelStatusEnum.ONLINE.getValue())
            .eq(ChannelEntity::getTenantId,tenantId);
        return Result.ok(channelMapper.selectList(lqw));
    }

    @Override
    public Result<Void> unbindByEdgeGatewayId(Long edgeGatewayId) {
        LambdaUpdateWrapper<ChannelEntity> luw = new LambdaUpdateWrapper<>();
        luw.eq(ChannelEntity::getEdgeGatewayId,edgeGatewayId)
            .set(ChannelEntity::getEdgeGatewayId,null);
        channelMapper.update( new ChannelEntity(),luw);
        return Result.ok();
    }

    @Override
    public Result<List<ChannelEntity>> listChannelByEdgeGatewayId(Long edgeGatewayId, Long tenantId,Boolean runtime) {
        if (!Optional.ofNullable(edgeGatewayId).isPresent() || !Optional.ofNullable(tenantId).isPresent()) {
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        List<ChannelEntity> list = new LambdaQueryChainWrapper<>(channelMapper)
                .eq(ChannelEntity::getEdgeGatewayId, edgeGatewayId)
                .isNotNull(runtime,ChannelEntity::getRuntimeInfo)
                .eq(ChannelEntity::getTenantId, tenantId).list();
        return Result.ok(list);
    }

    @Override
    @Transactional
    public Result bingEdgeGateway(Long channelId, Long edgeGatewayId, TenantIsolation tenantIsolation) {
        ChannelEntity channel = this.getByIdAndTenantIsolation(channelId, tenantIsolation).getResult();
        if (channel == null) {
            throw new BizException("该租户下找不到该渠道");
        }
        if(edgeGatewayId.equals(channel.getEdgeGatewayId())){
            return Result.ok();
        }
        ChannelEntity channelEntity = new ChannelEntity();
        channelEntity.setId(channelId);
        channelEntity.setEdgeGatewayId(edgeGatewayId);
        channelMapper.updateById(channelEntity);
        if (channel.getEdgeGatewayId() != null){
            edgeGatewayService.setNotSyncById(channel.getEdgeGatewayId());
        }

        edgeGatewayService.setNotSyncById(edgeGatewayId);
        return Result.ok();
    }
    
    @Override
    public Result connectTest(TenantIsolation tenantIsolation, List<Long> channelIds) {
        List<ChannelEntity> channelList = getChannelListByChannelIds(tenantIsolation,channelIds);
        // 由于测试连接都是在同一个网关下的
        Long edgeGatewayId = channelList.get(0).getEdgeGatewayId();
        List<ChannelElm> channelElmList = getChannelELmList(tenantIsolation,channelIds,channelList);
        Result<List<ConnectResult>> result = edgeGatewaySpiProxy.connectChannel(edgeGatewayId, tenantIsolation.getTenantId(), channelElmList);
        return result;
    }
    
    public List<ChannelEntity> getChannelListByChannelIds(TenantIsolation tenantIsolation, List<Long> channelIds){
        LambdaQueryWrapper<ChannelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.in(ChannelEntity::getId, channelIds).eq(ChannelEntity::getTenantId,tenantIsolation.getTenantId()).eq(ChannelEntity::getDeleted,0);
        List<ChannelEntity> channelList = channelMapper.selectList(lqw);
        return channelList;
    }
    
    public List<ChannelElm> getChannelELmList(TenantIsolation tenantIsolation,List<Long> channelIds,List<ChannelEntity> channelList){
        List<ChannelParamDTO> channelParamDTOList = channelParamService.listChannelParamByChannelIds(channelIds,tenantIsolation).getResult();
        Map<Long, List<ChannelParamDTO>> paramGroupList =channelParamDTOList.stream().collect(Collectors.groupingBy(ChannelParamDTO::getChannelId));
        List<ChannelElm> channelElmList = new ArrayList<>();
        channelList.forEach(channel ->{
            List<ChannelParamDTO> paramList = paramGroupList.get(channel.getId());
            
            ChannelElm channelElm = new ChannelElm();
            channelElm.setChannelId(channel.getId());
            channelElm.setDriver(DriverEnum.getNameByValue(channel.getDriver()));

            DriverEnum driver = DriverEnum.typeOfValue(channel.getDriver());
            
            List<ChannelParamEntity> entityList = paramList.stream()
                .map(t -> {
                    ChannelParamEntity entity = new ChannelParamEntity();
                    BeanUtils.copyProperties(t, entity);
                    return entity;
                })
                .collect(Collectors.toList());
            Result<ChannelParam> apply = ChannelParam.checkParam(driver, channel.getIsServer(), entityList);

            apply.getResult().processChannelElm(channelElm);
            channelElmList.add(channelElm);
        });
        return channelElmList;
    }

    @Override
    @Transactional
    @AuditLog(action = ActionEnum.CREATE,target = AuditTargetEnum.CHANNEL, details = "创建新通道")
    public Result<CreateChannelAllVO> createChannel(@Valid CreateChannelDTO dto, TenantIsolation tenantIsolation) {
        Result<Void> result = this.uniqueNameInEdgeGateway(dto.getName(), dto.getEdgeGatewayId(),tenantIsolation.getTenantId());
        if (!result.getSignal()) {
            return Result.error(result.getServiceCode(),result.getMessage());
        }

        ChannelEntity channelEntity = dozerMapper.map(dto, ChannelEntity.class);
        if (channelMapper.insert(channelEntity) == 0) {
            throw new BizException(ServiceCodeEnum.CODE_CREATE_FAIL);
        }

        Result<Void> createOrEditChannelParamListResult = channelParamService.createOrEditChannelParamList(
            channelEntity.getId(),
            channelEntity.getDriver(),
            dto.getIsServer(),
            dto.getChannelParamList(), 
            tenantIsolation
        );
        if (!createOrEditChannelParamListResult.getSignal()) {
            throw new BizException(createOrEditChannelParamListResult.getMessage());
        }

        this.proofreadChannel(channelEntity.getId(),tenantIsolation);

        if (dto.getEdgeGatewayId() != null ) {
            edgeGatewayService.setNotSyncById(dto.getEdgeGatewayId());
        }


        return Result.ok(CreateChannelAllVO.builder()
                .channelId(channelEntity.getId()).build());
    }

    @Override
    @Transactional
    @AuditLog(action = ActionEnum.UPDATE,target = AuditTargetEnum.CHANNEL, details = "修改通道")
    public Result<EditChannelAllVO> editChannel(EditChannelDTO dto, TenantIsolation tenantIsolation) {
        ChannelEntity channelEntity = this.getByIdAndTenantIsolation(dto.getId(), tenantIsolation).getResult();
        if (channelEntity == null) {
            throw new BizException("该租户下找不到该渠道");
        }

        Result<Void> result = this.uniqueNameInEdgeGateway(dto.getId(), dto.getName(), channelEntity.getEdgeGatewayId(),tenantIsolation.getTenantId());
        if (!result.getSignal()) {
            throw new BizException(result.getMessage());
        }

        channelParamService.deleteByIdsAndTenantIsolation(dto.getDeleteChannelParamIds(), tenantIsolation);

        Result<Void> createOrEditChannelParamListResult = channelParamService.createOrEditChannelParamList(
            dto.getId(),
            channelEntity.getDriver(),
            dto.getIsServer(),
            dto.getChannelParamList(), 
            tenantIsolation
        );
        if (!createOrEditChannelParamListResult.getSignal()) {
            throw new BizException(createOrEditChannelParamListResult.getMessage());
        }

        ChannelEntity channel = dozerMapper.map(dto, ChannelEntity.class);

        this.proofreadChannel(channelEntity, channel,tenantIsolation);

        if(!channelEntity.getName().equals(dto.getName())){
            CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());
            channelEntity.setName(channel.getName());
            Result<Channel> channelResult = Channel.checkInfo(channelEntity, commonFetcher);
            if(!channelResult.getSignal()){
                return Result.error(channelResult.getMessage());
            }
            Channel c = channelResult.getResult();
            labelBindRelationService.updateByChannel(c,tenantIsolation);
        }

        return Result.ok(EditChannelAllVO.builder()
                .channelId(dto.getId())
                .edgeGatewayId(channelEntity.getEdgeGatewayId()).build());
    }

    @Override
    public Result<QueryChannelByIdVO> getChannel(Long id, TenantIsolation tenantIsolation) {
        ChannelEntity channelEntity = this.getByIdAndTenantIsolation(id, tenantIsolation).getResult();
        if (channelEntity == null) {
            return Result.ok();
        }
        
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());
        Result<Channel> channelResult = Channel.checkInfo(channelEntity, commonFetcher);
        if(!channelResult.getSignal()){
            return Result.error(channelResult.getMessage());
        }

        Channel channel = channelResult.getResult();

        QueryChannelByIdVO result = channel.toVo();

        // QueryChannelByIdVO result = BeanUtilsIntensifier.copyBean(channel, QueryChannelByIdVO.class);
        // result.setDriverName(DriverEnum.typeOfValue(result.getDriver()).getNameDesc());
        // result.setChannelParamList(
        //         BeanUtilsIntensifier.copyBeanList(
        //                 channelParamService.getByChannelId(id,channel.getDriver()).getResult(),
        //                 ChannelParamVO.class
        //         ));

        return Result.ok(result);
    }

    /**
     * 通道测试列表
     * @param tenantIsolation
     * @param edgeGatewayId
     * @return
     */
    @Override
    public Result<List<ChannelCheckoutOutVO>> channelConnectTestList(TenantIsolation tenantIsolation, Long edgeGatewayId) {
        List<ChannelParamDTO> channelParamDTOList = channelParamService.listChannelParamByEdgeGatewayId(edgeGatewayId,tenantIsolation).getResult();
        Map<String,String> channelParamMap = buildChannelParamMap(channelParamDTOList);
        LambdaQueryWrapper<ChannelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ChannelEntity::getEdgeGatewayId, edgeGatewayId).eq(ChannelEntity::getTenantId,tenantIsolation.getTenantId()).eq(ChannelEntity::getDeleted,0);
        List<ChannelEntity> channelList = channelMapper.selectList(lqw);
        if(CollectionUtil.isEmpty(channelList)){
            return Result.ok(new ArrayList<>());
        }
        List<ChannelCheckoutOutVO> resultList = new ArrayList<>(channelList.size());
        channelList.forEach(channel->{
            ChannelCheckoutOutVO channelCheckoutOutVO = new ChannelCheckoutOutVO();
            BeanUtils.copyProperties(channel, channelCheckoutOutVO);
            if(channelParamMap.get(channel.getId() + ":ip") != null){
                channelCheckoutOutVO.setIp(channelParamMap.get(channel.getId() + ":ip"));
            }
            if(channelParamMap.get(channel.getId() + ":port") != null){
                channelCheckoutOutVO.setPort(channelParamMap.get(channel.getId() + ":port"));
            }
            channelCheckoutOutVO.setDriverName(DriverEnum.getNameByValue(channel.getDriver()));
            resultList.add(channelCheckoutOutVO);
        });
        return Result.ok(resultList);
    }

    @Override
    public Result<QueryChannelAllByIdVO> getChannelAll(Long id, TenantIsolation tenantIsolation) {
        ChannelEntity channel = this.getByIdAndTenantIsolation(id, tenantIsolation).getResult();
        if (channel == null) {
            return Result.ok();
        }

        QueryChannelAllByIdVO result = BeanUtilsIntensifier.copyBean(channel, QueryChannelAllByIdVO.class);
        result.setDriverName(DriverEnum.typeOfValue(result.getDriver()).getNameDesc());
        result.setChannelParamList(
                BeanUtilsIntensifier.copyBeanList(
                        channelParamService.getByChannelId(id,channel.getDriver()).getResult(),
                        ChannelParamVO.class
                ));
        result.setLabelGroupList(labelGroupService.getByChannelId(id).getResult());

        return Result.ok(result);
    }
    @Autowired
    private CommonFetcherFactory commonFetcherFactory;

    @Override
    @Async("collectingOnceAsyncExecutor")
    public void collectingOnce(String sessionId, String key,ConcurrentWebSocketSessionDecorator session, ChannelEntity channelEntity,List<AccessElm> accessElms, List<ChannelParamEntity> paramEntities) {

        ChannelElm channelElm = new ChannelElm();
        channelElm.setChannelId(channelEntity.getId());
        channelElm.setDriver(DriverEnum.getNameByValue(channelEntity.getDriver()));
        
        try{
            DriverEnum driver = DriverEnum.typeOfValue(channelEntity.getDriver());
            Result<ChannelParam> apply = ChannelParam.checkParam(driver, channelEntity.getIsServer(), paramEntities);
            if (!apply.getSignal()) {
                log.error("websocket客户端：{}，channelId:{},通道属性错误：{}",key,channelEntity.getId(),apply.getMessage());
                session.sendMessage(new TextMessage(JSON.toJSONString(
                    Result.error(ServiceCodeEnum.CHANNEL_PARAMS_FORMAT_FAIL)
                )));
                return;
            }
            apply.getResult().processChannelElm(channelElm);
            List<ChannelElm> channelElmList = new ArrayList<>();
            channelElmList.add(channelElm);
            if (!session.isOpen() && WsSessionManager.containsKey(key)) {
                session = (ConcurrentWebSocketSessionDecorator) WsSessionManager.get(key);
            }

            //通道
            Result<List<ConnectResult>> connectChannelResult = edgeGatewayCacheProxy.channelConnection(channelEntity.getEdgeGatewayId(),channelEntity.getTenantId(),channelElmList);

            //标签
            Result<List<ConnectResult>> connectLabelResult = edgeGatewayCacheProxy.labelValue(
                    channelEntity.getEdgeGatewayId(),
                    channelEntity.getTenantId(),
                    accessElms
            );

            if (!session.isOpen() && WsSessionManager.containsKey(key)) {
                session = (ConcurrentWebSocketSessionDecorator) WsSessionManager.get(key);
            }
            if(Optional.ofNullable(session).isPresent() && session.isOpen()){
                if(connectChannelResult.getSignal()){
                    ConnectResult connectResult = connectChannelResult.getResult().get(0);
                    if(connectResult.getOk()){
                        session.sendMessage(new TextMessage(JSON.toJSONString(
                                R.ok()
                                        .put("method", "connectChannel")
                                        .put("channelId",channelEntity.getId())
                                        .put("channelName",channelEntity.getName())
                                        .put("avgDelayMs",connectResult.getAvgDelayMs())
                        )));
                    }else{
                        session.sendMessage(new TextMessage(JSON.toJSONString(
                                R.error(connectResult.getMessage())
                                        .put("method", "connectChannel")
                                        .put("channelId",channelEntity.getId())
                                        .put("channelName",channelEntity.getName())
                        )));
                    }
                }else{
                    session.sendMessage(new TextMessage(JSON.toJSONString(
                            R.error(connectChannelResult.getMessage())
                                    .put("method", "connectChannel")
                                    .put("channelId",channelEntity.getId())
                                    .put("channelName",channelEntity.getName())
                    )));
                }
                session.sendMessage(new TextMessage(JSON.toJSONString(
                        R.result(connectLabelResult)
                                .put("method", "connectLabel")
                                .put("channelId",channelEntity.getId())
                                .put("channelName",channelEntity.getName())
                )));
                WsSessionManager.removeSessionInit(sessionId,channelEntity.getId());
            }else {
                log.error("websocket数据采集，通道初始化找不到可用的连接!");
                WsSessionManager.removeSessionInit(sessionId);
            }
        } catch (IOException e) {
            log.error("websocket发送消息失败！" + e.getMessage());
        }catch (IllegalStateException e){
            log.error("TheWebSocketSession {} has been closed {}",key, e.fillInStackTrace());
            WsSessionManager.remove(key);
        }

    }

    @Override
    public Result<ChannelEntity> getByLabelGroupId(Long labelGroupId) {
        return Result.ok(channelMapper.getByLabelGroupId(labelGroupId));
    }

    @Override
    public Result<List<ListChannelWithLabelGroupTreeAndLabelVO>> listChannelWithLabelGroupTreeAndLabel(ListChannelWithLabelGroupTreeAndLabelDTO dto, TenantIsolation tenantIsolation) {
        Result<List<ChannelEntity>> listResult = this.listByEdgeGatewayId(dto.getEdgeGatewayId(), tenantIsolation.getTenantId(),false);
        if (!listResult.getSignal() || CollectionUtil.isEmpty(listResult.getResult())){
            return Result.ok();
        }
        List<ChannelEntity> channelEntities = listResult.getResult();
        List<ListChannelWithLabelGroupTreeAndLabelVO> listChannelWithLabelGroupTreeAndLabelVOS = BeanUtil.copyToList(channelEntities, ListChannelWithLabelGroupTreeAndLabelVO.class);
        if (CollectionUtil.isNotEmpty(dto.getChannelIds()) && CollectionUtil.isNotEmpty(listChannelWithLabelGroupTreeAndLabelVOS)){
            for (ListChannelWithLabelGroupTreeAndLabelVO listChannelWithLabelGroupTreeAndLabelVO : listChannelWithLabelGroupTreeAndLabelVOS) {
                if (dto.getChannelIds().contains(listChannelWithLabelGroupTreeAndLabelVO.getId())){
                    List<LabelGroup> result = labelGroupService.getLabelGroupTree(listChannelWithLabelGroupTreeAndLabelVO.getId(), tenantIsolation, null).getResult();
                    if (CollectionUtil.isNotEmpty(result)){
                        for (LabelGroup labelGroup : result) {
                            labelGroup.setDriver(listChannelWithLabelGroupTreeAndLabelVO.getDriver());
                        }
                    }
                    listChannelWithLabelGroupTreeAndLabelVO.setLabelGroups(result);
                }
            }
        }

        return Result.ok(Optional.ofNullable(listChannelWithLabelGroupTreeAndLabelVOS).orElse(new ArrayList<>())
                .stream()
                .sorted(Comparator.comparing(ListChannelWithLabelGroupTreeAndLabelVO::getName))
                .collect(Collectors.toList()));
    }

    @Override
//    @GatewayChange(value = "#edgeGatewayId",state = true)
    @AuditLog(action = ActionEnum.UPDATE,target = AuditTargetEnum.CHANNEL, details = "通道状态改变")
    public Result<Void> statusChange(Long channelId, Long edgeGatewayId, TenantIsolation tenantIsolation) {
        ChannelEntity channelEntity = channelMapper.getById(tenantIsolation.getTenantId(), channelId);
        if(channelEntity!=null){
            //toggle status
            Integer status=0;
            if (channelEntity.getStatus() == 0) {
                status=1;
            }
            if (channelMapper.statusChange(channelId, edgeGatewayId,tenantIsolation.getTenantId(),status) == 1) {
                edgeGatewayService.setNotSyncById(edgeGatewayId);
                return Result.ok();
            }
        }
        return Result.error();
    }

    @Override
    @AuditLog(action = ActionEnum.UPDATE,target = AuditTargetEnum.CHANNEL, details = "通道轮询间隔修改")
    public Result<Void> modifyChannelInterval(Long channelId, Long edgeGatewayId, Integer intervalMs, TenantIsolation tenantIsolation) {
        if (!Optional.ofNullable(intervalMs).isPresent() || intervalMs.compareTo(10) < 0) {
            return Result.error(ServiceCodeEnum.CHANNEL_INTERVAL_ERROR);
        }
        if (channelMapper.modifyChannelInterval(channelId, edgeGatewayId, intervalMs,tenantIsolation.getTenantId()) == 1) {
            edgeGatewayService.setNotSyncById(edgeGatewayId);
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
    }

    @Override
    public Result<Void> deleteByEdgeGatewayId(Long edgeGatewayId) {
        LambdaQueryWrapper<ChannelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ChannelEntity::getEdgeGatewayId,edgeGatewayId);
        channelMapper.delete(lqw);
        return Result.ok();
    }

    @Override
    @Transactional
    @AuditLog(action = ActionEnum.CREATE,target = AuditTargetEnum.CHANNEL, details = "复制通道")
    public Result<CreateChannelAllVO> copyChannel(CopyChannelDTO dto, TenantIsolation tenantIsolation) {

        Result<ChannelEntity> copyChannelResult = this.getByIdAndTenantIsolation(dto.getCopyChannelId(), tenantIsolation);
        if (!copyChannelResult.getSignal() || copyChannelResult.getResult() == null){
            throw new BizException("被复制的通道不存在");
        }
    
        Result<Void> result = this.uniqueNameInEdgeGateway(dto.getName(), copyChannelResult.getResult().getEdgeGatewayId(),tenantIsolation.getTenantId());
        if (!result.getSignal()) {
            return Result.error(result.getServiceCode(),result.getMessage());
        }

        ChannelEntity channelEntity = copyChannelResult.getResult();
        channelEntity.setId(null);
        channelEntity.setName(dto.getName());
        channelEntity.setDescript(dto.getDescript());
        channelEntity.setRuntimeInfo(null);
        if (channelMapper.insert(channelEntity) == 0) {
            throw new BizException(ServiceCodeEnum.CODE_CREATE_FAIL);
        }

        Result<Void> createOrEditChannelParamListResult = channelParamService.createOrEditChannelParamList(
            channelEntity.getId(),
            channelEntity.getDriver(),
            channelEntity.getIsServer(),
            dto.getChannelParamList(), 
            tenantIsolation
        );
        if (!createOrEditChannelParamListResult.getSignal()) {
            throw new BizException(createOrEditChannelParamListResult.getMessage());
        }

        Result<List<LabelGroupDto>> labelGroupResult = labelGroupService.listLabelGroupByChannelId(dto.getCopyChannelId(), tenantIsolation);
        if (labelGroupResult.getSignal() && CollectionUtil.isNotEmpty(labelGroupResult.getResult())){
            for (LabelGroupEntity labelGroupEntity : labelGroupResult.getResult()) {
                Long oldId = labelGroupEntity.getId();
                labelGroupEntity.setId(null);
                labelGroupEntity.setChannelId(channelEntity.getId());
                labelGroupService.save(labelGroupEntity);
                List<LabelEntity> labelEntities = labelService.listByLabelGroupId(oldId, tenantIsolation);
                if(CollectionUtil.isNotEmpty(labelEntities)){
                    for (LabelEntity labelEntity : labelEntities) {
                        labelEntity.setId(null);
                        labelEntity.setLabelGroupId(labelGroupEntity.getId());
                    }
                    labelService.insertBatchSomeColumn(labelEntities);
                }
            }
        }

        this.proofreadChannel(channelEntity.getId(),tenantIsolation);

        if (channelEntity.getEdgeGatewayId() != null ) {
            edgeGatewayService.setNotSyncById(channelEntity.getEdgeGatewayId());
        }


        return Result.ok(CreateChannelAllVO.builder()
                .channelId(channelEntity.getId()).build());
    }

    @Override
    public Result<List<ChannelStatusVO>> channelStatus(Long edgeGatewayId, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<ChannelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ChannelEntity::getEdgeGatewayId, edgeGatewayId)
                .eq(ChannelEntity::getTenantId, tenantIsolation.getTenantId())
                .eq(ChannelEntity::getDeleted,0);
        List<ChannelEntity> channelList = channelMapper.selectList(lqw);
        String channelStatusMapStr = stringRedisTemplate.opsForValue().get(RedisConstant.EDGE_GATEWAY_CHANNEL_STATUS_PREFIX + tenantIsolation.getTenantId() + ":" + edgeGatewayId);
        if(CollectionUtil.isEmpty(channelList)) {
           return Result.ok(new ArrayList<>());
        }
        List<ChannelStatusVO> channelStatusVOList = new ArrayList<>();
        if(StringUtils.isEmpty(channelStatusMapStr)){
            channelList.forEach(channelEntity -> {
                channelStatusVOList.add(new ChannelStatusVO(channelEntity.getId(),false));
            });
            return Result.ok(channelStatusVOList);
        }
        Map<Long,Boolean> channelStatusMap = JSONObject.parseObject(channelStatusMapStr, Map.class);
        channelList.forEach(channelEntity -> {
            if(channelStatusMap.get(channelEntity.getId()) != null && channelStatusMap.get(channelEntity.getId())){
                channelStatusVOList.add(new ChannelStatusVO(channelEntity.getId(),true));
            }else {
                channelStatusVOList.add(new ChannelStatusVO(channelEntity.getId(),false));
            }
        });
        return Result.ok(channelStatusVOList);
    }

    @Override
//    @GatewayChange(value = "#edgeGatewayId",state = true)
    @AuditLog(action = ActionEnum.UPDATE,target = AuditTargetEnum.CHANNEL, details = "批量修改通道")
    public Result<Void> batchEditChannel(TenantIsolation tenantIsolation, ChannelRequestBo requestBo, Long edgeGatewayId) {
        if (CollectionUtil.isEmpty(requestBo.getIds()) || (Objects.isNull(requestBo.getStatus()) && Objects.isNull(requestBo.getIntervalMs())) || Objects.isNull(tenantIsolation) || Objects.isNull(tenantIsolation.getTenantId())) {
            return Result.ok();
        }
        new LambdaUpdateChainWrapper<>(channelMapper)
                .set(Objects.nonNull(requestBo.getStatus()),ChannelEntity::getStatus,requestBo.getStatus())
                .set(Objects.nonNull(requestBo.getIntervalMs()),ChannelEntity::getIntervalMs,requestBo.getIntervalMs())
                .eq(ChannelEntity::getTenantId,tenantIsolation.getTenantId())
                .eq(ChannelEntity::getEdgeGatewayId,edgeGatewayId)
                .in(ChannelEntity::getId,requestBo.getIds())
                .update();
        edgeGatewayService.setNotSyncById(edgeGatewayId);
        return Result.ok();
    }

    private Map<String,String> buildChannelParamMap(List<ChannelParamDTO> channelParamDTOList){
        Map<String,String> channelParamMap = new HashMap<>();
        channelParamDTOList.stream().forEach(channelParam->{
            channelParamMap.put(channelParam.getChannelId() + ":" + channelParam.getName(),channelParam.getValue());
        });
        return channelParamMap;
    }
    
    private Result<Void> uniqueName(String name, TenantIsolation tenantIsolation) {
        return this.uniqueName(null, name, tenantIsolation);
    }
    
    private Result<Void> uniqueNameInEdgeGateway(String name, Long edgeGatewayId,Long tenantId) {
        return this.uniqueNameInEdgeGateway(null, name, edgeGatewayId,tenantId);
    }

    /**
     * 判断名称是否唯一
     *
     * @param id
     * @param name
     * @param tenantIsolation
     * @return
     */
    private Result<Void> uniqueName(Long id, String name, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<ChannelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(id != null, ChannelEntity::getId, id)
                .eq(ChannelEntity::getName, name)
                .eq(ChannelEntity::getTenantId, tenantIsolation.getTenantId());

        if (channelMapper.selectCount(lqw) > 0) {
            return Result.error("已经存在该名称的通道,名称：" + name);
        }

        return Result.ok();
    }
    
    
    /**
     * 判断网关下名称是否唯一
     *
     * @param id
     * @param name
     * @param edgeGatewayId
     * @return
     */
    private Result<Void> uniqueNameInEdgeGateway(Long id, String name, Long edgeGatewayId,Long tenantId) {
        LambdaQueryWrapper<ChannelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(id != null, ChannelEntity::getId, id)
            .eq(ChannelEntity::getName, name)
            .eq(ChannelEntity::getTenantId, tenantId)
            .eq(ChannelEntity::getDeleted, 0)
            .eq(ChannelEntity::getEdgeGatewayId, edgeGatewayId);
        if (channelMapper.selectCount(lqw) > 0) {
            return Result.error("该网关下已经存在该名称的通道,名称：" + name);
        }
        return Result.ok();
    }

    @Override
    public Result<ChannelDebugInfoVo> getChannelDebugInfo(Long id, TenantIsolation tenantIsolation) {
        ChannelEntity channelEntity = this.getByIdAndTenantIsolation(id, tenantIsolation).getResult();
        if (channelEntity == null) {
            return Result.ok();
        }
        
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());
        Result<Channel> channelResult = Channel.checkInfo(channelEntity, commonFetcher);
        if(!channelResult.getSignal()){
            return Result.error(channelResult.getMessage());
        }

        Channel channel = channelResult.getResult();

        Result<ChannelDebugInfoVo> infoResult = channel.checkDebugInfo(commonFetcher);
        if(!infoResult.getSignal()){
            return Result.error(infoResult.getMessage());
        }

        ChannelDebugInfoVo info = infoResult.getResult();
        info.setMqttWsHost(wsHost);
        info.setMqttWsPort(wsPort);
        info.setMqttWsUrl(wsUrl);
        info.setMqttUsername(mqttUsername);
        info.setMqttPassword(mqttPassword);

        String sendTopic = ThirdTopic.createSubscribeSendTopic(
            tenantIsolation.getTenantId(), 
            channel.getEdgeGatewayId(), 
            channel.getId()
        );
        info.setSendTopic(sendTopic);

        String receiveTopic = ThirdTopic.createSubscribeReceiveTopic(
            tenantIsolation.getTenantId(), 
            channel.getEdgeGatewayId(), 
            channel.getId()
        );
        info.setReceiveTopic(receiveTopic);
        
        String autoResponseTopic = ThirdTopic.createSubscribeAutoResponseTopic(
            tenantIsolation.getTenantId(), 
            channel.getEdgeGatewayId(), 
            channel.getId()
        );
        info.setAutoResponseTopic(autoResponseTopic);

        String debugTopic = GwDebugTopic.createSubscribeDebugCustomTopic(
            tenantIsolation.getTenantId(), 
            channel.getEdgeGatewayId()
        );
        info.setDebugTopic(debugTopic);

        String sendTopicPrefix = ThirdTopic.createSendTopicPrefix(
            tenantIsolation.getTenantId(), 
            channel.getEdgeGatewayId(), 
            channel.getId()
        );
        info.setSendTopicPrefix(sendTopicPrefix);
        return Result.ok(info);
    }
    
    @Override
    public Result<List<ChannelVO>> listAllChannel() {
        List<ChannelEntity> channelEntities = new LambdaQueryChainWrapper<>(channelMapper).list();
        List<ChannelVO> channels = BeanUtilsIntensifier.copyBeanList(channelEntities, ChannelVO.class);
        for (ChannelVO channelVO : channels) {
            channelVO.setDriverName(DriverEnum.getNameByValue(channelVO.getDriver()));
        }
        return Result.ok(channels);
    }
    
    @Override
    public Result<List<ChannelEntity>> listByEdgeGatewayId(Long edgeGatewayId,Long tenantId) {
        LambdaQueryWrapper<ChannelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ChannelEntity::getEdgeGatewayId, edgeGatewayId)
            .eq(ChannelEntity::getTenantId, tenantId)
            .eq(ChannelEntity::getDeleted,0);
        List<ChannelEntity> channelEntityList = channelMapper.selectList(lqw);
        return Result.ok(channelEntityList);
    }

    @Override
    public Result<List<ChannelEntity>> listChannelByTenantId(Long tenantId, boolean onlyOnline) {
        LambdaQueryWrapper<ChannelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(onlyOnline,ChannelEntity::getStatus, ChannelStatusEnum.ONLINE.getValue())
                .eq(ChannelEntity::getTenantId,tenantId);
        return Result.ok(channelMapper.selectList(lqw));
    }
}
