package com.nti56.nlink.product.device.server.mapper;

import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.ComputeTaskEntity;
import com.nti56.nlink.product.device.server.model.ComputeTaskBo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Set;

public interface ComputeTaskMapper extends CommonMapper<ComputeTaskEntity> {

    List<ComputeTaskBo> listComputeTask(@Param("tenantId") Long tenantId, @Param("edgeGatewayId") Long edgeGatewayId);

    @Delete("DELETE FROM compute_task WHERE device_id = #{deviceId} AND tenant_id = #{tenantId}")
    Integer deleteByDeviceId(@Param("tenantId") Long tenantId, @Param("deviceId")Long deviceId);

    @Delete({
            "<script>",
            "DELETE FROM compute_task WHERE tenant_id = #{tenantId} AND device_id in ",
            "<foreach collection='deviceIds' item='deviceId' open='(' separator=',' close=');'>",
            " #{deviceId}",
            "</foreach>",
            "</script>"
    })
    Integer batchDeleteByDeviceIds(@Param("tenantId") Long tenantId, @Param("deviceIds")Set<Long> deviceIds);
    @Update("UPDATE compute_task SET deleted=0 WHERE device_id = #{deviceId} AND deleted = 1 AND tenant_id = #{tenantId} ")
    Integer enableDeviceComputeTask(@Param("tenantId") Long tenantId, @Param("deviceId")Long deviceId);

    @Delete("DELETE FROM compute_task WHERE tenant_id = #{tenantId} AND name = #{name} ")
    Integer deleteByName(@Param("name") String name, @Param("tenantId") Long tenantId);

    @Update("UPDATE compute_task SET deleted=1 WHERE name = #{name} AND tenant_id = #{tenantId} ")
    Integer logicDeleteByName(@Param("name") String name, @Param("tenantId") Long tenantId);

    @Select("SELECT * FROM compute_task WHERE tenant_id = #{tenantId} AND name = #{name} AND deleted = 0")
    List<ComputeTaskEntity> listByName(@Param("name") String name, @Param("tenantId") Long tenantId);

    @Update("UPDATE compute_task SET deleted=0 WHERE name = #{name} AND tenant_id = #{tenantId} ")
    Integer logicRestoreByName(@Param("name")String name, @Param("tenantId") Long tenantId);

    @Update({
            "<script>",
            "UPDATE compute_task SET deleted=0 WHERE tenant_id = #{tenantId} AND device_id in ",
            "<foreach collection='deviceIds' item='deviceId' open='(' separator=',' close=');'>",
            " #{deviceId}",
            "</foreach>",
            "</script>"
    })
    Integer batchEnableDeviceComputeTask(@Param("tenantId")Long tenantId, @Param("deviceIds")List<Long> deviceIds);
}
