package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.nti56.mapper.BaseMapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.*;
import com.nti56.nlink.product.device.client.model.dto.engineering.ProductDeviceServerDataDTO;
import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.EventDpo;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.SubscriptionDpo;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.DataTypeElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.EventDefineElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.EventElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyElm;
import com.nti56.nlink.product.device.client.model.req.ListTagReq;
import com.nti56.nlink.product.device.client.model.req.TagReq;
import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import com.nti56.nlink.product.device.server.constant.Constant;
import com.nti56.nlink.product.device.server.domain.thing.channel.Channel;
import com.nti56.nlink.product.device.server.domain.thing.device.Device;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelDpo;
import com.nti56.nlink.product.device.server.domain.thing.dpo.PropertyDpo;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ServiceDpo;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.*;
import com.nti56.nlink.product.device.server.domain.thing.label.Label;
import com.nti56.nlink.product.device.server.domain.thing.label.LabelGroup;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.ServiceEntityBase;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Subscription;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.*;
import com.nti56.nlink.product.device.server.model.*;
import com.nti56.nlink.product.device.server.model.channel.dto.CreateChannelDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.EditChannelDTO;
import com.nti56.nlink.product.device.server.model.channel.vo.CreateChannelAllVO;
import com.nti56.nlink.product.device.server.model.channel.vo.EditChannelAllVO;
import com.nti56.nlink.product.device.server.model.deviceTemplate.*;
import com.nti56.nlink.product.device.server.model.label.dto.MatchPropertyLabelDto;
import com.nti56.nlink.product.device.server.service.*;
import com.nti56.nlink.product.device.server.util.FileUtil;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * <p>
 * 设备模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2023-03-07 17:33:19
 * @since JDK 1.8
 */
@Service
@Slf4j
public class DeviceTemplateServiceImpl implements IDeviceTemplateService {

    @Autowired
    DeviceTemplateMapper mapper;

    @Override
    public Result<DeviceTemplateEntity> save(@NotNull DeviceTemplateEntity entity) {
        if (mapper.insert(entity) == 1) {
            return Result.ok(entity);
        }
        return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);
    }

    @Override
    public Result<Page<DeviceTemplateEntity>> getPage(DeviceTemplateEntity entity, Page<DeviceTemplateEntity> page) {
        LambdaQueryWrapper<DeviceTemplateEntity> eq = new LambdaQueryWrapper<DeviceTemplateEntity>()
                .eq(DeviceTemplateEntity::getTenantId, entity.getTenantId())
                .like(!StringUtils.isEmpty(entity.getName()), DeviceTemplateEntity::getName, entity.getName())
                .orderByDesc(DeviceTemplateEntity::getCreateTime);
        Page<DeviceTemplateEntity> list = mapper.selectPage(page, eq);
        return Result.ok(list);
    }

    @Override
    public Result<List<DeviceTemplateEntity>> list(DeviceTemplateEntity entity) {
        log.info("查询设备模板列表:{}", JSON.toJSONString(entity));
        List<DeviceTemplateEntity> list = new LambdaQueryChainWrapper<>(mapper)
                .eq(entity.getTenantId() != null, DeviceTemplateEntity::getTenantId, entity.getTenantId())
                .eq(!StringUtils.isEmpty(entity.getName()), DeviceTemplateEntity::getName, entity.getName())
                .list();
        log.info("查询设备模板列表结果:{}", JSON.toJSONString(list));
        return Result.ok(list);
    }

    @Override
    public Result<Void> update(@NotNull DeviceTemplateEntity entity) {
        DeviceTemplateEntity one = new LambdaQueryChainWrapper<>(mapper)
                .eq(DeviceTemplateEntity::getId, entity.getId())
                .eq(DeviceTemplateEntity::getTenantId, entity.getTenantId())
                .one();
        if (ObjectUtil.isEmpty(one)) {
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        if (!StringUtils.isEmpty(entity.getName())) {
            List<DeviceTemplateEntity> list = new LambdaQueryChainWrapper<>(mapper)
                    .ne(DeviceTemplateEntity::getId, entity.getId())
                    .eq(DeviceTemplateEntity::getTenantId, entity.getTenantId())
                    .eq(DeviceTemplateEntity::getName, entity.getName()).list();
            if (CollectionUtil.isNotEmpty(list)) {
                return Result.error("模板名称重复！");
            }
            one.setName(entity.getName());
        }
        one.setDescript(entity.getDescript());
        mapper.updateById(one);
        return Result.ok();
    }

    @Override
    public Result<Void> deleteById(@NotNull Long entityId, TenantIsolation tenantIsolation) {
        Result<DeviceTemplateEntity> byId = getById(entityId, tenantIsolation);
        if (byId.getSignal() && ObjectUtil.isNotEmpty(byId.getResult())) {
            try {
                if (mapper.deleteById(entityId) > 0) {
                    CreateDeviceTemplateFileRep deviceTemplateFile = this.createDeviceTemplateFile(tenantIsolation, entityId);
                    deviceTemplateFile.getFile().delete();
                }
            } catch (Exception e) {
                log.error("删除设备模板失败：{}", e.getMessage());
                throw new BizException(ServiceCodeEnum.CODE_DELETE_FAIL);
            }
        }
        return Result.ok();
    }

    @Override
    public Result<DeviceTemplateEntity> getById(@NotNull Long entityId, TenantIsolation tenantIsolation) {
        DeviceTemplateEntity one = new LambdaQueryChainWrapper<>(mapper)
                .eq(DeviceTemplateEntity::getTenantId, tenantIsolation.getTenantId())
                .eq(DeviceTemplateEntity::getId, entityId)
                .one();
        return Result.ok(one);
    }

    @Override
    public void downloadDeviceTemplate(HttpServletResponse response, TenantIsolation tenantIsolation, Long id) {
        Result<DeviceTemplateEntity> byId = this.getById(id, tenantIsolation);
        DeviceTemplateEntity entity = byId.getResult();
        if (!byId.getSignal() || entity == null) {
            throw new BizException("该租户下不存在此设备模板");
        }
        try {
            FileUtil.exportFile(response, new File(entity.getDownloadLink()), entity.getName(), "dt");
        } catch (IOException e) {
            throw new BizException("下载失败");
        }
    }

    @Override
    public Result<Void> importDeviceTemplate(TenantIsolation tenantIsolation, MultipartFile file) {
        if (file.isEmpty()) {
            throw new BizException("文件为空");
        }
        String originalFilename = file.getOriginalFilename();
        try {
            if (!".dt".equals(originalFilename.substring(originalFilename.lastIndexOf(".")))) {
                throw new BizException("文件格式错误");
            }
        } catch (Exception e) {
            throw new BizException("文件格式错误");
        }
        String docName = originalFilename.substring(0, originalFilename.lastIndexOf("."));
        this.checkNameUniqueness(tenantIsolation.getTenantId(), docName);

        long id = IdGenerator.generateId();
        CreateDeviceTemplateFileRep templateFileRep = this.createDeviceTemplateFile(tenantIsolation, id);
        File templateFile = templateFileRep.getFile();
        try {
            boolean newFile = templateFile.createNewFile();
            if (!newFile) {
                throw new BizException("创建设备模板异常");
            }
            //把上传的文件保存至本地
            file.transferTo(templateFile);
        } catch (IOException e) {
            templateFile.delete();
            throw new BizException("文件传输异常");
        }
        DeviceTemplateInfoDTO deviceTemplateInfoDTO;

        try (FileInputStream fos = new FileInputStream(templateFile);
             GZIPInputStream gos = new GZIPInputStream(fos);
             ObjectInputStream oos = new ObjectInputStream(gos)) {

            deviceTemplateInfoDTO = (DeviceTemplateInfoDTO) oos.readObject();
        } catch (Exception e) {
            e.printStackTrace();
            templateFile.delete();
            throw new BizException("文件序列化异常");
        }
        DeviceTemplateEntity build = DeviceTemplateEntity.builder()
                .id(templateFileRep.getId())
                .name(docName)
                .generateType(1)
                .docSize(templateFile.length())
                .downloadLink(templateFile.getPath())
                .platformVersion(deviceTemplateInfoDTO.getPlatformVersion())
                .tenantId(tenantIsolation.getTenantId())
                .build();
        try {
            Result<DeviceTemplateEntity> save = this.save(build);
            if (!save.getSignal()) {
                templateFile.delete();
                throw new BizException(save.getServiceCode(), save.getMessage());
            }
        } catch (Exception e) {
            templateFile.delete();
            throw new BizException("服务器异常，请稍后重试11");
        }
        return Result.ok();
    }

    @Autowired
    @Lazy
    IDeviceService deviceService;

    @Autowired
    CommonFetcherFactory commonFetcherFactory;

    @Value("${platformVersion}")
    private String platformVersion;

    @Autowired
    ChannelMapper channelMapper;

    @Autowired
    ChannelParamMapper channelParamMapper;

    @Autowired
    LabelGroupMapper labelGroupMapper;

    @Override
    public Result<Void> buildDeviceTemplate(TenantIsolation tenantIsolation, List<Long> ids, String name, String descript) {

        this.checkNameUniqueness(tenantIsolation.getTenantId(), name);

        Result<List<DeviceEntity>> listResult = deviceService.listDeviceByIds(tenantIsolation.getTenantId(), ids);
        if (!listResult.getSignal() || CollectionUtil.isEmpty(listResult.getResult())) {
            throw new BizException(ServiceCodeEnum.DEVICE_RESOURCE_UNDEFINED);
        }
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());
        List<DeviceEntity> deviceEntities = listResult.getResult();
        Set<ThingModelEntity> thingModelEntities = new HashSet<>();
        Set<SubscriptionEntity> subscriptionEntities = new HashSet<>();
        List<DeviceServiceEntity> deviceServiceEntities = new ArrayList<>();
        Set<ServiceEntityBase> thingServiceEntities = new HashSet<>();
        List<DeviceModelInheritEntity> deviceModelInheritEntities = new ArrayList<>();
        Set<ThingModelInheritEntity> thingModelInheritEntities = new HashSet<>();
        List<LabelBindRelationEntity> labelBindRelationEntities = new ArrayList<>();
        Set<LabelEntity> labels = new HashSet<>();
        List<LabelGroupEntity> labelGroupEntities;
        List<ChannelEntity> channelEntities;
        List<ChannelParamEntity> channelParamEntities;
        Set<Long> labelGroupIds = new HashSet<>();
        Set<Long> channelIds = new HashSet<>();

        deviceEntities.forEach(device -> {
            Result<Device> deviceResult = Device.checkInfoToBindRelation(device, commonFetcher);
            if (!deviceResult.getSignal()) {
                throw new BizException(deviceResult.getServiceCode(), "设备模板还不成熟，{0}", deviceResult.getMessage());
            }
            Device result = deviceResult.getResult();
            deviceModelInheritEntities.addAll(result.getDeviceModel().getInheritRows());
            thingModelInheritEntities.addAll(result.getDeviceModel().getInherit().listAllThingModelInheritEntity());
            thingModelEntities.addAll(result.getDeviceModel().getInherit().listAllThingModelEntity());
            if (CollectionUtil.isNotEmpty(result.getRelationEntities())) {
                labelBindRelationEntities.addAll(result.getRelationEntities());
            }
            subscriptionEntities.addAll(BeanUtilsIntensifier.getSomething(result.getDeviceModel().getSubscriptions(), Subscription::getRaw));
            List<DeviceServiceEntity> selfServiceRows = result.getDeviceModel().getSelfServiceRows();
            if (CollectionUtil.isNotEmpty(selfServiceRows)) {
                deviceServiceEntities.addAll(selfServiceRows);
            }
            thingServiceEntities.addAll(BeanUtilsIntensifier.getSomething(result.getDeviceModel().getInherit().getServices(),
                    com.nti56.nlink.product.device.server.domain.thing.modelbase.Service::getRaw));
            if (!ObjectUtils.isEmpty(result.getBindChannel())) {
                channelIds.add(result.getBindChannel().getId());
                labelGroupIds.add(result.getBindGroup().getId());
            }
            result.getPropertyLabelMap().values().forEach(labelBindRelation -> {
                Label label = labelBindRelation.getLabel();
                if (ObjectUtil.isNotEmpty(label)) {
                    labelGroupIds.add(label.getLabelGroupId());
                    channelIds.add(label.getChannelId());
                    labels.add(label.getRow());
                }
            });
        });

        labelGroupEntities = new LambdaQueryChainWrapper<>(labelGroupMapper)
                .eq(LabelGroupEntity::getTenantId, tenantIsolation.getTenantId())
                .in(CollectionUtil.isNotEmpty(labelGroupIds), LabelGroupEntity::getId, labelGroupIds)
                .list();
        channelEntities = new LambdaQueryChainWrapper<>(channelMapper)
                .eq(ChannelEntity::getTenantId, tenantIsolation.getTenantId())
                .in(CollectionUtil.isNotEmpty(channelIds), ChannelEntity::getId, channelIds)
                .list();
        channelParamEntities = new LambdaQueryChainWrapper<>(channelParamMapper)
                .eq(ChannelParamEntity::getTenantId, tenantIsolation.getTenantId())
                .in(CollectionUtil.isNotEmpty(channelIds), ChannelParamEntity::getChannelId, channelIds)
                .list();
        Set<String> labelGroupNames = LabelGroup.getFastLevelGroup(labelGroupEntities);
        List<LabelGroupEntity> groupEntities = new LambdaQueryChainWrapper<>(labelGroupMapper)
                .eq(LabelGroupEntity::getTenantId, tenantIsolation.getTenantId())
                .in(CollectionUtil.isNotEmpty(channelIds), LabelGroupEntity::getChannelId, channelIds)
                .in(CollectionUtil.isNotEmpty(labelGroupNames), LabelGroupEntity::getName, labelGroupNames)
                .list();
        if (CollectionUtil.isNotEmpty(groupEntities)) {
            labelGroupEntities.addAll(groupEntities);
        }

        DeviceTemplateInfoDTO templateInfoDTO = DeviceTemplateInfoDTO.builder()
                .dataDTO(ProductDeviceServerDataDTO.builder()
                        .deviceList(BeanUtilsIntensifier.copyBeanList(deviceEntities, DeviceDTO.class))
                        .deviceServiceList(BeanUtilsIntensifier.copyBeanList(deviceServiceEntities, DeviceServiceDTO.class))
                        .thingServiceList(BeanUtilsIntensifier.copyBeanList(thingServiceEntities, ThingServiceDTO.class))
                        .subscriptionList(BeanUtilsIntensifier.copyBeanList(subscriptionEntities, SubscriptionDTO.class))
                        .thingModelList(BeanUtilsIntensifier.copyBeanList(thingModelEntities, ThingModelDTO.class))
                        .deviceModelInheritList(BeanUtilsIntensifier.copyBeanList(deviceModelInheritEntities, DeviceModelInheritDTO.class))
                        .thingModelInheritList(BeanUtilsIntensifier.copyBeanList(thingModelInheritEntities, ThingModelInheritDTO.class))
                        .labelBindRelationList(BeanUtilsIntensifier.copyBeanList(labelBindRelationEntities, LabelBindRelationDTO.class))
                        .labelList(BeanUtilsIntensifier.copyBeanList(labels, LabelDTO.class))
                        .channelList(BeanUtilsIntensifier.copyBeanList(channelEntities, ChannelDTO.class))
                        .channelParamList(BeanUtilsIntensifier.copyBeanList(channelParamEntities, ChannelParamDTO.class))
                        .labelGroupList(BeanUtilsIntensifier.copyBeanList(labelGroupEntities, LabelGroupDTO.class))
                        .build())
                .platformVersion(platformVersion)
                .build();
        long id = IdGenerator.generateId();
        CreateDeviceTemplateFileRep templateFile = createDeviceTemplateFile(tenantIsolation, id);

        try (FileOutputStream fos = new FileOutputStream(templateFile.getFile());
             GZIPOutputStream gos = new GZIPOutputStream(fos);
             ObjectOutputStream oos = new ObjectOutputStream(gos)) {

            oos.writeObject(templateInfoDTO);
            oos.flush();
        } catch (IOException e) {
            templateFile.getFile().delete();
            log.error("创建设备模板异常：{}", e.getMessage());
            throw new BizException("创建设备模板异常");
        }
        DeviceTemplateEntity build = DeviceTemplateEntity.builder()
                .tenantId(tenantIsolation.getTenantId())
                .platformVersion(platformVersion)
                .id(id)
                .name(name)
                .descript(descript)
                .downloadLink(templateFile.getFile().getPath())
                .generateType(0)
                .docSize(templateFile.getFile().length())
                .build();
        if (!(mapper.insert(build) == 1)) {
            log.error("设备模板存储异常");
            templateFile.getFile().delete();
            throw new BizException("设备模板存储异常");
        }
        return Result.ok();
    }

    @Override
    public Result<DeviceTemplateBo> getTemplateInfo(TenantIsolation tenantIsolation, Long templateId) {
        DeviceTemplateInfoDTO deviceTemplateInfoDTO = this.getTemplateById(tenantIsolation, templateId);
        DeviceTemplateBo build = DeviceTemplateBo.builder().id(templateId).name(deviceTemplateInfoDTO.getName()).build();
        this.dataConversion(build, deviceTemplateInfoDTO, "out", null, true);
        return Result.ok(build);

    }

    private DeviceTemplateInfoDTO getTemplateById(TenantIsolation tenantIsolation, Long templateId) {
        Result<DeviceTemplateEntity> byId = this.getById(templateId, tenantIsolation);
        if (!byId.getSignal() || ObjectUtil.isEmpty(byId.getResult())) {
            throw new BizException("设备模板已被删除，请重新选择");
        }
        DeviceTemplateEntity entity = byId.getResult();
        CreateDeviceTemplateFileRep templateFileRep = this.createDeviceTemplateFile(tenantIsolation, templateId);
        File templateFile = templateFileRep.getFile();
        DeviceTemplateInfoDTO deviceTemplateInfoDTO;
        try (FileInputStream fos = new FileInputStream(templateFile);
             GZIPInputStream gos = new GZIPInputStream(fos);
             ObjectInputStream oos = new ObjectInputStream(gos)) {
            deviceTemplateInfoDTO = (DeviceTemplateInfoDTO) oos.readObject();
        } catch (Exception e) {
            log.error("设备模板序列化异常：{}", e.getMessage());
            throw new BizException("设备模板损坏，请联系模板管理者修复");
        }
        deviceTemplateInfoDTO.setName(entity.getName());
        return deviceTemplateInfoDTO;
    }

    @Autowired
    IEdgeGatewayService edgeGatewayService;

    @Override
    @Transactional
    public Result<DeviceTemplateBo> createByTemplate(TenantIsolation tenantIsolation, Long templateId, Long edgeGatewayId, DeviceTemplateBo requestBo) {

        List<EdgeGatewayEntity> edgeGatewayEntityList = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId()).list("id", edgeGatewayId, EdgeGatewayEntity.class);
        if (CollectionUtil.isEmpty(edgeGatewayEntityList) || edgeGatewayEntityList.size() != 1) {
            log.error("通过模板创建设备，所选网关不存在id：{},tenant:{}", edgeGatewayId, tenantIsolation.getTenantId());
            throw new BizException("所选网关不存在！");
        }
        DeviceTemplateInfoDTO template = this.getTemplateById(tenantIsolation, templateId);
        this.dataConversion(requestBo, template, "in", edgeGatewayId, false);
        this.doCreate(tenantIsolation, template);
        this.dataConversion(requestBo, template, "out", null, false);
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());
        checkInfo(template.getDataDTO(), commonFetcher, requestBo);
        return Result.ok(requestBo);
    }

    @Lazy
    @Autowired
    private IChannelService channelService;

    @Autowired
    IDeviceStatusManagementService deviceStatusManagementService;

    @Override
    public DeviceTemplateRespondBo batchUpdate(TenantIsolation tenantIsolation, DeviceTemplateBo requestBo) {
        DeviceTemplateRespondBo deviceTemplateRespondBo = new DeviceTemplateRespondBo();
        if (CollectionUtil.isNotEmpty(requestBo.getDeviceList())) {
            Result<List<DeviceRespondBo>> result = deviceBatchUpdate(tenantIsolation, requestBo.getDeviceList());
            deviceTemplateRespondBo.setDeviceList(result.getResult());
        }
        if (CollectionUtil.isNotEmpty(requestBo.getChannelList())) {
            List<ChannelRespondBo> channelList = new ArrayList<>();
            deviceTemplateRespondBo.setChannelList(channelList);
            requestBo.getChannelList().forEach(channelDTO -> {
                try {
                    ChannelEntity result = channelService.getByIdAndTenantIsolation(channelDTO.getId(), tenantIsolation).getResult();
                    if (ObjectUtil.isNotEmpty(result)) {
                        result.setName(channelDTO.getName());
                        result.setDescript(channelDTO.getDescript());
                        EditChannelDTO editChannelDTO = BeanUtilsIntensifier.copyBean(result, EditChannelDTO.class);
                        editChannelDTO.setChannelParamList(channelDTO.getChannelParamList());
                        Result<EditChannelAllVO> editChannelAllVOResult = channelService.editChannel(editChannelDTO, tenantIsolation);
                        if (!editChannelAllVOResult.getSignal()) {
                            ChannelRespondBo build = ChannelRespondBo.builder()
                                    .id(channelDTO.getId())
                                    .name(channelDTO.getName())
                                    .result(Result.error(editChannelAllVOResult.getServiceCode(), editChannelAllVOResult.getMessage()))
                                    .build();
                            channelList.add(build);
                        }
                    } else {
                        ChannelRespondBo build = ChannelRespondBo.builder()
                                .id(channelDTO.getId())
                                .name(channelDTO.getName())
                                .result(Result.error("通道不存在！"))
                                .build();
                        channelList.add(build);
                    }
                } catch (BizException e) {
                    ChannelRespondBo build = ChannelRespondBo.builder()
                            .id(channelDTO.getId())
                            .name(channelDTO.getName())
                            .result(Result.error(e.getCode(), e.getMessage()))
                            .build();
                    channelList.add(build);
                }
            });
        }
        return deviceTemplateRespondBo;
    }

    @Override
    public DeviceTemplateRespondBo batchDelete(TenantIsolation tenantIsolation, DeviceTemplateBo requestBo) {
        DeviceTemplateRespondBo deviceTemplateRespondBo = new DeviceTemplateRespondBo();
        if (CollectionUtil.isNotEmpty(requestBo.getDeviceList())) {
            List<Long> deviceIds = BeanUtilsIntensifier.getIds(requestBo.getDeviceList(), DeviceRequestBo::getId);
            deviceStatusManagementService.deviceBatchOffline(tenantIsolation, deviceIds);
            Result<List<DeviceRespondBo>> result = deviceService.deviceBatchDelete(tenantIsolation, deviceIds);
            List<DeviceRespondBo> collect = result.getResult().stream().filter(deviceRespondBo -> !deviceRespondBo.getResult().getSignal()).collect(Collectors.toList());
            deviceTemplateRespondBo.setDeviceList(collect);
        }
        if (CollectionUtil.isNotEmpty(requestBo.getChannelList())) {
            List<Long> channelIds = BeanUtilsIntensifier.getIds(requestBo.getChannelList(), EditChannelDTO::getId);
            List<ChannelRespondBo> channelList = new ArrayList<>();
            deviceTemplateRespondBo.setChannelList(channelList);
            channelIds.forEach(id -> {
                try {
                    channelService.deleteChannel(id, tenantIsolation);
                } catch (BizException e) {
                    ChannelRespondBo build = ChannelRespondBo.builder()
                            .id(id)
                            .result(Result.error(e.getCode(), e.getMessage()))
                            .build();
                    channelList.add(build);
                }
            });
        }
        return deviceTemplateRespondBo;
    }

    @Value("${file.download-url-prefix: /fileDownload/}")
    private String downloadUrlPrefix;

    @Value("${file.upload-path-prefix: /opt/deviceTemplate/tagExcel/}")
    private String uploadPathPrefix;

    @Override
    public Result<Void> buildTemplate(TenantIsolation tenantIsolation, DeviceTemplateBuildDTO dto, MultipartFile file) {
        // 校验模板是否重名
        this.checkNameUniqueness(tenantIsolation.getTenantId(), dto.getName());

        List<Long> idList = dto.getThingModelIdList();
        if (CollectionUtil.isEmpty(idList)) {
            return Result.error("请选择物模型！");
        }
        List<com.nti56.nlink.product.device.server.model.label.dto.LabelDTO> labelList = dto.getLabelList();
        // 过滤掉无标签分组
        List<com.nti56.nlink.product.device.server.model.label.dto.LabelDTO> filterLabelList = labelList.stream().filter(i -> {
            String name = i.getName();
            if (StrUtil.isBlank(name)) {
                return false;
            }
            if (name.lastIndexOf(LabelGroup.LABEL_GROUP_GRADE_SYMBOL) == -1) {
                i.setName("default." + name);
                return true;
            }
            return true;
        }).collect(Collectors.toList());
        List<String> groupNameSet = filterLabelList.stream().map(i -> i.getName().substring(0, i.getName().lastIndexOf(LabelGroup.LABEL_GROUP_GRADE_SYMBOL))).distinct().collect(Collectors.toList());
        // 判断本次导入的标签是否都在一个分组之下
        if (groupNameSet.size() != 1) {
            throw new BizException("导入的标签不在同一个分组之下!");
        }
        // 校验标签分组名称是否合法
        Result<Void> checkLabelGroupLevelName = RegexUtil.checkLabelGroupLevelName(groupNameSet.get(0));
        if (!checkLabelGroupLevelName.getSignal()) {
            throw new BizException(checkLabelGroupLevelName.getMessage());
        }
        // 覆盖重复标签
        Map<String, List<com.nti56.nlink.product.device.server.model.label.dto.LabelDTO>> labelCollect = filterLabelList.stream().collect(Collectors.groupingBy(i -> i.getName()));
        List<com.nti56.nlink.product.device.server.model.label.dto.LabelDTO> labelDistinctList = labelCollect.entrySet().stream().map(i -> i.getValue().get(0)).collect(Collectors.toList());


        if (!RegexUtil.checkLabelGroupLevelNames(groupNameSet).getSignal()) {
            throw new BizException(ServiceCodeEnum.LABEL_GROUP_LEVEL_NAME_ERROR);
        }
        DriverEnum driverEnum = DriverEnum.typeOfValue(dto.getDriver());
        // 校验标签与通道协议是否合法
        for (com.nti56.nlink.product.device.server.model.label.dto.LabelDTO labelDTO : labelDistinctList) {
            LabelEntity labelEntity = BeanUtilsIntensifier.copyBean(labelDTO, LabelEntity.class);
            labelEntity.setName(labelDTO.getName().substring(labelDTO.getName().lastIndexOf(LabelGroup.LABEL_GROUP_GRADE_SYMBOL) + 1));
            Result<Label> labelResult = Label.checkInfoToBase(labelEntity, driverEnum);
            if (!labelResult.getSignal()) {
                throw new BizException(labelResult.getServiceCode(), labelResult.getMessage() + ",labelName:{0}", labelEntity.getName());
            }
        }

        ChannelDTO channelDTO = new ChannelDTO();
        channelDTO.setDriver(dto.getDriver());

        List<ThingModelDto> thingModelList = new ArrayList<>();
        idList.forEach(id -> {
            Result<ThingModelDto> thingModelById = thingModelService.getThingModelById(tenantIsolation, id);
            if (!thingModelById.getSignal()) {
                throw new BizException(thingModelById.getMessage());
            }
            thingModelList.add(thingModelById.getResult());
        });
        List<ThingServiceDTO> serviceDTOList = new ArrayList<>();
        List<SubscriptionDTO> subscriptionDTOList = new ArrayList<>();
        List<ThingModelDTO> thingModelDTOList = new ArrayList<>();
        List<ThingModelInheritDTO> thingModelInheritDTOList = new ArrayList<>();
        List<String> inheritThingModelName = new ArrayList<>();
        List<String> inheritThingModelIdList = new ArrayList<>();
        for (ThingModelDto thingModelDto : thingModelList) {
            // 拼接继承物模型信息;
            for (Long inheritThingModelId : thingModelDto.getInheritThingModelIds()) {
                ThingModelInheritDTO modelInheritDTO = new ThingModelInheritDTO();
                modelInheritDTO.setInheritThingModelId(inheritThingModelId);
                modelInheritDTO.setThingModelId(thingModelDto.getId());
                thingModelInheritDTOList.add(modelInheritDTO);
            }
            inheritThingModelName.add(thingModelDto.getName());
            inheritThingModelIdList.add(thingModelDto.getId().toString());

            // 拼接物模型本身属性信息
            ThingModelDTO modelDTO = new ThingModelDTO();
            BeanUtils.copyProperties(thingModelDto, modelDTO, "model");
            ModelField modelField = new ModelField();
            ModelFieldDto model = thingModelDto.getModel();
            // 拼接物模型继承的属性信息
            List<ModelDpo> modelContainsInherits = thingModelDto.getModelContainsInherits();
            for (ModelDpo modelContainsInherit : modelContainsInherits) {
                List<EventElm> eventElms = new ArrayList<>();
                for (EventDpo eventDpo : modelContainsInherit.getEvents()) {
                    EventElm eventElm = BeanUtilsIntensifier.copyBean(eventDpo, EventElm.class);
                    EventDefineElm eventDefineElm = BeanUtilsIntensifier.copyBean(eventDpo.getEventDefine(), EventDefineElm.class);
                    eventElm.setEventDefine(eventDefineElm);
                    eventElms.add(eventElm);
                }
                if (CollectionUtil.isNotEmpty(eventElms)) {
                    model.getEvents().addAll(eventElms);
                }
                List<PropertyElm> propertyElms = new ArrayList<>();
                for (PropertyDpo property : modelContainsInherit.getProperties()) {
                    PropertyElm propertyElm = BeanUtilsIntensifier.copyBean(property, PropertyElm.class);
                    DataTypeElm dataTypeElm = BeanUtilsIntensifier.copyBean(property.getDataType(), DataTypeElm.class);
                    propertyElm.setDataType(dataTypeElm);
                    propertyElms.add(propertyElm);
                }
                if (CollectionUtil.isNotEmpty(propertyElms)) {
                    model.getProperties().addAll(propertyElms);
                }
                modelContainsInherit.getServices().forEach(item->item.setThingModelId(thingModelDto.getId()));
                serviceDTOList.addAll(BeanUtilsIntensifier.copyBeanList(modelContainsInherit.getServices(), ThingServiceDTO.class));
                modelContainsInherit.getSubscriptions().forEach(item->item.setDirectlyModelId(thingModelDto.getId()));
                subscriptionDTOList.addAll(BeanUtilsIntensifier.copyBeanList(modelContainsInherit.getSubscriptions(), SubscriptionDTO.class));
            }
            modelField.setEvents(model.getEvents());
            modelField.setProperties(model.getProperties());
            modelDTO.setModel(modelField);

            thingModelDTOList.add(modelDTO);
            // 拼接物模型服务和订阅
            serviceDTOList.addAll(BeanUtilsIntensifier.copyBeanList(model.getServices(), ThingServiceDTO.class));
            subscriptionDTOList.addAll(BeanUtilsIntensifier.copyBeanList(model.getSubscriptions(), SubscriptionDTO.class));
        }

        DeviceTemplateInfoDTO templateInfoDTO = DeviceTemplateInfoDTO.builder()
                .dataDTO(ProductDeviceServerDataDTO.builder()
                        .thingServiceList(serviceDTOList)
                        .subscriptionList(subscriptionDTOList)
                        .thingModelList(thingModelDTOList)
                        .thingModelInheritList(thingModelInheritDTOList)
                        .labelList(BeanUtilsIntensifier.copyBeanList(labelDistinctList, com.nti56.nlink.product.device.client.model.dto.LabelDTO.class))
                        .channelList(Lists.newArrayList(channelDTO))
                        .build())
                .platformVersion(platformVersion)
                .build();
        long id = IdGenerator.generateId();
        CreateDeviceTemplateFileRep templateFile = createDeviceTemplateFile(tenantIsolation, id);

        try (FileOutputStream fos = new FileOutputStream(templateFile.getFile());
             GZIPOutputStream gos = new GZIPOutputStream(fos);
             ObjectOutputStream oos = new ObjectOutputStream(gos)) {

            oos.writeObject(templateInfoDTO);
            oos.flush();
        } catch (IOException e) {
            templateFile.getFile().delete();
            log.error("创建设备模板异常：{}", e.getMessage());
            throw new BizException("创建设备模板异常");
        }
        // 上传标签文件
        File tempDir = new File("/opt/deviceTemplate/TagExcel", id + "");
        if (!tempDir.exists()) {
            boolean mkdirs = tempDir.mkdirs();
            if (!mkdirs) {
                throw new BizException("创建设备模板标签存放目录异常");
            }
        }
        String url = "/tagDownload/" +  id + "/" +file.getOriginalFilename();
        File saveFile = new File(tempDir,file.getOriginalFilename());
        try {
            file.transferTo(saveFile);
        } catch (IllegalStateException e) {
            log.error("file transfer error:{}",e);
        } catch (IOException e) {
            log.error("file transfer IOException:{}",e);
        }
        DeviceTemplateEntity build = DeviceTemplateEntity.builder()
                .tenantId(tenantIsolation.getTenantId())
                .platformVersion(platformVersion)
                .id(id)
                .name(dto.getName())
                .descript(dto.getDescript())
                .inheritThingModelName(String.join(",", inheritThingModelName))
                .inheritThingModelId(String.join(",", inheritThingModelIdList))
                .channelType(DriverEnum.getNameByValue(dto.getDriver()))
                .channelDriver(dto.getDriver())
                .labelCount(labelDistinctList.size())
                .labelFileName(file.getOriginalFilename())
                .labelFileLink(url)
                .downloadLink(templateFile.getFile().getPath())
                .generateType(0)
                .docSize(templateFile.getFile().length())
                .build();
        if (!(mapper.insert(build) == 1)) {
            log.error("设备模板存储异常");
            templateFile.getFile().delete();
            throw new BizException("设备模板存储异常");
        }
        return Result.ok();
    }

    @Override
    public Result<DeviceTemplateValidRep> valid(TenantIsolation tenantIsolation, DeviceTemplateValidReq validReq) {
        DeviceTemplateValidRep build = new DeviceTemplateValidRep();
        try {
            DeviceTemplateInfoDTO currentTemplate = this.getTemplateById(tenantIsolation, validReq.getId());
            List<String> currentThingModelNameList = currentTemplate.getDataDTO().getThingModelList().stream().map(
                    i -> i.getName()).collect(Collectors.toList());
            ChannelDTO templateChannel = Optional.ofNullable(currentTemplate.getDataDTO().getChannelList()).map(i -> i.get(0)).orElse(new ChannelDTO());
            if (ObjectUtil.isNull(templateChannel.getDriver())) {
                throw new BizException("模板中未保存协议驱动类型！");
            }
            String[] requireParamByValue = DriverEnum.getRequireParamByValue(templateChannel.getDriver());
            if (null == requireParamByValue) {
                throw new BizException("模板中保存的协议驱动类型无法匹配到必填参数信息！");
            }
            build = DeviceTemplateValidRep.builder()
                    .templateId(validReq.getId())
                    .templateName(currentTemplate.getName())
                    .templateDescript(this.getById(validReq.getId(), tenantIsolation).getResult().getDescript())
                    .driver(templateChannel.getDriver())
                    .customDriverName(templateChannel.getCustomDriverName())
                    .initChannelParamInfo(requireParamByValue)
                    .build();
            if (CollectionUtil.isNotEmpty(validReq.getSelectedList())) {
                for (Long matchTemplateId : validReq.getSelectedList()) {
                    DeviceTemplateInfoDTO matchTemplate = this.getTemplateById(tenantIsolation, matchTemplateId);
                    List<String> matchModelNameList = matchTemplate.getDataDTO().getThingModelList().stream().map(i -> i.getName()).collect(Collectors.toList());
                    if (currentThingModelNameList.stream().anyMatch(i -> matchModelNameList.contains(i))) {
                        throw new BizException("模板" + currentTemplate.getName() + "与模板" + matchTemplate.getName() + "存在同名物模型！");
                    }
                }
            }

        } catch (BizException e) {
            log.info("校验模板出现异常:{}", e);
            return Result.error(e.getMessage());
        }
        return Result.ok(build);
    }

    @Autowired
    ILabelService labelService;
    @Autowired
    LabelMapper labelMapper;
    @Autowired
    IThingModelService thingModelService;
    @Autowired
    ITagService tagService;
    @Autowired
    ITagBindRelationService tagBindRelationService;


    /**
     * 通过模板创建设备
     *
     * @param tenantIsolation
     * @param createDeviceReq
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Long> createByTemplate2(TenantIsolation tenantIsolation, DeviceTemplateCreateDeviceReq createDeviceReq) {
        long deviceId = IdGenerator.generateId();
        try {
            log.info("进入模板创建设备方法，参数：{},租户信息:{}", JSON.toJSONString(createDeviceReq), JSON.toJSONString(tenantIsolation));
            // 校验传入参数是否合法
            Result<Long> checkRes = checkCreateDeviceReq(tenantIsolation, createDeviceReq);
            if (!checkRes.getSignal()) {
                return checkRes;
            }
            // 创建设备
            DeviceEntity device = DeviceEntity.builder()
                    .id(deviceId)
                    .name(createDeviceReq.getDeviceName())
                    .status(StatusEnum.INACTIVATED.getValue())
                    .edgeGatewayId(createDeviceReq.getEdgeGatewayId())
                    .syncStatus(SyncStatusEnum.NOT_SYNC.getValue())
                    .tenantId(tenantIsolation.getTenantId())
                    .resourceId(String.valueOf(deviceId)).build();
            deviceMapper.insert(device);
            bindTag(tenantIsolation, createDeviceReq, device);
            // 初始化物模型名称列表，用于后续校验不同模板不同物模型之间的名称是否存在重复
            List<String> totalEventNameList = new ArrayList<>();
            List<String> totalPropertiesNameList = new ArrayList<>();
            List<String> totalServiceNameList = new ArrayList<>();
            List<String> totalSubscriptionNameList = new ArrayList<>();
            List<DeviceModelInheritDTO> deviceModelInheritList = new ArrayList<>();
            // 定义多模板校验通过后需要批量新增的物模型，服务，订阅和标签绑定关系集合，用于模板校验完成后的统一创建
            List<ThingModelDTO> addThingModelList = new ArrayList<>();
            List<ThingServiceDTO> addThingServiceList = new ArrayList<>();
            List<SubscriptionDTO> addSubscriptionList = new ArrayList<>();
            List<LabelBindRelationDTO> addLabelBindRelationList = new ArrayList<>();
            for (int i = 0; i < createDeviceReq.getChannelList().size(); i++) {
                DeviceTemplateChannelDto channelInfo = createDeviceReq.getChannelList().get(i);
                DeviceTemplateInfoDTO currentTemplate = this.getTemplateById(tenantIsolation, channelInfo.getTemplateId());
                ProductDeviceServerDataDTO templateDataDTO = currentTemplate.getDataDTO();
                Long channelId = createChannelAndLabel(tenantIsolation, createDeviceReq, i, channelInfo, currentTemplate, templateDataDTO);
                // 获取此租户下已有的物模型信息
                List<ThingModelEntity> thingModelEntities = thingModelMapper.selectList(Wrappers.<ThingModelEntity>lambdaQuery()
                        .and(w->w.eq(ThingModelEntity::getTenantId, tenantIsolation.getTenantId()).or().eq(ThingModelEntity::getTenantId,Constant.DEFAULT_THING))
                        .eq(ThingModelEntity::getModelType, ModelTypeEnum.THING_MODEL.getValue())
                        .eq(ThingModelEntity::getDeleted, 0));
                // 在此租户下创建物模型
                if (CollectionUtil.isNotEmpty(templateDataDTO.getThingModelList())) {
                    List<MatchPropertyLabelDto> matchPropertyLabelDtos = labelMapper.listMatchPropertyLabelDto(tenantIsolation.getTenantId(), createDeviceReq.getEdgeGatewayId(), channelId);
                    // 循环当前模板物模型信息
                    for (ThingModelDTO thingModelDTO : templateDataDTO.getThingModelList()) {
                        List<Long> inheritThingModelIdList = Optional.ofNullable(templateDataDTO.getThingModelInheritList()).orElse(new ArrayList<>()).stream().filter(item -> item.getThingModelId().equals(thingModelDTO.getId())).map(ThingModelInheritDTO::getInheritThingModelId).collect(Collectors.toList());
                        inheritThingModelIdList.add(thingModelDTO.getId());
                        // 获取物模型绑定的服务和订阅
                        List<ThingServiceDTO> templateServiceList = templateDataDTO.getThingServiceList().stream().filter(item -> inheritThingModelIdList.contains(item.getThingModelId())).collect(Collectors.toList());
                        List<SubscriptionDTO> templateSubscriptionList = templateDataDTO.getSubscriptionList().stream().filter(item -> inheritThingModelIdList.contains(item.getDirectlyModelId())).collect(Collectors.toList());
                        // 如果已存在的物模型名称包含当前模板物模型，进行重复判断  否则直接创建此物模型
                        Optional<ThingModelEntity> repeatOption = thingModelEntities.stream().filter(item -> ObjectUtil.equals(item.getName(), thingModelDTO.getName())).findFirst();
                        List<String> propertiesNameList = Optional.ofNullable(thingModelDTO.getModel()).map(ModelField::getProperties).orElse(new ArrayList<>()).stream().map(item -> item.getName()).collect(Collectors.toList());
                        List<String> eventNameList = Optional.ofNullable(thingModelDTO.getModel()).map(ModelField::getEvents).orElse(new ArrayList<>()).stream().map(item -> item.getName()).collect(Collectors.toList());
                        List<String> serviceNameList = templateServiceList.stream().map(item -> item.getServiceName()).collect(Collectors.toList());
                        List<String> subscriptionNameList = templateSubscriptionList.stream().map(item -> item.getName()).collect(Collectors.toList());
                        if (eventNameList.stream().anyMatch(item -> totalEventNameList.contains(item))) {
                            throw new BizException("物模型【" + thingModelDTO.getName() + "】与其他物模型存在同名事件!");
                        }
                        if (propertiesNameList.stream().anyMatch(item -> totalPropertiesNameList.contains(item))) {
                            throw new BizException("物模型【" + thingModelDTO.getName() + "】与其他物模型存在同名属性!");
                        }
                        if (serviceNameList.stream().anyMatch(item -> totalServiceNameList.contains(item))) {
                            throw new BizException("物模型【" + thingModelDTO.getName() + "】与其他物模型存在同名服务!");
                        }
                        if (subscriptionNameList.stream().anyMatch(item -> totalSubscriptionNameList.contains(item))) {
                            throw new BizException("物模型【" + thingModelDTO.getName() + "】与其他物模型存在同名订阅!");
                        }
                        totalEventNameList.addAll(eventNameList);
                        totalPropertiesNameList.addAll(propertiesNameList);
                        totalServiceNameList.addAll(serviceNameList);
                        totalSubscriptionNameList.addAll(subscriptionNameList);
                        if (repeatOption.isPresent()) {
                            ThingModelEntity repeatThingModel = repeatOption.get();
                            ThingModelDto thingModelById = thingModelService.getThingModelById(tenantIsolation, repeatThingModel.getId()).getResult();
                            ModelField model = new ModelField();
                            if (CollUtil.isNotEmpty(thingModelById.getModel().getProperties())){
                                model.setProperties(thingModelById.getModel().getProperties());
                            }
                            if (CollUtil.isNotEmpty(thingModelById.getModel().getEvents())){
                                model.setEvents(thingModelById.getModel().getEvents());
                            }
                            // 拼接物模型继承的属性信息
                            List<ModelDpo> modelContainsInherits = thingModelById.getModelContainsInherits();
                            for (ModelDpo modelContainsInherit : modelContainsInherits) {
                                List<EventElm> eventElms = new ArrayList<>();
                                for (EventDpo eventDpo : modelContainsInherit.getEvents()) {
                                    EventElm eventElm = BeanUtilsIntensifier.copyBean(eventDpo, EventElm.class);
                                    EventDefineElm eventDefineElm = BeanUtilsIntensifier.copyBean(eventDpo.getEventDefine(), EventDefineElm.class);
                                    eventElm.setEventDefine(eventDefineElm);
                                    eventElms.add(eventElm);
                                }
                                if (CollectionUtil.isNotEmpty(eventElms)) {
                                    if (model.getEvents() == null){
                                        model.setEvents(new ArrayList<>());
                                    }
                                    model.getEvents().addAll(eventElms);
                                }
                                List<PropertyElm> propertyElms = new ArrayList<>();
                                for (PropertyDpo property : modelContainsInherit.getProperties()) {
                                    PropertyElm propertyElm = BeanUtilsIntensifier.copyBean(property, PropertyElm.class);
                                    DataTypeElm dataTypeElm = BeanUtilsIntensifier.copyBean(property.getDataType(), DataTypeElm.class);
                                    propertyElm.setDataType(dataTypeElm);
                                    propertyElms.add(propertyElm);
                                }
                                if (CollectionUtil.isNotEmpty(propertyElms)) {
                                    if (model.getProperties() == null){
                                        model.setProperties(new ArrayList<>());
                                    }
                                    model.getProperties().addAll(propertyElms);
                                }
                            }
                            // 属性和事件通过JSON str进行对比，不完全一致则为修改过，进行报错
                            if (!ObjectUtil.equals(JSON.toJSONString(thingModelDTO.getModel()), JSON.toJSONString(model))) {
                                throw new BizException("物模型【" + repeatThingModel.getName() + "】已存在，且与模板下同名物模型的属性或事件不一致，无法通过模板创建此物模型!");
                            }
                            // 服务和订阅通过重写的equals方法进行对比，存在任意一个模板中的服务和订阅不一致  则进行报错提示
                            List<ServiceDpo> services = Optional.ofNullable(thingModelById.getFullModel()).map(item -> item.getServices()).orElse(new ArrayList<>());
                            List<ThingServiceDTO> serviceDTOList = BeanUtilsIntensifier.copyBeanList(services, ThingServiceDTO.class);
                            List<SubscriptionDpo> subscriptions = Optional.ofNullable(thingModelById.getFullModel()).map(item -> item.getSubscriptions()).orElse(new ArrayList<>());
                            List<SubscriptionDTO> subscriptionDTOList = BeanUtilsIntensifier.copyBeanList(subscriptions, SubscriptionDTO.class);
                            long serviceEqualsCount = templateServiceList.stream().filter(item -> serviceDTOList.stream().anyMatch(service -> service.equals(item))).count();
                            if (serviceEqualsCount != templateServiceList.size() || templateServiceList.size() != serviceDTOList.size()) {
                                throw new BizException("物模型【" + repeatThingModel.getName() + "】已存在，且与模板下同名物模型的物服务不一致，无法通过模板创建此物模型!");
                            }
                            long subscriptionEqualsCount = templateSubscriptionList.stream().filter(item -> subscriptionDTOList.stream().anyMatch(sub -> sub.equals(item))).count();
                            if (subscriptionEqualsCount != templateSubscriptionList.size() || templateSubscriptionList.size() != subscriptionDTOList.size()) {
                                throw new BizException("物模型【" + repeatThingModel.getName() + "】已存在，且与模板下同名物模型的订阅不一致，无法通过模板创建此物模型!");
                            }
                            log.info("物模型【" + repeatThingModel.getName() + "】已存在且完全一致，本次模板创建设备跳过此物模型的创建！");
                            DeviceModelInheritDTO deviceModelInheritDTO = new DeviceModelInheritDTO();
                            deviceModelInheritDTO.setDeviceId(deviceId);
                            deviceModelInheritDTO.setInheritThingModelId(thingModelById.getId());
                            deviceModelInheritDTO.setSortNo(0);
                            deviceModelInheritList.add(deviceModelInheritDTO);
                            // 进行标签绑定
                            bindLabel(tenantIsolation, deviceId, addLabelBindRelationList, matchPropertyLabelDtos, propertiesNameList, thingModelById.getId());
                            continue;
                        }
                        // 进入创建物模型方法
                        // 填充新增的物模型信息
                        long thingModelId = fillAddThingModel(tenantIsolation, deviceId, deviceModelInheritList, addThingModelList, addThingServiceList, addSubscriptionList, thingModelDTO, templateServiceList, templateSubscriptionList);
                        // 进行标签绑定
                        bindLabel(tenantIsolation, deviceId, addLabelBindRelationList, matchPropertyLabelDtos, propertiesNameList, thingModelId);
                    }
                }
            }
            // 保存设备信息
            ProductDeviceServerDataDTO addServerDataDto = ProductDeviceServerDataDTO.builder()
                    .deviceModelInheritList(deviceModelInheritList)
                    .thingModelList(addThingModelList)
                    .thingServiceList(addThingServiceList)
                    .subscriptionList(addSubscriptionList)
                    .labelBindRelationList(addLabelBindRelationList)
                    .thingModelInheritList(new ArrayList<>())
                    .build();
            engineeringProductService.createProductData(addServerDataDto);
        } catch (BizException e) {
            log.info("通过模板创建设备出现异常:{}", e);
            throw e;
        }
        return Result.ok(deviceId);
    }

    private void bindTag(TenantIsolation tenantIsolation, DeviceTemplateCreateDeviceReq createDeviceReq, DeviceEntity device) {
        // 如果传入客户名称不为空  进行标签绑定
        if (StrUtil.isNotBlank(createDeviceReq.getCustomerName())){
            ListTagReq req = new ListTagReq();
            req.setSearch(createDeviceReq.getCustomerName());
            Result<List<TagRsp>> tagListResult = tagService.list(req, tenantIsolation);
            Long id;
            // 不存在标记就新增一个标记
            if (CollUtil.isEmpty(tagListResult.getResult())){
                TagReq tagReq = new TagReq();
                tagReq.setTagKey(Constant.PMO_DEVICE_TAG_KEY);
                tagReq.setTagValue(createDeviceReq.getCustomerName());
                Result<TagRsp> saveRep = tagService.save(tagReq, tenantIsolation);
                if (!saveRep.getSignal()){
                    throw new BizException(saveRep.getMessage());
                }
                 id = saveRep.getResult().getId();
            }else {
                // 已存在标记  用标记id进行标记绑定
                 id = tagListResult.getResult().get(0).getId();
            }
            tagBindRelationService.saveList(device.getTenantId(), device.getId(),
                    Arrays.asList(id), ResourceTypeEnum.DEVICE);
        }
    }

    @Override
    public Result<List<DeviceTemplateThingPropertyBo>> getTemplateThingModel(TenantIsolation tenantIsolation, DeviceTemplateThingPropertyReq req) {
        if (CollUtil.isEmpty(req.getTemplateIdList())) {
            return Result.error("未传入模板id信息！");
        }
        List<DeviceTemplateThingPropertyBo> deviceTemplateThingPropertyBoList = new ArrayList<>();
        for (Long templateId : req.getTemplateIdList()) {
            DeviceTemplateInfoDTO currentTemplate = this.getTemplateById(tenantIsolation, templateId);
            Result<DeviceTemplateEntity> byId = this.getById(templateId, tenantIsolation);
            DeviceTemplateEntity entity = byId.getResult();
            ProductDeviceServerDataDTO templateDataDTO = currentTemplate.getDataDTO();
            DeviceTemplateThingPropertyBo deviceTemplateThingPropertyBo = new DeviceTemplateThingPropertyBo();
            deviceTemplateThingPropertyBo.setTemplateId(templateId);
            deviceTemplateThingPropertyBo.setTemplateName(currentTemplate.getName());
            deviceTemplateThingPropertyBo.setChannelName(Optional.ofNullable(DriverEnum.typeOfValue(entity.getChannelDriver())).map(DriverEnum::getNameDesc).orElse(entity.getChannelType()));
            deviceTemplateThingPropertyBo.setProperties(templateDataDTO.getThingModelList().stream().map(ThingModelDTO::getModel).flatMap(i -> (i.getProperties() != null ? i.getProperties() : new ArrayList<PropertyElm>()).stream()).collect(Collectors.toList()));
            deviceTemplateThingPropertyBoList.add(deviceTemplateThingPropertyBo);
        }
        return Result.ok(deviceTemplateThingPropertyBoList);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Map<Long, Long>> batchCopyTemplate(TenantIsolation tenantIsolation, DeviceTemplateThingPropertyReq req) {
        if (CollUtil.isEmpty(req.getTemplateIdList())) {
            return Result.error("未传入模板id信息！");
        }
        List<DeviceTemplateEntity> sourceList = new LambdaQueryChainWrapper<>(mapper)
                .in(DeviceTemplateEntity::getId, req.getTemplateIdList())
                .eq(DeviceTemplateEntity::getDeleted, 0)
                .list();

        List<DeviceTemplateEntity> existedList = new LambdaQueryChainWrapper<>(mapper)
                .eq(DeviceTemplateEntity::getTenantId, tenantIsolation.getTenantId())
                .in(CollUtil.isNotEmpty(sourceList), DeviceTemplateEntity::getName, sourceList.stream().map(DeviceTemplateEntity::getName).collect(Collectors.toList()))
                .eq(DeviceTemplateEntity::getDeleted, 0)
                .list();

        List<ThingModelEntity> currentTenantThingModelList = thingModelService.list(Wrappers.<ThingModelEntity>lambdaQuery()
                .and(w->w.eq(ThingModelEntity::getTenantId, tenantIsolation.getTenantId()).or().eq(ThingModelEntity::getTenantId,Constant.DEFAULT_THING))
                .eq(ThingModelEntity::getDeleted, 0)
        );
        Map<String, Long> modelMap = currentTenantThingModelList.stream().collect(Collectors.toMap(ThingModelEntity::getName, ThingModelEntity::getId));
        List<String> existNameList = existedList.stream().map(DeviceTemplateEntity::getName).collect(Collectors.toList());
        List<DeviceTemplateEntity> filterSourceList = sourceList.stream().filter(i -> !existNameList.contains(i.getName())).collect(Collectors.toList());
        Map<Long, Long> idMap = new HashMap<>();
        for (DeviceTemplateEntity sourceEntity : sourceList) {
            for (DeviceTemplateEntity exist : existedList) {
                if (ObjectUtil.equals(sourceEntity.getName(), exist.getName())) {
                    idMap.put(sourceEntity.getId(), exist.getId());
                }
            }
        }
        Map<Long, Long> thingModelIdMap = new HashMap<>();
        for (DeviceTemplateEntity deviceTemplateEntity : filterSourceList) {
            List<String> newThingModelIdList = new ArrayList<>();
            List<String> newThingModelNameList = new ArrayList<>();
            if (StrUtil.isNotBlank(deviceTemplateEntity.getInheritThingModelName())) {
                List<String> thingModelIdList = Arrays.asList(deviceTemplateEntity.getInheritThingModelId().split(","));
                List<String> thingModelNameList = Arrays.asList(deviceTemplateEntity.getInheritThingModelName().split(","));
                for (int i = 0; i < thingModelNameList.size(); i++) {
                    String name = thingModelNameList.get(i);
                    String thingModelId = thingModelIdList.get(i);
                    newThingModelNameList.add(name);
                    // 如果当前租户已存在当前物模型  则继承物模型id替换成当前租户的物模型id
                    if (modelMap.get(name) != null) {
                        newThingModelIdList.add(modelMap.get(name).toString());
                    }
//                    else {
//                        Result<Long> idResult = thingModelService.copyThingModel(tenantIsolation, Long.valueOf(thingModelId));
//                        if (idResult.getSignal()) {
//                            // 复制物模型成功，构造一个key为旧物模型的id ，value为新物模型id的map   用于后续替换
//                            thingModelIdMap.put(Long.parseLong(thingModelId), idResult.getResult());
//                            newThingModelIdList.add(idResult.getResult().toString());
//                        }
//                    }
                }
            }
            long tempId = IdGenerator.generateId();

            TenantIsolation tempTenant = new TenantIsolation();
            tempTenant.setTenantId(deviceTemplateEntity.getTenantId());
            DeviceTemplateInfoDTO templateInfoDTO = this.getTemplateById(tempTenant, deviceTemplateEntity.getId());
            // 如果带复制的模板中，存在物模型，则进行判断是否存在复制后新增的物模型，存在进行id替换，避免后续使用模板时出现问题
//            if (CollUtil.isNotEmpty(templateInfoDTO.getDataDTO().getThingModelList())) {
//                for (ThingModelDTO thingModelDTO : templateInfoDTO.getDataDTO().getThingModelList()) {
//                    if (thingModelIdMap.get(thingModelDTO.getId()) != null) {
//                        thingModelDTO.setId(thingModelIdMap.get(thingModelDTO.getId()));
//                    }
//                }
//            }
            CreateDeviceTemplateFileRep templateFile = createDeviceTemplateFile(tenantIsolation, tempId);
            try (FileOutputStream fos = new FileOutputStream(templateFile.getFile());
                 GZIPOutputStream gos = new GZIPOutputStream(fos);
                 ObjectOutputStream oos = new ObjectOutputStream(gos)) {
                oos.writeObject(templateInfoDTO);
                oos.flush();
            } catch (IOException e) {
                templateFile.getFile().delete();
                log.error("创建设备模板异常：{}", e.getMessage());
                throw new BizException("创建设备模板异常");
            }
            idMap.put(deviceTemplateEntity.getId(), tempId);
            deviceTemplateEntity.setId(tempId);
            deviceTemplateEntity.setTenantId(tenantIsolation.getTenantId());
            deviceTemplateEntity.setInheritThingModelId(StrUtil.join(",", newThingModelIdList));
            deviceTemplateEntity.setInheritThingModelName(StrUtil.join(",", newThingModelNameList));
            deviceTemplateEntity.setDownloadLink(templateFile.getFile().getPath());
            deviceTemplateEntity.setDocSize(templateFile.getFile().length());
            deviceTemplateEntity.setCreator(null);
            deviceTemplateEntity.setCreateTime(null);
            deviceTemplateEntity.setCreatorId(null);
            deviceTemplateEntity.setUpdatorId(null);
            deviceTemplateEntity.setUpdateTime(null);
            deviceTemplateEntity.setUpdator(null);
            this.save(deviceTemplateEntity);
        }
        log.info("复制设备模板反馈结果:" + JSON.toJSONString(idMap));
        return Result.ok(idMap);
    }

    /**
     * 校验通过模板创建设备入参是否合法
     *
     * @param tenantIsolation
     * @param createDeviceReq
     * @return
     */
    private Result<Long> checkCreateDeviceReq(TenantIsolation tenantIsolation, DeviceTemplateCreateDeviceReq createDeviceReq) {
        // 校验参数
        if (CollectionUtil.isEmpty(createDeviceReq.getChannelList())) {
            return Result.error("未选择模板，无法创建！");
        }
        // 判断网关是否存在
        List<EdgeGatewayEntity> edgeGatewayEntityList = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId()).list("id", createDeviceReq.getEdgeGatewayId(), EdgeGatewayEntity.class);
        if (CollectionUtil.isEmpty(edgeGatewayEntityList) || edgeGatewayEntityList.size() != 1) {
            log.error("通过模板创建设备，所选网关不存在id：{},tenant:{}", createDeviceReq.getEdgeGatewayId(), tenantIsolation.getTenantId());
            return Result.error("所选网关不存在！");
        }
        // 校验设备名称是否重复
        if (deviceMapper.selectCount(Wrappers.<DeviceEntity>lambdaQuery()
                .eq(DeviceEntity::getName, createDeviceReq.getDeviceName())
                .eq(DeviceEntity::getTenantId, tenantIsolation.getTenantId())) > 0) {
            return Result.error("已经存在该名称的设备,名称：" + createDeviceReq.getDeviceName());
        }
        return Result.ok();
    }

    private Long createChannelAndLabel(TenantIsolation tenantIsolation, DeviceTemplateCreateDeviceReq createDeviceReq, int i, DeviceTemplateChannelDto channelInfo, DeviceTemplateInfoDTO currentTemplate, ProductDeviceServerDataDTO templateDataDTO) {
        DriverEnum driverEnum = DriverEnum.typeOfValue(channelInfo.getDriver());
        if (driverEnum == null) {
            throw new BizException("驱动类型【" + channelInfo.getDriver() + "】不存在");
        }
        // 创建通道
        CreateChannelDTO createChannelDTO = CreateChannelDTO.builder()
                // 通道名称规则  设备名_驱动类型_所属模板列表序号
                .name(createDeviceReq.getDeviceName() + "_" + driverEnum.getName() + "_" + String.format("%0" + 2 + "d", i + 1))
                .driver(channelInfo.getDriver())
                .isServer(false)
                .descript("通过模板" + currentTemplate.getName() + "创建出的通道")
                .edgeGatewayId(createDeviceReq.getEdgeGatewayId())
                .channelParamList(channelInfo.getChannelParamList())
                .build();
        Result<CreateChannelAllVO> channelCreateResult = channelService.createChannel(createChannelDTO, tenantIsolation);
        if (!channelCreateResult.getSignal()) {
            // 出现创建通道异常进行抛错  执行事务回滚
            throw new BizException(channelCreateResult.getMessage());
        }
        Long channelId = channelCreateResult.getResult().getChannelId();
        // 导入标签文件
        List<LabelDTO> labelList = templateDataDTO.getLabelList();
        List<com.nti56.nlink.product.device.server.model.label.dto.LabelDTO> labelDTOS = BeanUtilsIntensifier.copyBeanList(labelList, com.nti56.nlink.product.device.server.model.label.dto.LabelDTO.class);
        Result<Void> labelBatchInputResult = labelService.labelBatchInput(tenantIsolation, channelId, labelDTOS);
        if (!labelBatchInputResult.getSignal()) {
            // 出现导入标签文件异常进行抛错 执行事务回滚
            throw new BizException(labelBatchInputResult.getMessage());
        }
        return channelId;
    }

    private long fillAddThingModel(TenantIsolation tenantIsolation, long deviceId, List<DeviceModelInheritDTO> deviceModelInheritList, List<ThingModelDTO> addThingModelList, List<ThingServiceDTO> addThingServiceList, List<SubscriptionDTO> addSubscriptionList, ThingModelDTO thingModelDTO, List<ThingServiceDTO> templateServiceList, List<SubscriptionDTO> templateSubscriptionList) {
        // 填充待添加的物模型信息
        long thingModelId = IdGenerator.generateId();
        thingModelDTO.setId(thingModelId);
        thingModelDTO.setModelType(ModelTypeEnum.THING_MODEL.getValue());
        thingModelDTO.setTenantId(tenantIsolation.getTenantId());
        addThingModelList.add(thingModelDTO);
        // 填充待添加的设备继承物模型信息
        DeviceModelInheritDTO deviceModelInheritDTO = new DeviceModelInheritDTO();
        deviceModelInheritDTO.setDeviceId(deviceId);
        deviceModelInheritDTO.setInheritThingModelId(thingModelId);
        deviceModelInheritDTO.setSortNo(0);
        deviceModelInheritList.add(deviceModelInheritDTO);
        // 填充待添加的服务信息
        templateServiceList.forEach(item -> {
            item.setId(IdGenerator.generateId());
            item.setThingModelId(thingModelId);
            item.setTenantId(tenantIsolation.getTenantId());
        });
        addThingServiceList.addAll(templateServiceList);
        // 填充待添加的订阅信息
        templateSubscriptionList.forEach(item -> {
            item.setId(IdGenerator.generateId());
            item.setDirectlyModelId(thingModelId);
            item.setTenantId(tenantIsolation.getTenantId());
        });
        addSubscriptionList.addAll(templateSubscriptionList);
        return thingModelId;
    }

    private void bindLabel(TenantIsolation tenantIsolation, long deviceId, List<LabelBindRelationDTO> addLabelBindRelationList, List<MatchPropertyLabelDto> matchPropertyLabelDtos, List<String> propertiesNameList, long thingModelId) {
        for (String propertiesName : propertiesNameList) {
            for (MatchPropertyLabelDto matchPropertyLabelDto : matchPropertyLabelDtos) {
                if (ObjectUtil.equals(propertiesName, matchPropertyLabelDto.getLabelName())) {
                    LabelBindRelationDTO bindRelationDTO = LabelBindRelationDTO.builder()
                            .id(IdGenerator.generateId())
                            .directlyModelId(thingModelId)
                            .modelType(1)
                            .labelId(matchPropertyLabelDto.getLabelId())
                            .deviceId(deviceId)
                            .propertyName(propertiesName)
                            .tenantId(tenantIsolation.getTenantId())
                            .labelGroupName(matchPropertyLabelDto.getLabelGroupName())
                            .channelName(matchPropertyLabelDto.getChannelName())
                            .labelName(matchPropertyLabelDto.getLabelName())
                            .edgeGatewayId(matchPropertyLabelDto.getEdgeGatewayId())
                            .build();
                    addLabelBindRelationList.add(bindRelationDTO);
                }
            }
        }
    }


    private Result<List<DeviceRespondBo>> deviceBatchUpdate(TenantIsolation tenantIsolation, List<DeviceRequestBo> deviceList) {
        List<Long> ids = BeanUtilsIntensifier.getIds(deviceList, DeviceRequestBo::getId);
        List<DeviceEntity> list1 = new LambdaQueryChainWrapper<>(deviceMapper).eq(DeviceEntity::getTenantId, tenantIsolation.getTenantId()).in(CollectionUtil.isNotEmpty(ids), DeviceEntity::getId, ids).list();
        List<DeviceEntity> nameList = new LambdaQueryChainWrapper<>(deviceMapper)
                .eq(DeviceEntity::getTenantId, tenantIsolation.getTenantId())
                .in(DeviceEntity::getName, BeanUtilsIntensifier.getSomething(deviceList, DeviceRequestBo::getName))
                .list();
        Map<String, DeviceEntity> nameMap = BeanUtilsIntensifier.collection2Map(nameList, DeviceEntity::getName);
        Map<Long, DeviceEntity> map = BeanUtilsIntensifier.collection2Map(list1, DeviceEntity::getId);
        List<DeviceRespondBo> list = new ArrayList<>();
        deviceList.forEach(device -> {
            if (!map.containsKey(device.getId())) {
                list.add(DeviceRespondBo.builder()
                        .id(device.getId())
                        .name(device.getName())
                        .result(Result.error(ServiceCodeEnum.CODE_PARAM_ERROR))
                        .build());
                return;
            }
            if (nameMap.containsKey(device.getName()) && !nameMap.get(device.getName()).getId().equals(device.getId())) {
                list.add(DeviceRespondBo.builder()
                        .id(device.getId())
                        .name(device.getName())
                        .result(Result.error("设备名称重复！"))
                        .build());
                return;
            }
            DeviceEntity deviceEntity = map.get(device.getId());
            deviceEntity.setName(device.getName());
            deviceEntity.setDescript(device.getDescript());

            try {
                deviceService.updateDeviceName(deviceEntity);
            } catch (BizException e) {
                list.add(DeviceRespondBo.builder()
                        .id(device.getId())
                        .name(device.getName())
                        .result(Result.error(e.getCode(), e.getMessage()))
                        .build());
            } catch (Exception e) {
                log.error("设备更新名字报错,id:{},msg:{}", deviceEntity.getId(), e.getMessage());
                list.add(DeviceRespondBo.builder()
                        .id(device.getId())
                        .name(device.getName())
                        .result(Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL))
                        .build());
            }
        });
        return Result.ok(list);
    }

    @Autowired
    ThingServiceMapper thingServiceMapper;

    @Autowired
    SubscriptionMapper subscriptionMapper;

    @Autowired
    ThingModelMapper thingModelMapper;

    @Autowired
    IEngineeringProductService engineeringProductService;

    @Autowired
    DeviceMapper deviceMapper;

    private void doCreate(TenantIsolation tenantIsolation, DeviceTemplateInfoDTO template) {
        List<String> deviceNames = BeanUtilsIntensifier.getSomething(template.getDataDTO().getDeviceList(), DeviceDTO::getName);
        this.checkNameUniqueness(deviceNames, deviceMapper, DeviceEntity.class, DeviceEntity::getName, DeviceEntity::getTenantId, tenantIsolation.getTenantId());

        List<String> channelNames = BeanUtilsIntensifier.getSomething(template.getDataDTO().getChannelList(), ChannelDTO::getName);
        this.checkNameUniqueness(channelNames, channelMapper, ChannelEntity.class, ChannelEntity::getName, ChannelEntity::getTenantId, tenantIsolation.getTenantId());

        List<String> thingModelNames = BeanUtilsIntensifier.getSomething(template.getDataDTO().getThingModelList(), ThingModelDTO::getName);
        List<ThingModelEntity> thingModelEntities = new LambdaQueryChainWrapper<>(thingModelMapper)
                .and(w->w.eq(ThingModelEntity::getTenantId, tenantIsolation.getTenantId()).or().eq(ThingModelEntity::getTenantId,Constant.DEFAULT_THING))
                .in(ThingModelEntity::getName, thingModelNames).list();
        Map<String, Long> thingModelNameMap = BeanUtilsIntensifier.collection2Map(thingModelEntities, ThingModelEntity::getName, ThingModelEntity::getId);
        this.idDataConversion(tenantIsolation, template, thingModelNameMap);
        engineeringProductService.createProductData(template.getDataDTO());
    }

    private void checkInfo(ProductDeviceServerDataDTO dataDTO, CommonFetcher commonFetcher, DeviceTemplateBo requestBo) {
        List<ChannelEntity> channelEntities = BeanUtilsIntensifier.copyBeanList(dataDTO.getChannelList(), ChannelEntity.class);
        channelEntities.forEach(channel -> {
            Result<Channel> channelResult = Channel.checkInfo(channel, commonFetcher);
            if (!channelResult.getSignal()) {
                throw new BizException(channelResult);
            }
        });
        List<DeviceEntity> deviceEntities = BeanUtilsIntensifier.copyBeanList(dataDTO.getDeviceList(), DeviceEntity.class);
        deviceEntities.forEach(device -> {
            Result<Device> deviceResult = Device.checkInfoToBindRelation(device, commonFetcher);
            if (!deviceResult.getSignal()) {
                throw new BizException(deviceResult);
            }
        });

    }

    private void idDataConversion(TenantIsolation tenantIsolation, DeviceTemplateInfoDTO template, Map<String, Long> repeatThingModelNameMap) {
        Long tenantId = tenantIsolation.getTenantId();
        HashMap<Long, Long> deviceMap = new HashMap<>();
        HashMap<Long, Long> thingModelMap = new HashMap<>();
        HashMap<Long, Long> channelMap = new HashMap<>();
        HashMap<Long, Long> labelMap = new HashMap<>();
        HashMap<Long, Long> labelGroupMap = new HashMap<>();
        Set<Long> repeatThingModelId = new HashSet<>();
        ProductDeviceServerDataDTO productDeviceServerData = template.getDataDTO();
        if (CollectionUtils.isNotEmpty(productDeviceServerData.getDeviceList())) {
            productDeviceServerData.getDeviceList().forEach(c -> {
                long id = IdGenerator.generateId();
                deviceMap.put(c.getId(), id);
                c.setId(id);
                c.setStatus(0);
                c.setTenantId(tenantId);
            });
        }
        if (CollectionUtils.isNotEmpty(productDeviceServerData.getThingModelList())) {
            List<ThingModelDTO> modelDTOS = new ArrayList<>(productDeviceServerData.getThingModelList());
            Iterator<ThingModelDTO> iterator = modelDTOS.listIterator();
            while (iterator.hasNext()) {
                ThingModelDTO next = iterator.next();
                if (repeatThingModelNameMap.containsKey(next.getName())) {
                    thingModelMap.put(next.getId(), repeatThingModelNameMap.get(next.getName()));
                    repeatThingModelId.add(next.getId());
                    iterator.remove();
                    continue;
                }
                long id = IdGenerator.generateId();
                thingModelMap.put(next.getId(), id);
                next.setId(id);
                next.setTenantId(tenantId);
            }
            productDeviceServerData.setThingModelList(modelDTOS);
        }
        if (CollectionUtils.isNotEmpty(productDeviceServerData.getChannelList())) {
            productDeviceServerData.getChannelList().forEach(c -> {
                long id = IdGenerator.generateId();
                channelMap.put(c.getId(), id);
                c.setId(id);
                c.setTenantId(tenantId);
            });
        }
        if (CollectionUtils.isNotEmpty(productDeviceServerData.getChannelParamList())) {
            productDeviceServerData.getChannelParamList().forEach(c -> {
                c.setId(IdGenerator.generateId());
                c.setChannelId(channelMap.getOrDefault(c.getChannelId(), c.getChannelId()));
                c.setTenantId(tenantId);
            });
        }
        if (CollectionUtils.isNotEmpty(productDeviceServerData.getDeviceModelInheritList())) {
            productDeviceServerData.getDeviceModelInheritList().forEach(c -> {
                c.setId(IdGenerator.generateId());
                c.setDeviceId(deviceMap.getOrDefault(c.getDeviceId(), c.getDeviceId()));
                c.setInheritThingModelId(thingModelMap.getOrDefault(c.getInheritThingModelId(), c.getInheritThingModelId()));
                c.setTenantId(tenantId);
            });
        }
        if (CollectionUtils.isNotEmpty(productDeviceServerData.getDeviceServiceList())) {
            productDeviceServerData.getDeviceServiceList().forEach(c -> {
                long id = IdGenerator.generateId();
                c.setId(id);
                c.setDeviceId(deviceMap.getOrDefault(c.getDeviceId(), c.getDeviceId()));
                c.setTenantId(tenantId);
            });
        }
        if (CollectionUtils.isNotEmpty(productDeviceServerData.getLabelGroupList())) {
            productDeviceServerData.getLabelGroupList().forEach(c -> {
                long id = IdGenerator.generateId();
                labelGroupMap.put(c.getId(), id);
                c.setId(id);
                c.setChannelId(channelMap.getOrDefault(c.getChannelId(), c.getChannelId()));
                c.setTenantId(tenantId);
            });
        }
        if (CollectionUtils.isNotEmpty(productDeviceServerData.getLabelList())) {
            productDeviceServerData.getLabelList().forEach(c -> {
                long id = IdGenerator.generateId();
                labelMap.put(c.getId(), id);
                c.setId(id);
                c.setLabelGroupId(labelGroupMap.getOrDefault(c.getLabelGroupId(), c.getLabelGroupId()));
                c.setTenantId(tenantId);
            });
        }
        if (CollectionUtils.isNotEmpty(productDeviceServerData.getLabelBindRelationList())) {
            productDeviceServerData.getLabelBindRelationList().forEach(c -> {
                c.setId(IdGenerator.generateId());
                c.setLabelId(labelMap.getOrDefault(c.getLabelId(), c.getLabelId()));
                c.setDeviceId(deviceMap.getOrDefault(c.getDeviceId(), c.getDeviceId()));
                if (Integer.valueOf(1).equals(c.getModelType())) {
                    c.setDirectlyModelId(thingModelMap.getOrDefault(c.getDirectlyModelId(), c.getDirectlyModelId()));
                } else if (Integer.valueOf(2).equals(c.getModelType())) {
                    c.setDirectlyModelId(deviceMap.getOrDefault(c.getDirectlyModelId(), c.getDirectlyModelId()));
                }
                c.setTenantId(tenantId);
            });
        }
        if (CollectionUtils.isNotEmpty(productDeviceServerData.getSubscriptionList())) {
            List<SubscriptionDTO> subscriptionDTOS = new ArrayList<>(productDeviceServerData.getSubscriptionList());
            Iterator<SubscriptionDTO> iterator = subscriptionDTOS.iterator();
            while (iterator.hasNext()) {
                SubscriptionDTO c = iterator.next();
                c.setId(IdGenerator.generateId());
                //TODO: 回调
//                c.setCallbackId(redirectMap.getOrDefault(c.getCallbackId(),c.getCallbackId()));
                if (Integer.valueOf(1).equals(c.getModelType())) {
                    if (repeatThingModelId.contains(c.getDirectlyModelId())) {
                        iterator.remove();
                        continue;
                    }
                    c.setDirectlyModelId(thingModelMap.getOrDefault(c.getDirectlyModelId(), c.getDirectlyModelId()));
                } else if (Integer.valueOf(2).equals(c.getModelType())) {
                    c.setDirectlyModelId(deviceMap.getOrDefault(c.getDirectlyModelId(), c.getDirectlyModelId()));
                }
                if (ObjectUtil.isEmpty(c.getEnable())) {
                    c.setEnable(true);
                }
                c.setTenantId(tenantId);
            }
            productDeviceServerData.setSubscriptionList(subscriptionDTOS);
        }
        if (CollectionUtils.isNotEmpty(productDeviceServerData.getThingModelInheritList())) {
            List<ThingModelInheritDTO> modelInheritDTOS = new ArrayList<>(productDeviceServerData.getThingModelInheritList());
            Iterator<ThingModelInheritDTO> iterator = modelInheritDTOS.iterator();
            while (iterator.hasNext()) {
                ThingModelInheritDTO c = iterator.next();
                if (repeatThingModelId.contains(c.getThingModelId())) {
                    iterator.remove();
                    continue;
                }
                c.setId(IdGenerator.generateId());
                c.setThingModelId(thingModelMap.getOrDefault(c.getThingModelId(), c.getThingModelId()));
                c.setInheritThingModelId(thingModelMap.getOrDefault(c.getInheritThingModelId(), c.getInheritThingModelId()));
                c.setTenantId(tenantId);
            }
            productDeviceServerData.setThingModelInheritList(modelInheritDTOS);
        }

        if (CollectionUtils.isNotEmpty(productDeviceServerData.getThingServiceList())) {
            List<ThingServiceDTO> serviceDTOS = new ArrayList<>(productDeviceServerData.getThingServiceList());
            Iterator<ThingServiceDTO> iterator = serviceDTOS.iterator();
            while (iterator.hasNext()) {
                ThingServiceDTO c = iterator.next();
                if (repeatThingModelId.contains(c.getThingModelId())) {
                    iterator.remove();
                    continue;
                }
                long id = IdGenerator.generateId();
                c.setId(id);
                c.setThingModelId(thingModelMap.getOrDefault(c.getThingModelId(), c.getThingModelId()));
                c.setTenantId(tenantId);
            }
            productDeviceServerData.setThingServiceList(serviceDTOS);
        }
    }

    private <T, R extends BaseMapper> void checkNameUniqueness(List<String> names, R entityMapper, Class<T> c, SFunction<T, String> getFunc, SFunction<T, Long> getTenant, Long tenantId) {
        List<T> list = new LambdaQueryChainWrapper<T>(entityMapper)
                .eq(getTenant, tenantId).in(getFunc, names).list();
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> strings = BeanUtilsIntensifier.getSomething(list, getFunc);
            log.error("设备模板创建失败，{}重复：{}", c.getName(), strings.toString());
            throw new BizException("设备模板创建失败，" + c.getName().substring(0, c.getName().length() - 6) + "名称重复：" + strings.toString());
        }
    }

    private void dataConversion(DeviceTemplateBo bo, DeviceTemplateInfoDTO dto, String direction, Long edgeGatewayId, boolean deleteIpPort) {
        if ("out".equals(direction)) {
            List<DeviceDTO> deviceList = dto.getDataDTO().getDeviceList();
            List<DeviceRequestBo> deviceRequestBos = BeanUtilsIntensifier.copyBeanList(deviceList, DeviceRequestBo.class);
            bo.setDeviceList(deviceRequestBos);
            Map<Long, List<ChannelParamDTO>> map = dto.getDataDTO().getChannelParamList().stream().collect(Collectors.groupingBy(ChannelParamDTO::getChannelId));
            List<EditChannelDTO> channelDTOS = new ArrayList<>();
            dto.getDataDTO().getChannelList().forEach(channelDTO -> {
                List<ChannelParamDTO> channelParamDTOS = map.get(channelDTO.getId());
                if (deleteIpPort) {
                    channelParamDTOS.forEach(channelParamDTO -> {
                        if ("ip".equals(channelParamDTO.getName()) || "port".equals(channelParamDTO.getName())) {
                            channelParamDTO.setValue("");
                        }
                    });
                }
                channelDTOS.add(EditChannelDTO.builder()
                        .id(channelDTO.getId())
                        .name(channelDTO.getName())
                        .descript(channelDTO.getDescript())
                        .channelParamList(BeanUtilsIntensifier.copyBeanList(channelParamDTOS, com.nti56.nlink.product.device.server.model.channel.dto.ChannelParamDTO.class))
                        .build());
            });
            bo.setChannelList(channelDTOS);
        } else {
            Map<Long, EditChannelDTO> channelEditMap = bo.getChannelList().stream().collect(Collectors.toMap(EditChannelDTO::getId, e -> e));
            Map<Long, List<com.nti56.nlink.product.device.server.model.channel.dto.ChannelParamDTO>> channelParamEditMap = BeanUtilsIntensifier.collection2Map(bo.getChannelList(), EditChannelDTO::getId, EditChannelDTO::getChannelParamList);
            Map<String, String> channelNameMap = new HashMap<>();
            dto.getDataDTO().getChannelList().forEach(channelDTO -> {
                if (channelEditMap.containsKey(channelDTO.getId())) {
                    EditChannelDTO editChannelDTO = channelEditMap.get(channelDTO.getId());
                    channelNameMap.put(channelDTO.getName(), editChannelDTO.getName());
                    channelDTO.setDescript(editChannelDTO.getDescript());
                    channelDTO.setName(editChannelDTO.getName());
                    channelDTO.setEdgeGatewayId(edgeGatewayId);
                }
            });
            Map<Long, DeviceRequestBo> deviceEditMap = bo.getDeviceList().stream().collect(Collectors.toMap(DeviceRequestBo::getId, device -> device));
            dto.getDataDTO().getDeviceList().forEach(deviceDTO -> {
                if (deviceEditMap.containsKey(deviceDTO.getId())) {
                    DeviceRequestBo editBo = deviceEditMap.get(deviceDTO.getId());
                    deviceDTO.setDescript(editBo.getDescript());
                    deviceDTO.setName(editBo.getName());
                    if (!ObjectUtils.isEmpty(deviceDTO.getChannel())) {
                        deviceDTO.setChannel(channelNameMap.getOrDefault(deviceDTO.getChannel(), deviceDTO.getChannel()));
                    }
                    deviceDTO.setEdgeGatewayId(edgeGatewayId);
                }
            });
            Map<Long, ChannelParamDTO> channelParamDTOMap = dto.getDataDTO().getChannelParamList().stream().collect(Collectors.toMap(ChannelParamDTO::getId, e -> e));
            channelParamEditMap.values().forEach(channelParamDTOs -> {
                if (CollectionUtil.isNotEmpty(channelParamDTOs)) {
                    channelParamDTOs.forEach(channelParamDTO -> {
                        if (channelParamDTOMap.containsKey(channelParamDTO.getId())) {
                            channelParamDTOMap.get(channelParamDTO.getId()).setValue(channelParamDTO.getValue());
                        }
                    });
                }
            });
        }
    }

    private void checkNameUniqueness(Long tenantId, String name) {
        List<DeviceTemplateEntity> list = new LambdaQueryChainWrapper<>(mapper)
                .eq(DeviceTemplateEntity::getTenantId, tenantId)
                .eq(DeviceTemplateEntity::getName, name)
                .list();
        if (CollectionUtil.isNotEmpty(list)) {
            log.error("租户下已存在该模板:{}", name);
            throw new BizException("租户下已存在该模板:" + name);
        }
    }

    private CreateDeviceTemplateFileRep createDeviceTemplateFile(TenantIsolation tenantIsolation, Long templateId) {
        ApplicationHome h = new ApplicationHome(this.getClass());
        File tenantFile = new File(h.getSource().getParentFile().getParent() + "/deviceTemplate", tenantIsolation.getTenantId() + "");
        if (!tenantFile.exists()) {
            boolean mkdirs = tenantFile.mkdirs();
            if (!mkdirs) {
                throw new BizException("创建设备模板目录异常");
            }
        }
        File templateFile = new File(tenantFile, templateId + ".dt");
        return CreateDeviceTemplateFileRep.builder()
                .id(templateId)
                .file(templateFile)
                .build();
    }


}
