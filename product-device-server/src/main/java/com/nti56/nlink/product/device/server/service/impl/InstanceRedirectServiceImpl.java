package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.exception.BizException;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.constant.Constant;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.event.InvokeRedirectEvent;
import com.nti56.nlink.product.device.server.domain.redirect.InstanceRedirect;
import com.nti56.nlink.product.device.server.domain.redirect.MQTTRedirectFn;
import com.nti56.nlink.product.device.server.domain.redirect.WebhookRedirectFn;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.enums.RedirectTypeEnum;
import com.nti56.nlink.product.device.server.mapper.RedirectMapper;
import com.nti56.nlink.product.device.server.mapper.ThingModelMapper;
import com.nti56.nlink.product.device.server.model.edgegateway.dto.ExcelMessageDTO;
import com.nti56.nlink.product.device.server.model.redirect.ExcelRedirectDTO;
import com.nti56.nlink.product.device.server.model.redirect.RedirectDTO;
import com.nti56.nlink.product.device.server.model.redirect.RedirectReferenceDto;
import com.nti56.nlink.product.device.server.service.*;
import com.nti56.nlink.product.device.server.service.impl.strategy.MQTTClientPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 类说明：
 *
 * @ClassName InstanceRedirectServiceImpl
 * @Description 针对表【re_instance_redirect(实例回调操作)】的数据库操作Service实现
 * <AUTHOR>
 * @Date 2022/6/24 15:51
 * @Version 1.0
 */

@Slf4j
@Service
public class InstanceRedirectServiceImpl extends ServiceImpl<RedirectMapper, InstanceRedirectEntity> implements IInstanceRedirectService {

    @Resource
    private RedirectMapper mapper;
    @Autowired
    private CommonFetcherFactory commonFetcherFactory;

    @Autowired
    @Lazy
    ThingModelMapper thingModelMapper;
    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Autowired @Lazy
    private StringRedisTemplate stringRedisTemplate;

    @Autowired @Lazy
    private ISubscriptionService subscriptionService;

    @Override
    public Result<Page<RedirectDTO>> getPage(InstanceRedirectEntity entity, Page<InstanceRedirectEntity> page) {
        Page<InstanceRedirectEntity> list = getInstanceRedirectEntityListByName(entity.getRedirectName(),entity.getTenantId(),page);
        List<InstanceRedirectEntity> records = list.getRecords();
        List<RedirectDTO> dtoRecords = new ArrayList<>(records.size());
        for (InstanceRedirectEntity record : records) {
            RedirectDTO redirectDTO = new RedirectDTO();
            BeanUtil.copyProperties(record, redirectDTO);
            String countStr = stringRedisTemplate.opsForValue().get(String.format(RedisConstant.REDIRECT_INVOKE_TIMES, record.getId()));
            redirectDTO.setRedirectInvokeTime(Optional.ofNullable(record.getRedirectInvokeTime()).orElse(0) + Integer.valueOf(StringUtils.isEmpty(countStr) ? "0" : countStr));
            redirectDTO.setLastInvokeTime(stringRedisTemplate.opsForValue().get(String.format(RedisConstant.REDIRECT_INVOKE_LAST_TIME, record.getId())));
            RedirectTypeEnum typeEnum = RedirectTypeEnum.typeOfCode((byte) record.getRedirectType().intValue());
            switch (typeEnum){
                case WEBHOOK:
                    WebhookRedirectFn webhookFn = JSONUtil.toBean(record.getRedirectFn(), WebhookRedirectFn.class);
                    redirectDTO.setTargetUrl(webhookFn.getTargetUrl());
                    break;
                case MQTT_SINK:
                    MQTTRedirectFn mqttRedirectFn = JSONUtil.toBean(record.getRedirectFn(), MQTTRedirectFn.class);
                    redirectDTO.setTargetUrl(mqttRedirectFn.getTargetUrl());
                    break;
            }

            dtoRecords.add(redirectDTO);
        }
        Page<RedirectDTO> dtoPage = new Page<>();
        dtoPage.setCurrent(list.getCurrent());
        dtoPage.setSize(list.getSize());
        dtoPage.setTotal(list.getTotal());
        dtoPage.setRecords(dtoRecords);
        return Result.ok(dtoPage);
    }

    private Page<InstanceRedirectEntity> getInstanceRedirectEntityListByName(String name,Long tenantId,Page<InstanceRedirectEntity> page){
        QueryWrapper queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId);
        queryWrapper.like(!StringUtils.isEmpty(name), "redirect_name", name);
        Page<InstanceRedirectEntity> list = mapper.selectPage(page, queryWrapper);
        return list;
    }

    @Override
    public Result<List<InstanceRedirectEntity>> list(InstanceRedirectEntity entity) {

        List<InstanceRedirectEntity> list = mapper.selectList(new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    @Override
    @Transactional
    public Result<InstanceRedirectEntity> save(TenantIsolation tenantIsolation, InstanceRedirectEntity entity) {

        Result<InstanceRedirect> instanceRedirectResult = InstanceRedirect.checkBeforeSave(entity, commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId()));
        if (!instanceRedirectResult.getSignal()) {
            throw new BizException(instanceRedirectResult.getServiceCode(), instanceRedirectResult.getMessage());
        }
        // 校验租户下名称唯一
        InstanceRedirectEntity queryEntity = new InstanceRedirectEntity();
        queryEntity.setTenantId(tenantIsolation.getTenantId());
        queryEntity.setRedirectName(entity.getRedirectName());
        InstanceRedirectEntity existRedirect = mapper.selectOne(new QueryWrapper<>(queryEntity));
        if (!Objects.isNull(existRedirect)) {
            throw new BizException("回调名称已存在");
        }
        try {
            entity.setModuleId(tenantIsolation.getModuleId());
            entity.setSpaceId(tenantIsolation.getSpaceId());
            entity.setTenantId(tenantIsolation.getTenantId());
            entity.setEngineeringId(tenantIsolation.getEngineeringId());
            if (mapper.insert(entity) == 1) {
                return Result.ok(entity);
            }
        } catch (Exception e) {
            log.error("create redirect error,error msg:{}", e.getMessage(), e);
        }
        return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);

    }

    @Override
    @Transactional
    public Result update(InstanceRedirectEntity entity, TenantIsolation tenantIsolation) {
        Result<InstanceRedirect> instanceRedirectResult = InstanceRedirect.checkBeforeUpdate(entity, commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId()));
        if (!instanceRedirectResult.getSignal()) {
            throw new BizException(instanceRedirectResult.getServiceCode(), instanceRedirectResult.getMessage());
        }
        MQTTClientPool.removeClient(entity.getId());
        if (mapper.updateById(entity) == 1) {
            stringRedisTemplate.delete(String.format(RedisConstant.REDIRECT_INSTANCE_CACHE, entity.getId()));
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
    }

    @Override
    public Result deleteById(Long redirectId, TenantIsolation tenantIsolation) {
        Result<InstanceRedirect> deleteCheck = InstanceRedirect.checkBeforeDelete(redirectId, commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId()));
        if (!deleteCheck.getSignal()) {
            throw new BizException(deleteCheck.getServiceCode(), deleteCheck.getMessage());
        }
        //校验回调有没有被使用
     /*   InstanceExecEntity instanceExecEntity = new InstanceExecEntity();
        instanceExecEntity.setTenantId(tenantIsolation.getTenantId());
        instanceExecEntity.setRedirectId(redirectId);
        List<InstanceExecEntity> instanceExecEntities = instanceExecMapper.selectList(new QueryWrapper<>(instanceExecEntity));
        if(CollectionUtil.isNotEmpty(instanceExecEntities)){
            return Result.error("回调已被引用，无法删除。");
        }*/
        MQTTClientPool.removeClient(redirectId);
        if (mapper.deleteById(redirectId) == 1) {
            stringRedisTemplate.delete(String.format(RedisConstant.REDIRECT_INSTANCE_CACHE, redirectId));
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

    @Override
    public Result<InstanceRedirectEntity> getByIdAndTenantIsolation(Long redirectId, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<InstanceRedirectEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(InstanceRedirectEntity::getId, redirectId)
                .eq(InstanceRedirectEntity::getTenantId, tenantIsolation.getTenantId());
        return Result.ok(mapper.selectOne(lqw));
    }

    @Override
    public Result<Object> testRedirect(InstanceRedirectEntity entity, TenantIsolation tenantIsolation) {
        // 清除缓存，避免拿到旧连接
        if (!Objects.isNull(entity.getId())) {
            MQTTClientPool.removeClient(entity.getId());
        }
        switch (RedirectTypeEnum.typeOfCode((byte) entity.getRedirectType().intValue())) {
            case WEBHOOK:
                return WebhookRedirectFn.getFunctionInstance(entity, null).execFn();
            case MQTT_SINK:
                MQTTRedirectFn functionInstance = MQTTRedirectFn.getFunctionInstance(entity, null);
                if (Objects.isNull(functionInstance)) {
                    return Result.error("找不到回调");
                }
                return functionInstance.execFnSync();
            default:
                return Result.error("暂不支持的回调类型");
        }

    }


    @Override
//    @Async
    public Result<Boolean> execRedirectWithPayload(Long redirectId, TenantIsolation tenantIsolation, Object payload) {
        log.debug("payload object:{}", payload);
        String instanceStr = stringRedisTemplate.opsForValue().get(String.format(RedisConstant.REDIRECT_INSTANCE_CACHE, redirectId));
        InstanceRedirectEntity instance = null;
        if (StringUtils.isEmpty(instanceStr)) {
            Result<InstanceRedirectEntity> redirect = InstanceRedirect.getRedirect(redirectId, commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId()));
            if (!redirect.getSignal() || Objects.isNull(redirect.getResult())) {
                return Result.ok(false, "找不到回调信息");
            }
            instance = redirect.getResult();
            String key = String.format(RedisConstant.REDIRECT_INSTANCE_CACHE, redirectId);
            stringRedisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(instance));
            stringRedisTemplate.expire(key, 30, TimeUnit.MINUTES);
        } else {
            instance = JSONUtil.toBean(instanceStr, InstanceRedirectEntity.class);
        }
        InstanceRedirect instanceRedirect = new InstanceRedirect(instance);
        if (!Objects.isNull(payload)) {
            instanceRedirect.setPayload(payload);
        }

        Result<Boolean> booleanResult = instanceRedirect.invoke();
        if (booleanResult.getSignal()) {
            // 成功执行回调，回调调用次数加1
//            stringRedisTemplate.opsForValue().increment(String.format(Constant.RedisConstant.REDIRECT_INVOKE_TIMES,redirectId));
            eventPublisher.publishEvent(new InvokeRedirectEvent(this, redirectId, tenantIsolation.getTenantId()));
        }else {
            log.debug("执行回调失败，原因{}",booleanResult.getMessage());
        }
        return booleanResult;
    }

    @Override
    public Result<Integer> redirectCount(TenantIsolation tenantIsolation) {
        /*LambdaQueryWrapper<InstanceRedirectEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(InstanceRedirectEntity::getDeleted, 0)
                .eq(InstanceRedirectEntity::getTenantId, tenantIsolation.getTenantId());
        List<InstanceRedirectEntity> instanceRedirectEntityList = mapper.selectList(lqw);
        Integer redirectCount = 0;
        if (CollectionUtil.isNotEmpty(instanceRedirectEntityList)) {
            for (InstanceRedirectEntity instanceRedirectEntity : instanceRedirectEntityList) {
                String countStr = stringRedisTemplate.opsForValue().get(String.format(RedisConstant.REDIRECT_INVOKE_TIMES, instanceRedirectEntity.getId()));
                Integer singleCount = Optional.ofNullable(instanceRedirectEntity.getRedirectInvokeTime()).orElse(0) + Integer.valueOf(StringUtils.isEmpty(countStr) ? "0" : countStr);
                redirectCount += singleCount;
            }
        }*/
        String key = String.format(RedisConstant.REDIRECT_INVOKE_TIMES_DAILY, tenantIsolation.getTenantId(),DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT));
        stringRedisTemplate.expire(key,1440, TimeUnit.MINUTES);
        String countStr = stringRedisTemplate.opsForValue().get(key);
        return Result.ok(Integer.valueOf(StringUtils.isEmpty(countStr) ? "0" : countStr));
    }

    @Override
    public Result<Boolean> updateReference(TenantIsolation toBean, RedirectReferenceDto referenceDto) {

        if (Objects.isNull(referenceDto)) {
            return Result.error("parma null");
        }
        Long redirectId = referenceDto.getRedirectId();
        Long refId = referenceDto.getRefId();
        String key = String.format(RedisConstant.REDIRECT_REFERENCE_CACHE, redirectId);
        switch (referenceDto.getUpdateType()) {
            case 0:
                //删除
                stringRedisTemplate.opsForHash().delete(key, String.valueOf(refId));
                break;
            case 1:
                stringRedisTemplate.opsForHash().put(key, String.valueOf(refId), JSONUtil.toJsonStr(referenceDto));
                break;
            default:


        }
        return Result.ok();
    }

    @Override
    public Result<List<Object>> getReference(Long redirectId, TenantIsolation tenantIsolation) {
        /*if(!Objects.isNull(redirectId)){
            Map<Object, Object> refStrMap = stringRedisTemplate.opsForHash().entries(String.format(RedisConstant.REDIRECT_REFERENCE_CACHE, redirectId));
            if(CollectionUtil.isNotEmpty(refStrMap)){
                List<Object> resultList = new ArrayList<>(refStrMap.size());
                refStrMap.forEach((k,v)->{
                    resultList.add(JSONUtil.parseObj(String.valueOf(v)));
                });
                return Result.ok(resultList);
            }
            return Result.ok(new ArrayList<>());
        }*/
        if(Objects.isNull(redirectId)) {
            return Result.error("回调ID为空");

        }
        SubscriptionEntity entity = new SubscriptionEntity();
        entity.setCallbackId(redirectId);
        entity.setTenantId(tenantIsolation.getTenantId());
        entity.setDeleted(false);
        Result<List<SubscriptionEntity>> listResult = subscriptionService.list(entity);
        if (!listResult.getSignal()) {
            return Result.error(listResult.getMessage());
        }
        List<SubscriptionEntity> subscriptionEntities = listResult.getResult();
        if (CollectionUtil.isEmpty(subscriptionEntities)) {
            return Result.ok(new ArrayList<>());
        }
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());
        LambdaQueryWrapper<ThingModelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.and(w->w.eq(ThingModelEntity::getTenantId, tenantIsolation.getTenantId())).or()
                .eq(ThingModelEntity::getTenantId, Constant.DEFAULT_THING);
        List<ThingModelEntity> thingModelEntityList = thingModelMapper.selectList(lqw);
        Map<Long, String> thingNameMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(thingModelEntityList)) {
            thingNameMap = thingModelEntityList.stream().collect(Collectors.toMap(ThingModelEntity::getId, ThingModelEntity::getName));
        }

        List<DeviceEntity> deviceEntityList = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), DeviceEntity.class);
        Map<Long, String> deviceNameMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(deviceEntityList)) {
            deviceNameMap = deviceEntityList.stream().collect(Collectors.toMap(DeviceEntity::getId, DeviceEntity::getName));
        }
        List<Object> resultList = new ArrayList<>(subscriptionEntities.size());
        for (SubscriptionEntity subscriptionEntity : subscriptionEntities) {
            JSONObject jsonObject = new JSONObject();
            if (1 == subscriptionEntity.getModelType()) {
                if(ObjectUtil.isNull(thingNameMap.get(subscriptionEntity.getDirectlyModelId()))){
                    continue;
                }
                jsonObject.put("typeName", "模型");
                jsonObject.put("type", 2);
                jsonObject.put("refName", thingNameMap.get(subscriptionEntity.getDirectlyModelId()));
            } else if (2 == subscriptionEntity.getModelType()) {
                if(ObjectUtil.isNull(deviceNameMap.get(subscriptionEntity.getDirectlyModelId()))){
                    continue;
                }
                jsonObject.put("typeName", "设备");
                jsonObject.put("type", 1);
                jsonObject.put("refName", deviceNameMap.get(subscriptionEntity.getDirectlyModelId()));
            } else {
                continue;
            }
            jsonObject.put("redirectId", redirectId);
            jsonObject.put("refId", subscriptionEntity.getDirectlyModelId());
            resultList.add(jsonObject);
        }
        return Result.ok(resultList);
    }

    @EventListener
    @Async
    public void invokeListener(InvokeRedirectEvent event) {
        stringRedisTemplate.opsForValue().increment(String.format(RedisConstant.REDIRECT_INVOKE_TIMES, event.getInstanceRedirectEntity()));
        stringRedisTemplate.opsForValue().increment(String.format(RedisConstant.REDIRECT_INVOKE_TIMES_DAILY, event.getTenantId(), DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT)));
        stringRedisTemplate.opsForValue().set(String.format(RedisConstant.REDIRECT_INVOKE_LAST_TIME, event.getInstanceRedirectEntity()),
                DateUtil.format(new Date(), DatePattern.NORM_DATETIME_MS_PATTERN));
    }

    @Override
    public Result<List<ExcelMessageDTO>> instanceRedirectBatchInput(TenantIsolation tenantIsolation, Integer exportType, List<ExcelRedirectDTO> list) {
        if(CollectionUtil.isEmpty(list)){
            return Result.error("导入的内容为空!");
        }
        List<ExcelMessageDTO> excelMessageDTOList = new ArrayList<>();
        List<InstanceRedirectEntity> insertInstanceRedirectEntityList = new ArrayList<>();
        List<InstanceRedirectEntity> instanceRedirectEntityList = mapper.listAllRedirect(tenantIsolation.getTenantId());
        Set<String> nameSet = new HashSet();
        if(CollectionUtil.isNotEmpty(instanceRedirectEntityList)){
            nameSet = instanceRedirectEntityList.stream().map(InstanceRedirectEntity::getRedirectName).collect(Collectors.toSet());
        }
        for (ExcelRedirectDTO excelRedirectDTO : list) {
            String redirectName = excelRedirectDTO.getRedirectName();
            //0:不保留;1:保留两者
            if(exportType == 0 && nameSet.contains(redirectName)){
                if(excelMessageDTOList.size() >= 100){
                    continue;
                }
                ExcelMessageDTO excelMessageDTO = new ExcelMessageDTO(excelRedirectDTO.getRedirectName(),"导入模式为不保留，回调名重复!");
                excelMessageDTOList.add(excelMessageDTO);
                //不保留类型，给错误提示
                continue;
            }
            if(exportType == 1 && nameSet.contains(excelRedirectDTO.getRedirectName())){
                String randomString = RandomUtil.randomString(5);
                if(randomString.length() >= 58){
                    redirectName = excelRedirectDTO.getRedirectName().subSequence(0,58) + randomString;
                }else {
                    redirectName = excelRedirectDTO.getRedirectName() + randomString;
                }
            }
            excelRedirectDTO.setRedirectName(redirectName);
            InstanceRedirectEntity instanceRedirectEntity = new InstanceRedirectEntity();
            BeanUtil.copyProperties(excelRedirectDTO,instanceRedirectEntity);
            Result<InstanceRedirect> instanceRedirectResult = InstanceRedirect.checkBeforeSave(instanceRedirectEntity, commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId()));
            if(!instanceRedirectResult.getSignal()){
                if(excelMessageDTOList.size() >= 100){
                    continue;
                }
                ExcelMessageDTO excelMessageDTO = new ExcelMessageDTO(instanceRedirectEntity.getRedirectName(),instanceRedirectResult.getMessage());
                excelMessageDTOList.add(excelMessageDTO);
                continue;
            }
            Long redirectId = IdGenerator.generateId();
            instanceRedirectEntity.setId(redirectId);
            insertInstanceRedirectEntityList.add(instanceRedirectEntity);

        }
        if(CollectionUtil.isNotEmpty(insertInstanceRedirectEntityList)){
            mapper.insertBatchSomeColumn(insertInstanceRedirectEntityList);
        }
        return Result.ok(excelMessageDTOList);
    }

    @Override
    public void exportRedirect(HttpServletResponse response, Long tenantId, Set<Long> referenceIds) throws IOException {
        List<InstanceRedirectEntity> instanceRedirectEntityList = mapper.selectBatchIds(referenceIds);
        List<ExcelRedirectDTO> excelRedirectDTOS = new ArrayList<>();
        for(InstanceRedirectEntity entity :instanceRedirectEntityList){
            ExcelRedirectDTO excelRedirectDTO = new ExcelRedirectDTO();
            BeanUtil.copyProperties(entity,excelRedirectDTO);
            excelRedirectDTOS.add(excelRedirectDTO);
        }
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = tenantId.toString();
            fileName = URLEncoder.encode(fileName + "_redirects", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ExcelRedirectDTO.class).sheet("redirects")
                    .doWrite(excelRedirectDTOS);
        } catch (Exception e) {
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            Map<String, String> map = MapUtils.newHashMap();
            map.put("status", "failure");
            map.put("message", "回调下载文件失败" + e.getMessage());
            response.getWriter().println(JSON.toJSONString(map));
        }
    }
}




