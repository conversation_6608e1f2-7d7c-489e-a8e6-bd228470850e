package com.nti56.nlink.product.device.server.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelDpo;
import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema
public class ThingModelDto {
    
    @Schema(description = "物模型主键ID")
    private Long id;

    @Schema(description = "物模型名称")
    private String name;

    @Schema(description = "继承的物模型id列表")
    private List<Long> inheritThingModelIds;

    @Schema(description = "标记实体")
    private List<TagRsp> tags;

    @Schema(description = "继承的物模型定义")
    private List<ModelDpo> modelContainsInherits;
    
    @Schema(description = "自己的物模型定义")
    private ModelFieldDto model;

    @Schema(description = "完整的物模型定义")
    private ModelDpo fullModel;

    @Schema(description = "物模型描述")
    private String descript;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "通用故障模型")
    private ModelDpo commonFaultModel;

    @Schema(description = "设备基础模型")
    private ModelDpo deviceBaseModel;

    private boolean defaultFlag = false;
}
