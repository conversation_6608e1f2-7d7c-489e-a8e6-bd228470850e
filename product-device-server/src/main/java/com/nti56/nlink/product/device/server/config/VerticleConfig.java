package com.nti56.nlink.product.device.server.config;

import com.nti56.nlink.product.device.server.factory.SpringVerticleFactory;
import com.nti56.nlink.product.device.server.service.IDeviceStatusManagementService;
import com.nti56.nlink.product.device.server.verticle.*;
import io.vertx.core.DeploymentOptions;
import io.vertx.core.Vertx;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-21 17:29:16
 * @since JDK 1.8
 */
@Slf4j
@Configuration
public class VerticleConfig implements ApplicationRunner {

    @Autowired
    private SpringVerticleFactory springVerticleFactory;
    @Autowired
    IDeviceStatusManagementService deviceService;


    @Autowired
    Vertx vertx;
    
    @Override
    public void run(ApplicationArguments args) throws Exception {

        {
            DeploymentOptions options = new DeploymentOptions()
                    .setInstances(1);
            // .setInstances(cpuCores * 2);
            vertx.deployVerticle(springVerticleFactory.prefix() + ":" + MqttEdgeGatewayProxyVerticle.class.getName(), options);
        }




        //在启动verticle之前先进行意思设备同步，将状态同步到内存和redis中
        deviceService.syncCommonType();

        // while(!MemoryCache.getInitState()){
            int second=2;
            // log.info("设备同步未完成，等待{}秒后,再检查一次",second);
            try{
                Thread.sleep(second*1000);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

        // }
        // The verticle factory is registered manually because it is created by the Spring container
        final Integer cpuCores = Runtime.getRuntime().availableProcessors();
        log.info("cpuCores: {}", cpuCores);

        // Scale the verticles on cores: create 4 instances during the deployment
        {
            DeploymentOptions options = new DeploymentOptions()
                    .setInstances(1);
            vertx.deployVerticle(springVerticleFactory.prefix() + ":" + LogProcessVerticle.class.getName(), options);
        }
        {
            DeploymentOptions options = new DeploymentOptions().setWorker(true)
                .setInstances(2);
            vertx.deployVerticle(springVerticleFactory.prefix() + ":" + MqttEventConsumerVerticle.class.getName(), options);
        }
        {
            DeploymentOptions options = new DeploymentOptions()
                .setInstances(2);
            vertx.deployVerticle(springVerticleFactory.prefix() + ":" + MqttPropertyConsumerVerticle.class.getName(), options);
        }
        {
            DeploymentOptions options = new DeploymentOptions()
                .setInstances(10);
            vertx.deployVerticle(springVerticleFactory.prefix() + ":" + MqttLabelConsumerVerticle.class.getName(), options);
        }
        {
            DeploymentOptions options = new DeploymentOptions()
                .setInstances(1);
            vertx.deployVerticle(springVerticleFactory.prefix() + ":" + MqttSenderVerticle.class.getName(), options);
        }
        {
            DeploymentOptions options = new DeploymentOptions()
                .setInstances(1);
            vertx.deployVerticle(springVerticleFactory.prefix() + ":" + MqttSyncConsumerVerticle.class.getName(), options);
        }
        {
            DeploymentOptions options = new DeploymentOptions()
                .setInstances(1);
            vertx.deployVerticle(springVerticleFactory.prefix() + ":" + MqttHeartbeatConsumerVerticle.class.getName(), options);
        }
        {
            DeploymentOptions options = new DeploymentOptions()
                .setInstances(1);
            vertx.deployVerticle(springVerticleFactory.prefix() + ":" + MqttDebugConsumerVerticle.class.getName(), options);
        }
        





        {
            DeploymentOptions options = new DeploymentOptions()
                    .setInstances(1);
            vertx.deployVerticle(springVerticleFactory.prefix() + ":" + MqttNotAssignHeartbeatConsumerVerticle.class.getName(), options);
        }
        {
            DeploymentOptions options = new DeploymentOptions()
                    .setInstances(1);
            vertx.deployVerticle(springVerticleFactory.prefix() + ":" + MqttSyncLabelConfigurationConsumerVerticle.class.getName(), options);
        }

        {
            DeploymentOptions options = new DeploymentOptions().setWorker(true)
                    .setInstances(1);
            vertx.deployVerticle(springVerticleFactory.prefix() + ":" + MqttHardwareConsumerVerticle.class.getName(), options);
        }
    }
    
}
