package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DeviceOptionEnum;
import com.nti56.nlink.product.device.server.entity.ThingModelInheritEntity;
import com.nti56.nlink.product.device.server.model.DeviceDto;
import com.nti56.nlink.product.device.server.model.DeviceRespondBo;

import java.util.List;
import java.util.Set;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:11:44
 * @since JDK 1.8
 */
public interface IDeviceStatusManagementService{

    Result<List<DeviceRespondBo>> deviceBatchSync(TenantIsolation tenantIsolation, List<Long> ids);

    Result<List<DeviceRespondBo>> deviceBatchOption(TenantIsolation tenantIsolation, List<Long> ids, DeviceOptionEnum optionType);

    Result<List<DeviceRespondBo>> deviceBatchOnline(TenantIsolation tenantIsolation, List<Long> ids);

    Result<List<DeviceRespondBo>> deviceBatchOffline(TenantIsolation tenantIsolation, List<Long> ids);

    void syncCommonType();

    Result<List<DeviceRespondBo>> syncByThingModel(TenantIsolation tenantIsolation, Long thingModelId);
    
    Result<List<DeviceRespondBo>> deviceBatchSyncAll(TenantIsolation tenantIsolation);

    Result<Void> syncBeforeCheck(TenantIsolation tenantIsolation,List<Long> deviceIdList);

    Result<Void> syncBeforeCheckInfo(TenantIsolation tenantIsolation);
}
