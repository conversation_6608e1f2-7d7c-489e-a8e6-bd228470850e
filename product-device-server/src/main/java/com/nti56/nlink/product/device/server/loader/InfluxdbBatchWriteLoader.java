package com.nti56.nlink.product.device.server.loader;

import com.nti56.nlink.product.device.server.config.ApplicationHolder;
import com.nti56.nlink.product.device.server.config.InfluxDBCacheConfig;
import com.nti56.nlink.product.device.server.verticle.post.processor.property.UpData2InfluxDBHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class InfluxdbBatchWriteLoader implements ApplicationListener<ApplicationReadyEvent> {
    
    @Autowired
    private InfluxDBCacheConfig cacheConfig;
    
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        if (!cacheConfig.isEnabled()) {
            log.info("InfluxDB cache is disabled, skipping batch write task");
            return;
        }
        
        log.info("Starting InfluxDB batch write background task with config: {}", cacheConfig);
        
        // 使用异步方式启动批量写入任务，避免阻塞应用启动
        startBatchWriteTaskAsync();
        
        log.info("InfluxDB batch write task started successfully");
    }
    
    @Async
    public void startBatchWriteTaskAsync() {
        try {
            UpData2InfluxDBHandler bean = ApplicationHolder.getApplicationContext().getBean(UpData2InfluxDBHandler.class);
            bean.writeByTenant();
        } catch (Exception e) {
            log.error("InfluxDB batch write task failed", e);
            // 重启机制
            log.info("Attempting to restart batch write task in 30 seconds...");
            try {
                Thread.sleep(30000);
                startBatchWriteTaskAsync();
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
                log.warn("Batch write task restart interrupted");
            }
        }
    }
}
