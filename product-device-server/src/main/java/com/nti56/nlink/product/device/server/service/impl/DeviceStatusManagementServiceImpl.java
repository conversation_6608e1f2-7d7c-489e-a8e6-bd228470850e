package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.fetcher.Preloader;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.DeviceRuntimeMetadataField;
import com.nti56.nlink.product.device.server.annotation.AuditLog;
import com.nti56.nlink.product.device.server.constant.Constant;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.device.Device;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DeviceOptionEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.StatusEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.SubscriptionEventTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.SyncStatusEnum;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Subscription;
import com.nti56.nlink.product.device.server.domain.thing.thingmodel.ThingModel;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.enums.ActionEnum;
import com.nti56.nlink.product.device.server.enums.AuditTargetEnum;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.listener.NoChangeKeyExpireListener.NoChangeInfo;
import com.nti56.nlink.product.device.server.mapper.*;
import com.nti56.nlink.product.device.server.model.DeviceRespondBo;
import com.nti56.nlink.product.device.server.model.device.dto.DeviceCheckInfoContext;
import com.nti56.nlink.product.device.server.scriptApi.EngineeringFactory;
import com.nti56.nlink.product.device.server.service.*;
import com.nti56.nlink.product.device.server.service.cache.redis2Mem.MemoryCache;
import com.nti56.nlink.product.device.server.serviceEngine.DataConversionService;
import com.nti56.nlink.product.device.server.util.redis.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 类说明: 设备服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:10:11
 * @since JDK 1.8
 */
@Service
@Slf4j
public class DeviceStatusManagementServiceImpl implements IDeviceStatusManagementService {

    @Autowired @Lazy
    DeviceMapper mapper;

    @Autowired @Lazy
    LabelMapper labelMapper;

    @Autowired @Lazy
    IDeviceService deviceService;

    @Autowired @Lazy
    EdgeGatewayMapper edgeGatewayMapper;

    @Autowired @Lazy
    ITaskService taskService;

    @Autowired @Lazy
    LabelBindRelationMapper labelBindRelationMapper;

    @Autowired @Lazy
    CommonFetcherFactory commonFetcherFactory;

    @Autowired @Lazy
    IEdgeGatewayService edgeGatewayService;

    @Autowired
    @Lazy
    IDeviceOptionService deviceOptionService;

    @Autowired
    @Lazy
    IDeviceModelInheritService deviceModelInheritService;

    @Autowired
    @Lazy
    IThingModelInheritService thingModelInheritService;

    @Autowired
    @Lazy
    IThingModelService thingModelService;

    @Autowired
    @Lazy
    ThingModelMapper thingModelMapper;

    @Autowired
    @Lazy
    ThingServiceMapper thingServiceMapper;

    @Autowired
    @Lazy
    ISubscriptionService subscriptionService;

    @Autowired
    private EngineeringFactory engineeringFactory;

    @Autowired
    private DataConversionService dataConversionService;


    @Override
    public Result<List<DeviceRespondBo>> deviceBatchSync(TenantIsolation tenantIsolation, List<Long> ids) {
        return deviceBatchOption(tenantIsolation, ids, DeviceOptionEnum.SYNC_ALL);
    }
    
    @Override
    public Result<List<DeviceRespondBo>> deviceBatchSyncAll(TenantIsolation tenantIsolation) {
        return deviceBatchOption(tenantIsolation, null, DeviceOptionEnum.SYNC_ALL);
    }
    
    
    private List<EdgeGatewayEntity> getEdgeGatewayList(Long tenantId, Set<Long> egwIds) {
        if (CollectionUtil.isEmpty(egwIds)) {
            return new ArrayList<>();
        }
        return new LambdaQueryChainWrapper<>(edgeGatewayMapper)
                .eq(EdgeGatewayEntity::getTenantId, tenantId)
                .in(EdgeGatewayEntity::getId, egwIds)
                .list();
    }

    private List<DeviceEntity> getDeviceByIds(Long tenantId, List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return new LambdaQueryChainWrapper<>(mapper)
                    .eq(DeviceEntity::getTenantId, tenantId)
                    .eq(DeviceEntity::getDeleted,0)
                    .list();
        }
        return new LambdaQueryChainWrapper<>(mapper)
                .eq(DeviceEntity::getTenantId, tenantId)
                .in(DeviceEntity::getId, ids)
                .eq(DeviceEntity::getDeleted,0)
                .list();
    }

    /**
     * 批量操作
     *
     * @param tenantIsolation
     * @param ids
     * @param optionType      1 - 同步，2 - 启用， 3 - 停用, 4 - 删除
     * @return
     */
    @Override
    @Transactional
    public Result<List<DeviceRespondBo>> deviceBatchOption(TenantIsolation tenantIsolation, List<Long> ids, DeviceOptionEnum optionType) {
        log.debug("device batch option ids:{},optionType:{}", ids, optionType.getName());
        List<DeviceEntity> deviceEntities = getDeviceByIds(tenantIsolation.getTenantId(), ids);
        if (deviceEntities.isEmpty()) {
            return Result.ok();
        }
        // if(CollectionUtil.isEmpty(ids)){
        //     ids = deviceEntities.stream().map(DeviceEntity::getId).collect(Collectors.toList());
        // }
        Set<Long> egwIds = deviceEntities.stream().map(DeviceEntity::getEdgeGatewayId).collect(Collectors.toSet());
        List<EdgeGatewayEntity> edgeGatewayEntities = getEdgeGatewayList(tenantIsolation.getTenantId(), egwIds);
        List<DeviceRespondBo> respondBoList = new ArrayList<>();
        Map<Long,DeviceRespondBo> deviceBos = new HashMap<>();
        Map<Long, String> gwNameMap = edgeGatewayEntities.stream().collect(Collectors.toMap(EdgeGatewayEntity::getId, EdgeGatewayEntity::getName));
        // Map<Long,Future<Result<Long>>> futureMap = new HashMap<>();
        switch (optionType) {
            case ONLINE: {
                Result<List<DeviceRespondBo>> result = doDeviceBatchOnline(tenantIsolation, deviceEntities,gwNameMap,deviceBos);
                if (!result.getSignal()) {
                    return Result.error(ServiceCodeEnum.DEVICE_OPTION_ERROR);
                }else {
                    respondBoList.addAll(result.getResult());
                }
                break;
            }
            case OFFLINE: {
                Result<Void> result = deviceOptionService.doDeviceBatchOffline(tenantIsolation, deviceEntities,gwNameMap);
                if (!result.getSignal()) {
                    return Result.error(ServiceCodeEnum.DEVICE_OPTION_ERROR);
                }
                break;
            }
            case DELETE: {
                Result<List<DeviceRespondBo>> listResult = deviceService.deleteDeviceBatch(tenantIsolation, deviceEntities, gwNameMap);
                if (listResult.getSignal()) {
                    respondBoList.addAll(listResult.getResult());
                }else{
                    return Result.error(ServiceCodeEnum.DEVICE_OPTION_ERROR);
                }
                break;
            }
            case SYNC:{
                Result<Void> result = syncBeforeCheck(tenantIsolation,ids);
                if(!result.getSignal()){
                    log.info("device sync err and {}",result.getMessage());
                    return Result.error(result.getMessage());
                }
                // deviceBatchSync(tenantIsolation, futureMap, deviceEntities, deviceBos, gwNameMap);
                //TODO  更新需要单独写
                deviceBatchAllSync2(tenantIsolation,  deviceEntities, deviceBos, gwNameMap);
                break;
            }
            case SYNC_ALL: {
                long start=System.currentTimeMillis();
                Result<Void> result = syncBeforeCheck(tenantIsolation,ids);
                if(!result.getSignal()){
                    log.info("device sync err and {}",result.getMessage());
                    return Result.error(result.getMessage());
                }
                // deviceBatchAllSync(tenantIsolation, futureMap, deviceEntities, deviceBos, gwNameMap);
                deviceBatchAllSync2(tenantIsolation,  deviceEntities, deviceBos, gwNameMap);
                engineeringFactory.resetEngineering(tenantIsolation.getTenantId());
                respondBoList.addAll(deviceBos.values().stream().filter(deviceRespondBo -> !deviceRespondBo.getResult().getSignal()).collect(Collectors.toList()));
                log.info("=============== post oneline，sync，syncAll operation spend:{} tenantId:{} ",System.currentTimeMillis() - start,tenantIsolation.getTenantId());
                break;
            }
        }
        // if (CollectionUtil.isNotEmpty(futureMap)) {
        //     //future结果等待需要花很长时间，同步执行会超时，改成异步执行
        //     new Thread(()->{
        //         long begin = System.currentTimeMillis();
        //         getFutureResult(futureMap,deviceBos,optionType.getNameDesc());
        //         if (DeviceOptionEnum.ONLINE.getValue().equals(optionType.getValue()) || DeviceOptionEnum.SYNC.getValue().equals(optionType.getValue()) || DeviceOptionEnum.SYNC_ALL.getValue().equals(optionType.getValue())) {
        //             //重置Things
        //             engineeringFactory.resetEngineering(tenantIsolation.getTenantId());
        //         }
        //         respondBoList.addAll(deviceBos.values().stream().filter(deviceRespondBo -> !deviceRespondBo.getResult().getSignal()).collect(Collectors.toList()));
        //         log.info("=============== post oneline，sync，syncAll operation spend:{}",System.currentTimeMillis() - begin);
        //     }).start();

        // }
//        Set<Long> edgeGatewayIds = new HashSet<>();
//        edgeGatewayEntities.stream().forEach((e) -> {
//            edgeGatewayIds.add(e.getId());
//        });
//        edgeGatewayService.batchUpdateGatewayRunTimeInfo(edgeGatewayIds,tenantIsolation);
        return Result.ok(respondBoList);
    }

    private void checkInfoBefore(TenantIsolation tenantIsolation, Map<Long, Future<Result<Long>>> futureMap, List<DeviceEntity> deviceEntities, Map<Long, DeviceRespondBo> deviceBos, Map<Long, String> gwNameMap) {
        long begin = System.currentTimeMillis();
        deviceEntities.forEach(deviceEntity -> {
            CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(deviceEntity.getTenantId());
            Result<Device> deviceResult = Device.checkInfoToEvent(
                    deviceEntity,
                    commonFetcher
            );
        });
        long end = System.currentTimeMillis();
        log.info("============={}",end - begin);
    }

    // private void deviceBatchSync(TenantIsolation tenantIsolation, Map<Long, Future<Result<Long>>> futureMap, List<DeviceEntity> deviceEntities, Map<Long, DeviceRespondBo> deviceBos, Map<Long, String> gwNameMap) {
    //     deviceEntities.forEach(deviceEntity -> {
    //         DeviceRespondBo build = DeviceRespondBo.builder().edgeGatewayName(gwNameMap.get(deviceEntity.getEdgeGatewayId())).build();
    //         try {
    //             Future<Result<Long>> resultFuture = deviceOptionService.doDeviceSync(1,tenantIsolation, deviceEntity,null,null,null,null,null,null,null,null,null,null,null,null);
    //             futureMap.put(deviceEntity.getId(), resultFuture);
    //             build.setResult(Result.ok());
    //         } catch (BizException e) {
    //             build.setResult(Result.error(e.getCode(), e.getMessage()));
    //         }
    //         BeanUtilsIntensifier.propertyInjection(deviceEntity, build);
    //         deviceBos.put(deviceEntity.getId(),build);
    //     });
    // }

    private void deviceBatchAllSync2(TenantIsolation tenantIsolation, List<DeviceEntity> deviceEntities, Map<Long, DeviceRespondBo> deviceBos, Map<Long, String> gwNameMap) {
        long start = System.currentTimeMillis();
        log.info("init DeviceCheckInfoContext,device size:{}", deviceEntities.size());
        DeviceCheckInfoContext context=new DeviceCheckInfoContext(deviceEntities);
        log.info("init DeviceCheckInfoContext end. spend:{}ms", System.currentTimeMillis() - start);

        Map<Long,Device> checkDeviceMap = new HashMap<>();
        Map<Long,DeviceEntity> deviceEntityMap = new HashMap<>();
        long start1 = System.currentTimeMillis();
        log.info("start check before sync");
        deviceEntities.forEach(deviceEntity -> {
            DeviceRespondBo build = DeviceRespondBo.builder().edgeGatewayName(gwNameMap.get(deviceEntity.getEdgeGatewayId())).build();
            try {
                Result<Device> result = deviceOptionService.doDeviceSync2(tenantIsolation, deviceEntity, context);
                if (result.getSignal()){
                    checkDeviceMap.put(deviceEntity.getId(),result.getResult());
                    deviceEntityMap.put(deviceEntity.getId(), deviceEntity);
                    build.setResult(Result.ok());
                }else{
                    build.setResult(Result.error(result.getServiceCode(), result.getMessage()));
                }
            } catch (BizException e) {
                build.setResult(Result.error(e.getCode(), e.getMessage()));
            }
            BeanUtilsIntensifier.propertyInjection(deviceEntity, build);
            deviceBos.put(deviceEntity.getId(),build);
        });
        log.info("end check before sync. spend:{}ms", System.currentTimeMillis() - start1);
        long start2 = System.currentTimeMillis();
        log.info("start doBatchSync2...");
        doBatchSync2(tenantIsolation,checkDeviceMap,deviceEntityMap);
        log.info("end doBatchSync2 spend:{}ms",System.currentTimeMillis()-start2);
    }



    private void doBatchSync2(TenantIsolation tenant,Map<Long,Device> checkDeviceMap, Map<Long,DeviceEntity> deviceEntityMap){
        Map<Long,ComputeTaskEntity> deviceIdTaskMap = new HashMap<>();
        long start=System.currentTimeMillis();
        for(Long deviceId:deviceEntityMap.keySet()){
            long start1 = System.currentTimeMillis();
            //  if (deviceResult.getSignal()) {
            Device deviceRun = checkDeviceMap.get(deviceId);
            DeviceEntity device = deviceEntityMap.get(deviceId);
            if(deviceRun==null){
                log.warn("设备验证结果不存在，deviceId:{}",deviceId);
                continue;
            }
            if(device==null){
                log.warn("设备不存在，deviceId:{}",deviceId);
                continue;
            }
            DeviceRuntimeMetadataField metadata = deviceRun.createRuntimeMetadata();
            MemoryCache.setDeviceRuntimeMetaDataCache(deviceId, metadata);

            // device.setRuntimeMetadata(metadata);
            //获取计算任务
            Result<ComputeTaskEntity> computeTaskResult = deviceRun.createComputeTask();

            if (!computeTaskResult.getSignal()) {
                log.warn("获取计算任务失败，deviceId:{},errCode:{},errMsg:{}",deviceId,computeTaskResult.getServiceCode(), computeTaskResult.getMessage());
                continue;
            }

            //删除旧计算任务，插入新的计算任务
            ComputeTaskEntity task = computeTaskResult.getResult();
            deviceIdTaskMap.put(deviceId, task);

            device.setStatus(StatusEnum.ONLINE.getValue());
            device.setLastSyncTime(LocalDateTime.now());
            device.setSyncStatus(SyncStatusEnum.HAVE_SYNC.getValue());
            // mapper.updateById(device);
            deviceService.initDeviceTwinData(deviceRun, device, tenant);
            RedisUtil redisUtil = new RedisUtil(redisTemplate, null);
            Set<String> finalKeys = new HashSet<>();
            // String deviceBindKey = String.format(RedisConstant.DEVICE_BIND, device.getId());
            // finalKeys.add(deviceBindKey);
            // Set<Object> deviceBind = redisTemplate.opsForSet().members(deviceBindKey);
            Set<String> deviceBind = MemoryCache.getDeviceBind(device.getId());
            if (CollectionUtil.isNotEmpty(deviceBind)) {
                deviceBind.forEach(bind -> redisUtil.hdel((String) bind,device.getId().toString()));
            }

            MemoryCache.deleteSubscriptionListByDeviceId(deviceRun.getId());
            // 优化：使用索引键获取设备的订阅注册表键，避免scan操作
            // Set<String> subscriptionKeys = RedisSubscriptionIndexUtil.getDeviceSubscriptionKeys(redisTemplate, deviceRun.getId());
            // if (CollectionUtil.isNotEmpty(subscriptionKeys)) {
            //     finalKeys.addAll(subscriptionKeys);
            // }
            // 删除索引键本身
            // finalKeys.add(RedisSubscriptionIndexUtil.getSubscriptionIndexKey(deviceRun.getId()));





            finalKeys.add(String.format(RedisConstant.SUBSCRIPTION_ENABLE,tenant.getTenantId(),deviceRun.getId()));
            // String labelPropertyKey = String.format(RedisConstant.LABEL_PROPERTY_MAPPING, deviceRun.getId());
            // finalKeys.add(labelPropertyKey);
            redisTemplate.delete(finalKeys);

            MemoryCache.deleteDeviceLabelPropertyMap(device.getId());
            MemoryCache.deleteDeviceBind(device.getId());
            //计算任务
            MemoryCache.setComputeTask(deviceRun.getId(), task);



            // redisUtil.set(String.format(RedisConstant.EVENT_REGISTRY,deviceRun.getId()),task);
            Set<String> deviceBindKeys = new HashSet<>();
            // log.info("device group key set:{}",deviceRun.getGroupMap().keySet());
            //分组-设备映射表
            deviceRun.getGroupMap().forEach((group,labelNames) -> {

                int i = group.indexOf(".");
                String channel = group.substring(0, i);
                String groupName = group.substring(i + 1);
                Map<String,Object> value = new HashMap<>();
                String groupDeviceMapping = String.format(RedisConstant.GROUP_DEVICE_MAPPING, deviceRun.getDeviceEntity().getEdgeGatewayId(), channel, groupName);
                value.put(deviceRun.getId().toString(),labelNames);
                MemoryCache.setGroupDeviceMap(groupDeviceMapping,value);
                deviceBindKeys.add(groupDeviceMapping);
            });
            if (!deviceBindKeys.isEmpty()) {
                MemoryCache.setDeviceBind(device.getId(), deviceBindKeys);
            }
            //标签-属性映射表
            // redisUtil.set(labelPropertyKey,deviceRun.getNameMap());
            MemoryCache.setDeviceLabelPropertyMap(deviceRun.getId(), deviceRun.getNameMap());


            Map<String, List<Subscription>> subscriptionRegistryMap=deviceRun.getSubscriptionRegistry();
            subscriptionRegistryMap.forEach((k,v) -> {
                MemoryCache.addSubscriptionListByDeviceId(deviceRun.getId(),k, v);
            });


            List<Subscription> subscriptions = deviceRun.getDeviceModel().getSubscriptions();
            if (CollectionUtil.isNotEmpty(subscriptions)) {
                Set<Long> set = subscriptions.stream().filter(subscription -> subscription.getEnable()).map(Subscription::getId).collect(Collectors.toSet());
                if (CollUtil.isNotEmpty(set)) {
                    redisTemplate.opsForSet().add(String.format(RedisConstant.SUBSCRIPTION_ENABLE,tenant.getTenantId(),deviceRun.getId()),set.toArray());
                }
            }
            //  redisUtil.set(String.format(RedisConstant.DEVICE_ENABLE,deviceRun.getId()),true);
            MemoryCache.putDeviceEnable(deviceRun.getId(),true);
            subscriptions.stream().forEach(t -> {
                Integer eventType = t.getEventType();
                SubscriptionEventTypeEnum eventTypeEnum = SubscriptionEventTypeEnum.typeOfValue(eventType);
                if(SubscriptionEventTypeEnum.NO_CHANGE.equals(eventTypeEnum)){
                    Integer noChangeSeconds = t.getNoChangeSeconds();
                    String[] split = t.getProperties().split(",");
                    for(String s:split){
                        NoChangeInfo noChangeInfo = new NoChangeInfo(deviceRun.getId(), s);
                        String key = noChangeInfo.toKey();
                        redisUtil.set(key, true, noChangeSeconds);
                    }
                }
            });


            subscriptions.forEach(subscri->{dataConversionService.codePreload(subscri.getId(),subscri.getDataConversionCode()); });

            long spend = System.currentTimeMillis() - start1;
            if(spend>2000){
                log.info("AAAAAAA###### 486 deviceId:{} spend:{}",deviceId,spend);
            }
            // Deprecated 给第一版的设备云管的设备状态变化通知
            // ModelDpo modelDpo = deviceRun.getDeviceModel().toDpo();
            // changeNoticeService.changeNotice(tenant.getTenantId(), modelDpo, ChangeTypeEnum.MODEL, ChangeSubjectEnum.DEVICE);


            // return Result.ok();
        // }
        // throw new BizException(deviceResult.getServiceCode(), deviceResult.getMessage());
        }

        log.info("AAAAAAA###### 493 spend:{}ms", System.currentTimeMillis() - start);
        //计算任务更新
        if(CollectionUtil.isNotEmpty(deviceIdTaskMap)){
            taskService.batchUpdateComputeTask(tenant.getTenantId(),deviceIdTaskMap);
        }

//   long start8=System.currentTimeMillis();
//         redisTemplate.delete(deviceRuntimeMetadataMap.keySet());
//         redisTemplate.opsForValue().multiSet(deviceRuntimeMetadataMap);
//  log.info("AAAAAAA>>>554,spendTime:{}ms",System.currentTimeMillis()-start8);
        //更新设备
        if(CollectionUtil.isNotEmpty(deviceIdTaskMap)){
            // mapper.batchUpdateDevices(deviceEntityMap.values());
            deviceService.ayncBatchUpdateDevice(deviceEntityMap.values(),tenant);
        }

    }
    private void deviceBatchAllSync(TenantIsolation tenantIsolation, Map<Long, Future<Result<Long>>> futureMap, List<DeviceEntity> deviceEntities, Map<Long, DeviceRespondBo> deviceBos, Map<Long, String> gwNameMap) {
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());

        Map<Long,List<LabelBindRelationEntity>> labelBindRelationEntityMap = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), LabelBindRelationEntity.class).stream()
                .collect(Collectors.groupingBy(LabelBindRelationEntity::getDeviceId));


        Map<Long,EdgeGatewayEntity> edgeGatewayEntityMap =  commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), EdgeGatewayEntity.class).stream().collect(Collectors.toMap(
                EdgeGatewayEntity::getId,
                Function.identity(),
                (existing, replacement) -> replacement  // 如果出现重复键，使用后来的值替换已有的值
        ));

        Map<Long,List<ResourceRelationEntity>> resourceRelationEntityListMap = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), ResourceRelationEntity.class)
                .stream()
                .collect(Collectors.groupingBy(ResourceRelationEntity::getDeviceId));


        LambdaQueryWrapper<ThingModelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.and(w->w.eq(ThingModelEntity::getTenantId, tenantIsolation.getTenantId())).or()
                .eq(ThingModelEntity::getTenantId, Constant.DEFAULT_THING);
        List<ThingModelEntity> thingModelEntities = thingModelMapper.selectList(lqw);
        Map<Long,ThingModelEntity> thingModelEntityMap
                = thingModelEntities
                .stream().collect(Collectors.toMap(ThingModelEntity::getId,
                        Function.identity(),
                        (existing, replacement) -> replacement  // 如果出现重复键，使用后来的值替换已有的值
                ));
        //根据继承modelId获取到的map  有异议
        Map<Long,List<ThingModelInheritEntity>> thingModelInheritEntityMap = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), ThingModelInheritEntity.class)
                .stream()
                .collect(Collectors.groupingBy(ThingModelInheritEntity::getThingModelId));

        List<ThingServiceEntity> thingServiceEntityList = thingServiceMapper.selectList( new LambdaQueryWrapper<ThingServiceEntity>()
                .and(w->w.eq(ThingServiceEntity::getTenantId, tenantIsolation.getTenantId())).or()
                .eq(ThingServiceEntity::getTenantId, Constant.DEFAULT_THING));
        Map<Long,List<ThingServiceEntity>> thingServiceEntityMap =thingServiceEntityList
                .stream()
                .collect(Collectors.groupingBy(ThingServiceEntity::getThingModelId));
        Map<Long,List<SubscriptionEntity>> subscriptionEntityMap = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), SubscriptionEntity.class)
                .stream()
                .collect(Collectors.groupingBy(SubscriptionEntity::getDirectlyModelId));
        Map<Long,List<DeviceModelInheritEntity>> deviceModelinheritEntityListMap = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), DeviceModelInheritEntity.class)
                .stream()
                .collect(Collectors.groupingBy(DeviceModelInheritEntity::getDeviceId));
        Map<Long,List<DeviceServiceEntity>> deviceServiceEntityMap =
                commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), DeviceServiceEntity.class)
                        .stream()
                        .collect(Collectors.groupingBy(DeviceServiceEntity::getDeviceId));
        List<LabelEntity> labelEntityList = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), LabelEntity.class);
        List<ChannelEntity> channelEntitieList = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), ChannelEntity.class);
        List<LabelGroupEntity> labelGroupEntityList = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), LabelGroupEntity.class);
        deviceEntities.forEach(deviceEntity -> {
            DeviceRespondBo build = DeviceRespondBo.builder().edgeGatewayName(gwNameMap.get(deviceEntity.getEdgeGatewayId())).build();
            try {
                Future<Result<Long>> resultFuture = deviceOptionService.doDeviceSync(2,tenantIsolation, deviceEntity
                        ,labelBindRelationEntityMap, edgeGatewayEntityMap,resourceRelationEntityListMap,thingModelEntityMap,
                        thingModelInheritEntityMap,thingServiceEntityMap,subscriptionEntityMap,deviceModelinheritEntityListMap,
                        deviceServiceEntityMap,labelEntityList,channelEntitieList,labelGroupEntityList

                );
                futureMap.put(deviceEntity.getId(), resultFuture);
                build.setResult(Result.ok());
            } catch (BizException e) {
                build.setResult(Result.error(e.getCode(), e.getMessage()));
            }
            BeanUtilsIntensifier.propertyInjection(deviceEntity, build);
            deviceBos.put(deviceEntity.getId(),build);
        });
    }

    @Autowired @Lazy
    private RedisTemplate<String, Object> redisTemplate;

    private Result<List<DeviceRespondBo>> doDeviceBatchOnline(TenantIsolation tenantIsolation, List<DeviceEntity> deviceEntities, Map<Long, String> gwNameMap,  Map<Long, DeviceRespondBo> deviceBos) {
        List<DeviceRespondBo> deviceRespondBos = new ArrayList<>();
        Iterator<DeviceEntity> iterator = deviceEntities.iterator();
        List<DeviceEntity> needAcivationList = new ArrayList<>();
        List<DeviceEntity> enableOnly = new ArrayList<>();
        while (iterator.hasNext()) {
            DeviceEntity next = iterator.next();
            if (Objects.requireNonNull(StatusEnum.typeOfValue(next.getStatus())) == StatusEnum.ONLINE) {
                iterator.remove();
                continue;
            }
            if (next.getStatus().equals(StatusEnum.INACTIVATED.getValue())) {
                needAcivationList.add(next);
            }else {
                enableOnly.add(next);
            }
        }
        if (CollectionUtil.isEmpty(deviceEntities)) {
            return Result.ok(deviceRespondBos);
        }
        if (!enableOnly.isEmpty()) {
            List<Long> ids = BeanUtilsIntensifier.getIds(enableOnly, DeviceEntity::getId);
            // RedisUtil redisUtil = new RedisUtil(redisTemplate, null);
            ids.forEach(id -> MemoryCache.putDeviceEnable(id, true));
            taskService.batchenableDeviceComputeTask(tenantIsolation, ids);
            new LambdaUpdateChainWrapper<>(mapper)
                    .eq(DeviceEntity::getTenantId,tenantIsolation.getTenantId())
                    .in(DeviceEntity::getId, ids)
                    .set(DeviceEntity::getStatus, StatusEnum.ONLINE.getValue())
                    .update();

        }
        if (!needAcivationList.isEmpty()) {
            // deviceBatchAllSync(tenantIsolation,futureMap,needAcivationList,deviceBos,gwNameMap);
            deviceBatchAllSync2(tenantIsolation,needAcivationList,deviceBos,gwNameMap);
        }
        return Result.ok(deviceRespondBos);
    }

    private void getFutureResult(Map<Long, Future<Result<Long>>> futureMap, Map<Long, DeviceRespondBo> deviceBos, String optionName) {
        if (MapUtil.isNotEmpty(futureMap)) {
            futureMap.forEach( (id,future) ->{
                DeviceRespondBo deviceRespondBo = deviceBos.get(id);
                try {
                    Result<Long> result = future.get();
                    if (!result.getSignal()) {
                        deviceRespondBo.setResult(Result.error(result.getServiceCode(),result.getMessage()));
                    }
                } catch (InterruptedException e) {
                    log.warn("{}失败11,id:{},msg:{}", optionName, id, e);
                    deviceRespondBo.setResult(Result.error(ServiceCodeEnum.DEVICE_OPTION_ERROR));
                } catch (ExecutionException e) {
                    if (e.getCause() instanceof BizException){
                        BizException bizException = (BizException) e.getCause();
                        log.info("{}失败22,id:{},msg:{}", optionName, id, e.getMessage());
                        deviceRespondBo.setResult(Result.error(bizException.getCode(),bizException.getMessage()));
                    }else {
                        log.error("{}失败33,id:{},msg:{}", optionName, id, e);
                        
                        deviceRespondBo.setResult(Result.error(ServiceCodeEnum.DEVICE_OPTION_ERROR));
                    }
                }
            });
        }
    }

    @Override
    public Result<List<DeviceRespondBo>> deviceBatchOnline(TenantIsolation tenantIsolation, List<Long> ids) {
        return deviceBatchOption(tenantIsolation, ids, DeviceOptionEnum.ONLINE);
    }

    @Override
    public Result<List<DeviceRespondBo>> deviceBatchOffline(TenantIsolation tenantIsolation, List<Long> ids) {
        return deviceBatchOption(tenantIsolation, ids, DeviceOptionEnum.OFFLINE);
    }

    @Override
    @AuditLog(action = ActionEnum.SYNC, target = AuditTargetEnum.GATEWAY, details = "同步CommonType网关")
    public void syncCommonType() {
        ThingModelInheritEntity entity = new ThingModelInheritEntity();
        entity.setInheritThingModelId(Constant.DEFAULT_THING);
        List<ThingModelInheritEntity> result = thingModelInheritService.list(entity).getResult();
        if (CollUtil.isNotEmpty(result)){
            Map<Long, List<ThingModelInheritEntity>> collect = result.stream().collect(Collectors.groupingBy(i -> i.getTenantId()));
            for (Map.Entry<Long, List<ThingModelInheritEntity>> longListEntry : collect.entrySet()) {
                Long tenantId = longListEntry.getKey();
                CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);
                // Set<Long> deviceIdSet = new HashSet<>();
                // for (ThingModelInheritEntity thingModelInheritEntity : longListEntry.getValue()) {
                //     List<Long> deviceIds = ThingModel.listAllBeInheritDeviceIdById(thingModelInheritEntity.getThingModelId(), commonFetcher);
                //     deviceIdSet.addAll(deviceIds);
                // }
                TenantIsolation tenantIsolation = new TenantIsolation();
                tenantIsolation.setTenantId(tenantId);
                // deviceBatchSync(tenantIsolation,new ArrayList<>(deviceIdSet));
                deviceBatchSyncAll(tenantIsolation);
            }
        }
    }

    @Override
    public Result<List<DeviceRespondBo>> syncByThingModel(TenantIsolation tenantIsolation, Long thingModelId) {
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());
        List<Long> deviceIds = ThingModel.listAllBeInheritDeviceIdById(thingModelId, commonFetcher);
        return deviceBatchSync(tenantIsolation,deviceIds);
    }


    public Result<Void> syncBeforeCheck(TenantIsolation tenantIsolation,List<Long> deviceIdList){
        StringBuilder message = new StringBuilder();
        Result<List<DeviceModelInheritEntity>> deviceModelInheritEntityResult = deviceModelInheritService.listByDeviceIds(tenantIsolation.getTenantId(),deviceIdList);
        if(!deviceModelInheritEntityResult.getSignal()){
            return Result.error(deviceModelInheritEntityResult.getMessage());
        }
        List<DeviceModelInheritEntity> deviceInheritEntityList = deviceModelInheritEntityResult.getResult();

        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());

        List<InstanceRedirectEntity> instanceRedirectEntityList = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), InstanceRedirectEntity.class);

        List<Long> redirectIdList  = instanceRedirectEntityList.stream().map(InstanceRedirectEntity::getId).collect(Collectors.toList());
        //自身的回调
        Preloader<SubscriptionEntity> selfPreloader = commonFetcher
                .preloader( "deleted", 0, SubscriptionEntity.class)
                .preload("directly_model_id", deviceIdList);
        List<SubscriptionEntity> selfSubscriptionEntities = selfPreloader.list();

        //租户下的设备服务
        Preloader<DeviceServiceEntity> deviceServicePreloader = commonFetcher
                .preloader( "deleted", 0, DeviceServiceEntity.class);
        List<DeviceServiceEntity> deviceServiceEntityList = deviceServicePreloader.list();

        Map<Long, List<DeviceServiceEntity>> deviceServiceEntityMap = deviceServiceEntityList.stream().collect(Collectors.groupingBy(DeviceServiceEntity::getDeviceId));

        //把租户下所有的模型查出来
        List<ThingModelInheritEntity> thingModelInheritEntityList = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), ThingModelInheritEntity.class);

        //继承的模型ID
        Set<Long> modelIdList = deviceInheritEntityList.stream().map(DeviceModelInheritEntity::getInheritThingModelId).collect(Collectors.toSet());
        Set<Long> allModelId = new HashSet<>();
        for(Long id : modelIdList){
            allModelId.add(id);
            thingModelService.findParentIds(thingModelInheritEntityList,id,allModelId);
        }

        //租户下的设备服务

        List<ThingServiceEntity> thingServiceEntityList = thingServiceMapper.selectList( new LambdaQueryWrapper<ThingServiceEntity>()
                .and(w->w.eq(ThingServiceEntity::getTenantId, tenantIsolation.getTenantId())).or()
                .eq(ThingServiceEntity::getTenantId, Constant.DEFAULT_THING));

        Map<Long,List<ThingServiceEntity>> thingServiceEntityMap = thingServiceEntityList.stream().collect(Collectors.groupingBy(ThingServiceEntity::getThingModelId));

        for(SubscriptionEntity subscriptionEntity :selfSubscriptionEntities){
            if(subscriptionEntity.getOutType() == 0 && !redirectIdList.contains(subscriptionEntity.getCallbackId())){
                message.append("设备id :" + subscriptionEntity.getDirectlyModelId() + " 不存在该关联订阅 callBackId:"+subscriptionEntity.getCallbackId()+" redirectIdList :"+redirectIdList.stream().map(Object::toString).collect(Collectors.joining(",")));
            }
            if(subscriptionEntity.getOutType() == 1 && ObjectUtil.isNotNull(subscriptionEntity.getTargetDevice())){
               Long deviceId =  subscriptionEntity.getTargetDevice() != 0 ? subscriptionEntity.getTargetDevice() : subscriptionEntity.getDirectlyModelId();
               checkServiceName(message, deviceServiceEntityMap, subscriptionEntity, deviceId,thingServiceEntityMap,allModelId);
            }
        }
        if(CollectionUtil.isNotEmpty(allModelId)){
            List<Long> queryList = allModelId.stream().collect(Collectors.toList());

            Preloader<SubscriptionEntity> preloader = commonFetcher
                    .preloader( "deleted", 0, SubscriptionEntity.class)
                    .preload("directly_model_id", queryList);
            List<SubscriptionEntity> subscriptionEntities = preloader.list();

            for(SubscriptionEntity subscriptionEntity :subscriptionEntities){
                if(subscriptionEntity.getOutType() == 0 && !redirectIdList.contains(subscriptionEntity.getCallbackId())){
                    message.append("模型id :" + subscriptionEntity.getDirectlyModelId() + " 不存在该关联订阅 callBackId:"+subscriptionEntity.getCallbackId()+" redirectIdList :"+redirectIdList.stream().map(Object::toString).collect(Collectors.joining(",")));
                }
                if(subscriptionEntity.getOutType() == 1 && ObjectUtil.isNotNull(subscriptionEntity.getTargetDevice())){
                    Long id =  subscriptionEntity.getTargetDevice() != 0 ? subscriptionEntity.getTargetDevice() : subscriptionEntity.getDirectlyModelId();
                    if(subscriptionEntity.getTargetDevice() != 0 ){
                        checkServiceName(message, deviceServiceEntityMap, subscriptionEntity, id,thingServiceEntityMap,allModelId);
                    }else {
                        Map<String, ThingServiceEntity> thingNameMap = getThingService(thingServiceEntityMap,allModelId);
                        if(ObjectUtil.isNull(thingNameMap.get(subscriptionEntity.getTargetService()))){
                            message.append("模型id :" + subscriptionEntity.getDirectlyModelId() + " 不存在该关联服务； ");
                        }
                    }
                }
            }
        }
        if(!StringUtils.isEmpty(message.toString())){
            return Result.error(message.toString());
        }
        return Result.ok();
    }

    private void checkServiceName(StringBuilder message, Map<Long, List<DeviceServiceEntity>> deviceServiceEntityMap, SubscriptionEntity subscriptionEntity, Long id,Map<Long,List<ThingServiceEntity>> thingServiceEntityMap,Set<Long> allModelId) {
        List<DeviceServiceEntity> deviceServiceEntityList = new ArrayList<>();
        if(ObjectUtil.isNotNull(deviceServiceEntityMap.get(id))){
            deviceServiceEntityList = deviceServiceEntityMap.get(id);
        }
        Map<String, ThingServiceEntity> thingNameMap = getThingService(thingServiceEntityMap,allModelId);
        Map<String,DeviceServiceEntity> deviceNameMap = deviceServiceEntityList.stream().collect(Collectors.toMap(DeviceServiceEntity::getServiceName, person -> person, // 值的提取器
                (existing, replacement) -> existing // 合并函数，保留已存在的值
        ));
        if(ObjectUtil.isNotNull(deviceNameMap.get(subscriptionEntity.getTargetService())) && ObjectUtil.isNotNull(thingNameMap.get(subscriptionEntity.getTargetService()))){
            message.append("设备id :" + subscriptionEntity.getDirectlyModelId() + " 不存在该关联服务； ");
        }
    }

    private Map<String,ThingServiceEntity> getThingService(Map<Long, List<ThingServiceEntity>> thingServiceEntityMap,Set<Long> allModelId) {
        List<ThingServiceEntity> thingServiceEntityList = new ArrayList<>();
        for(Long modelId :allModelId){
            if(ObjectUtil.isNotNull(thingServiceEntityMap.get(modelId))){
                thingServiceEntityList.addAll(thingServiceEntityMap.get(modelId));
            }
        }
        Map<String, ThingServiceEntity> thingNameMap = thingServiceEntityList.stream()
                .collect(Collectors.toMap(
                        ThingServiceEntity::getServiceName, // 键的提取器
                        person -> person, // 值的提取器
                        (existing, replacement) -> existing // 合并函数，保留已存在的值
                ));
        return thingNameMap;
    }


    @Override
    public Result<Void> syncBeforeCheckInfo(TenantIsolation tenantIsolation) {
        List<DeviceEntity> deviceEntities = getDeviceByIds(tenantIsolation.getTenantId(), null);
        if (deviceEntities.isEmpty()) {
            return Result.ok();
        }
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());

        Map<Long,List<LabelBindRelationEntity>> labelBindRelationEntityMap = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), LabelBindRelationEntity.class).stream()
                .collect(Collectors.groupingBy(LabelBindRelationEntity::getDeviceId));


        Map<Long,EdgeGatewayEntity> edgeGatewayEntityMap =  commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), EdgeGatewayEntity.class).stream().collect(Collectors.toMap(
                EdgeGatewayEntity::getId,
                Function.identity(),
                (existing, replacement) -> replacement  // 如果出现重复键，使用后来的值替换已有的值
        ));

        Map<Long,List<ResourceRelationEntity>> resourceRelationEntityListMap = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), ResourceRelationEntity.class)
                .stream()
                .collect(Collectors.groupingBy(ResourceRelationEntity::getDeviceId));


        LambdaQueryWrapper<ThingModelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.and(w->w.eq(ThingModelEntity::getTenantId, tenantIsolation.getTenantId())).or()
                .eq(ThingModelEntity::getTenantId, Constant.DEFAULT_THING);
        List<ThingModelEntity> thingModelEntities = thingModelMapper.selectList(lqw);
        Map<Long,ThingModelEntity> thingModelEntityMap
                =thingModelEntities
                .stream().collect(Collectors.toMap(ThingModelEntity::getId,
                Function.identity(),
                (existing, replacement) -> replacement  // 如果出现重复键，使用后来的值替换已有的值
        ));
        //根据继承modelId获取到的map  有异议
        Map<Long,List<ThingModelInheritEntity>> thingModelInheritEntityMap = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), ThingModelInheritEntity.class)
                .stream()
                .collect(Collectors.groupingBy(ThingModelInheritEntity::getInheritThingModelId));

        List<ThingServiceEntity> thingServiceEntityList = thingServiceMapper.selectList( new LambdaQueryWrapper<ThingServiceEntity>()
                .and(w->w.eq(ThingServiceEntity::getTenantId, tenantIsolation.getTenantId())).or()
                .eq(ThingServiceEntity::getTenantId, Constant.DEFAULT_THING));
        Map<Long,List<ThingServiceEntity>> thingServiceEntityMap = thingServiceEntityList
                .stream()
                .collect(Collectors.groupingBy(ThingServiceEntity::getThingModelId));
        Map<Long,List<SubscriptionEntity>> subscriptionEntityMap = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), SubscriptionEntity.class)
                .stream()
                .collect(Collectors.groupingBy(SubscriptionEntity::getDirectlyModelId));
        Map<Long,List<DeviceModelInheritEntity>> deviceModelinheritEntityListMap = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), DeviceModelInheritEntity.class)
                .stream()
                .collect(Collectors.groupingBy(DeviceModelInheritEntity::getDeviceId));
        Map<Long,List<DeviceServiceEntity>> deviceServiceEntityMap =
                commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), DeviceServiceEntity.class)
                .stream()
                .collect(Collectors.groupingBy(DeviceServiceEntity::getDeviceId));
        List<LabelEntity> labelEntityList = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), LabelEntity.class);
        List<ChannelEntity> channelEntitieList = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), ChannelEntity.class);
        List<LabelGroupEntity> labelGroupEntityList = commonFetcher.list("tenant_id", tenantIsolation.getTenantId(), LabelGroupEntity.class);
        long begin = System.currentTimeMillis();
        try {
            deviceEntities.forEach(deviceEntity -> {
                Result<Device> deviceResult = Device.checkInfoToEventNew(
                        deviceEntity,
                        labelBindRelationEntityMap,
                        edgeGatewayEntityMap,
                        resourceRelationEntityListMap,
                        thingModelEntityMap,
                        thingModelInheritEntityMap,
                        thingServiceEntityMap,
                        subscriptionEntityMap,
                        deviceModelinheritEntityListMap,
                        deviceServiceEntityMap,
                        labelEntityList,
                        channelEntitieList,
                        labelGroupEntityList
                );
                if(!deviceResult.getSignal()){
                    System.out.println(deviceResult);
                }
            });
        }catch (Exception e){
            System.out.println(e);
        }

        long end  = System.currentTimeMillis();
        log.info("=============syncBeforeCheckInfo cost {} ms", end - begin);
        return Result.ok();
    }

}
