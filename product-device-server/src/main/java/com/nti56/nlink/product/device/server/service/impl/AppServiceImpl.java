package com.nti56.nlink.product.device.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.JwtUserInfoUtils;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.constant.Constant;
import com.nti56.nlink.product.device.server.entity.InstanceRedirectEntity;
import com.nti56.nlink.product.device.server.model.cloudnest.*;
import com.nti56.nlink.product.device.server.service.AppService;
import com.nti56.nlink.product.device.server.service.IInstanceRedirectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Set;

@Slf4j
@Service
public class AppServiceImpl implements AppService {

    private static final int BUSSINESS_ROLE_TYPE = 1;

    private static String APP_CODE = "OT";

    private static String DEFAULT_ROLE_NAME = "系统管理员";

    private static boolean IS_NEW_ROLE = false;

    private static int MENU_CATEGORY = 1; // 1:pc端菜单 2:移动端菜单

    @Value("${user-center.base-url}")
    private String baseUrl;

    @Value("${user-center.add-member}")
    private String addMember;

    @Value("${user-center.role-list}")
    private String roleList;

    @Value("${user-center.menu-list}")
    private String menuList;

    @Value("${user-center.update-role-menu}")
    private String updateRoleMenu;


    @Value("${user-center.role-member-list}")
    private String roleMemberList;

    @Value("${user-center.add-role}")
    private String addRole;

    @Value("${mqtt.host}")
    private String mqttHost;

    @Value("${mqtt.port}")
    private String mqttPort;

    @Value("${mqtt.username}")
    private String mqttUsername;

    @Value("${mqtt.password}")
    private String mqttPassword;

    @Autowired
    @Qualifier("restTemplate")
    private RestTemplate restHttpTemplate;

    @Autowired
    private IInstanceRedirectService redirectService;

    public Result<Boolean> init(Long tenantId) {
        // 初始化回调数据
        initRedirect(tenantId);

        Long userId = getUserId();
        //获取角色列表，判断是否有 系统管理员
        Result<Long> defaultRoleId = getDefaultRoleId(tenantId);
        if (defaultRoleId.getSignal()) {
            Long roleId = defaultRoleId.getResult();
            if (roleId != -1L) {
                Result<Boolean> isExist = roleContainsUser(tenantId, roleId, userId);
                if (isExist.getSignal() && !isExist.getResult()) {
                    log.info("租户:{} 下的角色:{} 未添加用户:{} ", tenantId, roleId, userId);
                    //角色未添加此用户，则添加
                    Result<Boolean> addUserToRoleResult = addUserToRole(tenantId, roleId, userId);
                    if (!addUserToRoleResult.getSignal()) {
                        log.warn("租户:{} 下的角色:{} 添加用户:{} 失败",tenantId, roleId, userId);
                        return Result.error(addUserToRoleResult.getMessage());
                    }
                    log.info("租户:{} 下的角色:{} 添加用户:{} 成功", tenantId, roleId, userId);
                }
                //判断是否创建了默认角色，如果创建了，就授权菜单
                if (IS_NEW_ROLE) {
                    //创建默认角色后，授权菜单
                    Result<Boolean> grantResult = grantDefaultRoleMenu(tenantId, roleId);
                    if (grantResult.getSignal()) {
                        return Result.ok(grantResult.getResult());
                    }
                }else{
                    return Result.ok(true,"默认角色以存在，延用原来的权限");
                }
            }
        }
        return Result.error("获取默认租户失败");
    }


    private Result<Boolean> roleContainsUser(Long tenantId, Long roleId, Long userId) {
        boolean isExit = false;
        ApiResult<List<ApiRoleMemberResponse>> result = listRoleMember(tenantId, roleId);
        log.info("listRoleMember result:{}", JSON.toJSONString(result));
        if (result.getSucceeded()) {
            if (result.getData() != null) {
                isExit = result.getData().stream().filter(x -> x.getUserId().equals(userId.toString())).findFirst().isPresent();
            }
            log.info("roleContainsUser result:{}", isExit);
            return Result.ok(isExit);
        }
        log.error("roleContainsUser error:{}", result.getMessage());
        return Result.error(result.getMessage());
    }


    private Long getUserId() {
        return JwtUserInfoUtils.getUserId();
    }


    private Result<Boolean> addUserToRole(Long tenantId, Long roleId, Long userId) {
        ApiRoleMemberAddRequest request = new ApiRoleMemberAddRequest();
        request.setRoleId(roleId);
        Set<Long> userIds = Sets.newHashSet();
        userIds.add(userId);
        request.setUserIds(userIds);
        ApiResult<Boolean> booleanApiResult = addRoleMember(tenantId, request);
        if (booleanApiResult.getSucceeded()) {
            return Result.ok(booleanApiResult.getData());
        }
        log.error("addUserToRole error:{}", booleanApiResult.getMessage());
        return Result.error(booleanApiResult.getMessage());

    }


    /**
     * 创建默认角色 系统管理员
     *
     * @param tenantId
     * @return
     */
    private Result<Long> createDefaultRole(Long tenantId) {
        ApiAddOrEditRoleRequest request = new ApiAddOrEditRoleRequest();
        request.setAppcode(APP_CODE);
        request.setRoleName(DEFAULT_ROLE_NAME);
        request.setClientId(tenantId.toString());
        request.setRoleType(BUSSINESS_ROLE_TYPE);
        request.setRoleDescribe("默认系统管理员角色");
        request.setSortNo(1);
        request.setRoleCode("admin");
        ApiResult<ApiRoleResponse> save = addRole(tenantId,request);
        if (save.getSucceeded()) {
            IS_NEW_ROLE = true;
            return Result.ok(save.getData().getId());
        }
        log.error("createDefaultRole error:{}", save.getMessage());
        return Result.error(save.getMessage());

    }


    /**
     * 为默认角色授权菜单
     */
    private Result<Boolean> grantDefaultRoleMenu(Long tenantId, Long roleId) {
        List<Long> allMenuIds = getAllMenu(tenantId);
        ApiUpdateRoleMenuRequest request = new ApiUpdateRoleMenuRequest();
        request.setRoleId(roleId);
        request.setAppcode(APP_CODE);
        request.setClientId(tenantId.toString());
        request.setMenus(getRoleMenu(tenantId, roleId, allMenuIds));
        request.setMenuCategory(MENU_CATEGORY);
        ApiResult<Boolean> booleanApiResult = updateRoleMenu(tenantId,request);
        if (booleanApiResult.getSucceeded()) {
            log.info("为默认角色授权成功，角色id:{} ,菜单id列表:{}", roleId, JSON.toJSONString(allMenuIds));
            return Result.ok(booleanApiResult.getData());
        }
        log.error("grantDefaultRoleMenu error:{}", booleanApiResult.getMessage());
        return Result.error(booleanApiResult.getMessage());
    }

    private List<ApiUpdateRoleMenuRequest.SysRoleMenuDTO> getRoleMenu(Long tenantId, Long roleId, List<Long> menuIds) {
        List<ApiUpdateRoleMenuRequest.SysRoleMenuDTO> result = Lists.newArrayList();
        menuIds.forEach(x -> {
            ApiUpdateRoleMenuRequest.SysRoleMenuDTO dto = new ApiUpdateRoleMenuRequest.SysRoleMenuDTO();
            dto.setMenuId(x);
            dto.setRoleId(roleId);
            dto.setAppcode(APP_CODE);
            dto.setClientId(tenantId.toString());
            result.add(dto);
        });
        return result;
    }


    private List<Long> getAllMenu(Long tenantId) {

        List<Long> menuListIds = Lists.newArrayList();
        ApiMenuRequest request = new ApiMenuRequest();
        request.setAppcode(APP_CODE);
        ApiResult<List<ApiMenuResponse>> list = listMenu(tenantId,request);
        if (list.getSucceeded()) {
            list.getData().forEach(x -> menuListIds.add(x.getId()));
        }
        return menuListIds;
    }


    private Result<Long> getDefaultRoleId(Long tenantId) {
        Long roleId = -1L;
        ApiRoleRequest request = new ApiRoleRequest();
        request.setRoleName(DEFAULT_ROLE_NAME);
        request.setRoleType(BUSSINESS_ROLE_TYPE);
        request.setAppcode(APP_CODE);
        request.setClientId(tenantId.toString());
        ApiResult<List<ApiRoleResponse>> result = listRole(tenantId,request);
        if (result.getSucceeded()) {
            if (result.getData().size() > 0) {
                log.info("tenantId:{} result:{} 下存在默认角色，无需创建", tenantId,JSON.toJSONString(result));
                ApiRoleResponse response = result.getData().get(0);
                roleId = response.getId();
            } else {
                log.info("tenantId:{} 没有默认角色",tenantId);
                //没有系统管理角色，就创建
                Result<Long> createDefaultRoleResult = createDefaultRole(tenantId);
                if (createDefaultRoleResult.getSignal()) {
                    roleId = createDefaultRoleResult.getResult();
                } else {
                    log.error("tenantId:{},createDefaultRole failure errMsg:{}", tenantId, createDefaultRoleResult.getMessage());
                }
            }
        } else {
            log.error("tenantId:{},getRoleList failure errMsg:{}", tenantId, result.getMessage());
        }
        return Result.ok(roleId);
    }


    private ApiResult<List<ApiRoleResponse>> listRole(Long tenantId,ApiRoleRequest request) {
        try {
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add(Constant.LOGIN_TOKEN, JwtUserInfoUtils.getAuthorization());
            headers.add(Constant.CLIENT_ID, tenantId.toString());
            headers.add(Constant.APP_CODE_STR, Constant.APPCODE_HEADER);
            HttpEntity httpEntity = new HttpEntity(request, headers);
            String pathUrl = baseUrl + roleList;
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(pathUrl);
            ParameterizedTypeReference<ApiResult<List<ApiRoleResponse>>> responseType = new ParameterizedTypeReference<ApiResult<List<ApiRoleResponse>>>() {
            };
            return restHttpTemplate.exchange(builder.build().toString(), HttpMethod.POST, httpEntity, responseType).getBody();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ApiResult.failed("查询角色失败");
    }

    private ApiResult<List<ApiMenuResponse>> listMenu(Long tenantId,ApiMenuRequest request) {
        try {
            //表单提交数据
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add(Constant.LOGIN_TOKEN, JwtUserInfoUtils.getAuthorization());
            headers.add(Constant.CLIENT_ID, tenantId.toString());
            headers.add(Constant.APP_CODE_STR, Constant.APPCODE_HEADER);
            HttpEntity httpEntity = new HttpEntity(request, headers);
            String pathUrl = baseUrl + menuList;
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(pathUrl);
            ParameterizedTypeReference<ApiResult<List<ApiMenuResponse>>> responseType = new ParameterizedTypeReference<ApiResult<List<ApiMenuResponse>>>() {
            };
            return restHttpTemplate.exchange(builder.build().toString(), HttpMethod.POST, httpEntity, responseType).getBody();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }


        return ApiResult.failed("查询菜单列表失败");

    }


    private ApiResult<Boolean> updateRoleMenu(Long tenantId, ApiUpdateRoleMenuRequest request) {
        try {
            //表单提交数据
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add(Constant.LOGIN_TOKEN, JwtUserInfoUtils.getAuthorization());
            headers.add(Constant.CLIENT_ID, tenantId.toString());
            headers.add(Constant.APP_CODE_STR, Constant.APPCODE_HEADER);
            HttpEntity httpEntity = new HttpEntity(request, headers);
            String pathUrl = baseUrl + updateRoleMenu;
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(pathUrl);
            ParameterizedTypeReference<ApiResult<String>> responseType = new ParameterizedTypeReference<ApiResult<String>>() {
            };
            ApiResult<String> result = restHttpTemplate.exchange(builder.build().toString(), HttpMethod.POST, httpEntity, responseType).getBody();
            if(result.getSucceeded()&&"success".equalsIgnoreCase(result.getData())){
                return ApiResult.succeed(true);
            }
            log.error("updateRoleMenu failure errMsg:{}", result.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ApiResult.failed("更新角色的菜单权限失败");
    }


    private ApiResult<ApiRoleResponse> addRole(Long tenantId,ApiAddOrEditRoleRequest request) {
        try {
            //表单提交数据
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add(Constant.LOGIN_TOKEN, JwtUserInfoUtils.getAuthorization());
            headers.add(Constant.CLIENT_ID, tenantId.toString());
            headers.add(Constant.APP_CODE_STR, Constant.APPCODE_HEADER);
            HttpEntity httpEntity = new HttpEntity(request, headers);
            String pathUrl = baseUrl + addRole;
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(pathUrl);
            ParameterizedTypeReference<ApiResult<ApiRoleResponse>> responseType = new ParameterizedTypeReference<ApiResult<ApiRoleResponse>>() {
            };
            return restHttpTemplate.exchange(builder.build().toString(), HttpMethod.POST, httpEntity, responseType).getBody();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ApiResult.failed("添加角色失败");
    }


    private ApiResult<Boolean> addRoleMember(Long tenantId, ApiRoleMemberAddRequest request) {
        try {
            //表单提交数据
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add(Constant.LOGIN_TOKEN, JwtUserInfoUtils.getAuthorization());
            headers.add(Constant.CLIENT_ID, tenantId.toString());
            headers.add(Constant.APP_CODE_STR, Constant.APPCODE_HEADER);
            HttpEntity httpEntity = new HttpEntity(request, headers);
            String pathUrl = baseUrl + addMember;
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(pathUrl);
            ParameterizedTypeReference<ApiResult<Boolean>> responseType = new ParameterizedTypeReference<ApiResult<Boolean>>() {
            };
            return restHttpTemplate.exchange(builder.build().toString(), HttpMethod.POST, httpEntity, responseType).getBody();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }


        return ApiResult.failed("添加用户与角色的关联失败");
    }

    private ApiResult<List<ApiRoleMemberResponse>> listRoleMember(Long tenantId, Long roleId) {
        try {
            //表单提交数据
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add(Constant.LOGIN_TOKEN, JwtUserInfoUtils.getAuthorization());
            headers.add(Constant.CLIENT_ID, tenantId.toString());
            headers.add(Constant.APP_CODE_STR, Constant.APPCODE_HEADER);
            HttpEntity httpEntity = new HttpEntity(headers);
            String pathUrl = baseUrl + roleMemberList + "?roleId=" + roleId;
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(pathUrl);
            ParameterizedTypeReference<ApiResult<List<ApiRoleMemberResponse>>> responseType = new ParameterizedTypeReference<ApiResult<List<ApiRoleMemberResponse>>>() {
            };
            return restHttpTemplate.exchange(builder.build().toString(), HttpMethod.GET, httpEntity, responseType).getBody();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ApiResult.failed("查询角色下的用户列表失败");
    }

    private void initRedirect(Long tenantId) {
        log.info("开始初始化租户:{}的回调数据", tenantId);

        // 设置租户隔离
        TenantIsolation tenantIsolation = new TenantIsolation();
        tenantIsolation.setTenantId(tenantId);

        // 初始化4个回调数据
        initDevicePropertyChangeRedirect(tenantIsolation);
        initDeviceStateChangeRedirect(tenantIsolation);
        initDeviceFaultNotifyRedirect(tenantIsolation);
        initDeviceErrorNotifyRedirect(tenantIsolation);

        log.info("租户:{}的回调数据初始化完成", tenantId);
    }

    /**
     * 初始化设备属性变化回调
     */
    private void initDevicePropertyChangeRedirect(TenantIsolation tenantIsolation) {
        InstanceRedirectEntity entity = InstanceRedirectEntity.builder()
                .tenantId(tenantIsolation.getTenantId())
                .redirectName("设备云管-设备属性变化")
                .redirectType(1)  // MQTT类型
                .redirectRequestTimeout(10000)
                .redirectRequestConnectTimeout(10000)
                .redirectInvokeTime(null)
                .redirectFn("{\"ip\":\"" + mqttHost + "\",\"port\":" + mqttPort + ",\"username\":\"" + mqttUsername + "\",\"password\":\"" + mqttPassword + "\",\"keepAlive\":60000,\"messageResendInterval\":0,\"topic\":\"dcm/operating/" + tenantIsolation.getTenantId() + "/${deviceType}/data\",\"qos\":0,\"payload\":\"\",\"clientId\":\"dcm-operating-" + tenantIsolation.getTenantId() + "\",\"reconnectGapTime\":30}")
                .description("设备云管-设备属性变化，用于设备云管心电图功能中，属性变化实时推送给设备云管进行展示设备的实时属性变化")
                .build();
        
        redirectService.save(tenantIsolation, entity);
    }

    /**
     * 初始化设备状态变化通知回调
     */
    private void initDeviceStateChangeRedirect(TenantIsolation tenantIsolation) {
        InstanceRedirectEntity entity = InstanceRedirectEntity.builder()
                .tenantId(tenantIsolation.getTenantId())
                .redirectName("设备云管-设备状态变化通知")
                .redirectType(1)  // MQTT类型
                .redirectRequestTimeout(10000)
                .redirectRequestConnectTimeout(10000)
                .redirectInvokeTime(null)
                .redirectFn("{\"ip\":\""+ mqttHost +"\",\"port\":"+ mqttPort +",\"username\":\""+ mqttUsername +"\",\"password\":\""+ mqttPassword +"\",\"keepAlive\":60000,\"messageResendInterval\":0,\"topic\":\"dcm/state/" + tenantIsolation.getTenantId() + "/${deviceType}/data\",\"qos\":0,\"payload\":\"\",\"clientId\":\"dcm-state-change-" + tenantIsolation.getTenantId() + "\",\"reconnectGapTime\":30}")
                .description("设备云管-设备状态变化通知，用于设备心电图，显示设备的状态（空闲、任务、故障、离线）")
                .build();
        
        redirectService.save(tenantIsolation, entity);
    }

    /**
     * 初始化设备预警通知回调
     */
    private void initDeviceFaultNotifyRedirect(TenantIsolation tenantIsolation) {
        InstanceRedirectEntity entity = InstanceRedirectEntity.builder()
                .tenantId(tenantIsolation.getTenantId())
                .redirectName("设备云管-设备预警通知")
                .redirectType(1)  // MQTT类型
                .redirectRequestTimeout(10000)
                .redirectRequestConnectTimeout(10000)
                .redirectInvokeTime(null)
                .redirectFn("{\"ip\":\""+ mqttHost +"\",\"port\":"+ mqttPort +",\"username\":\""+ mqttUsername +"\",\"password\":\""+ mqttPassword +"\",\"keepAlive\":60000,\"messageResendInterval\":0,\"topic\":\"dcm/fault/" + tenantIsolation.getTenantId() + "/${deviceType}/data\",\"qos\":0,\"payload\":\"\",\"clientId\":\"dcm-fault-notify-" + tenantIsolation.getTenantId() + "\",\"reconnectGapTime\":30}")
                .description("设备云管-设备预警通知，用于设备预警事件触发时，通知设备云管，在设备告警中创建一条告警记录，告警区分登记（一般，警告，严重，致命）")
                .build();
        
        redirectService.save(tenantIsolation, entity);
    }

    /**
     * 初始化设备故障通知回调
     */
    private void initDeviceErrorNotifyRedirect(TenantIsolation tenantIsolation) {
        InstanceRedirectEntity entity = InstanceRedirectEntity.builder()
                .tenantId(tenantIsolation.getTenantId())
                .redirectName("设备云管-设备故障通知")
                .redirectType(1)  // MQTT类型
                .redirectRequestTimeout(10000)
                .redirectRequestConnectTimeout(10000)
                .redirectInvokeTime(null)
                .redirectFn("{\"ip\":\""+ mqttHost +"\",\"port\":"+ mqttPort +",\"username\":\""+ mqttUsername +"\",\"password\":\""+ mqttPassword +"\",\"keepAlive\":60000,\"messageResendInterval\":0,\"topic\":\"dcm/error/" + tenantIsolation.getTenantId() + "/${deviceType}/data\",\"qos\":0,\"payload\":\"\",\"clientId\":\"dcm-error-notify-" + tenantIsolation.getTenantId() + "\",\"reconnectGapTime\":30}")
                .description("设备云管-设备故障通知，用于在设备故障事件触发时，通知云管在设备故障中增加一套故障信息，不区分等级")
                .build();
        
        redirectService.save(tenantIsolation, entity);
    }

}
