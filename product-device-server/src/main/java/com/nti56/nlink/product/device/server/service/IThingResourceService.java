package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.product.device.server.scriptApi.Device;

import java.util.List;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName IThingService
 * @date 2022/4/21 14:46
 * @Version 1.0
 */
public interface IThingResourceService {


    List<Device> getTenantDevices(Long tenantId);

    List<Long> getByTag(Long tenantId,String key,String value);

    List<Long> getByTagId(Long tenantId,String tagId);
}
