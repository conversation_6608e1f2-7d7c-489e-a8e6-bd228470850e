package com.nti56.nlink.product.device.server.service.impl.strategy;

import com.google.common.cache.*;
import org.eclipse.paho.mqttv5.client.MqttClient;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.mqttv5.common.MqttException;

import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 类说明：
 *
 * @ClassName MQTTClientPool
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/9/6 14:32
 * @Version 1.0
 */

@Slf4j
public class MQTTClientPool {

    private static final Lock lock = new ReentrantLock();


    private static final ConcurrentHashMap<Long,Long> latestConnectFailedTimeMap = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<Long,Long> recentConnectTimeMap = new ConcurrentHashMap<>();

    private static final ConcurrentHashMap<Long, MqttClient> clientMap = new ConcurrentHashMap<>();

    public static MqttClient getClient(Long redirectId) {

        lock.lock();
        try {
            return clientMap.get(redirectId);
        } finally {
            lock.unlock();
        }
    }

    public static void register(Long redirectId, MqttClient mqttClient) {
        lock.lock();
        try {
            clientMap.put(redirectId, mqttClient);
            log.info("注册mqtt客户端成功,redirectId:{},clientMap size:{}",redirectId,clientMap.size());
        } finally {
            lock.unlock();
        }
    }

    public static void removeClient(Long redirectId){
        lock.lock();
        try {
            MqttClient client = clientMap.remove(redirectId);
            if(!Objects.isNull(client)){
                if(client.isConnected()){
                    client.disconnect();
                }
            }
        } catch (MqttException e) {
            log.error(e.getMessage(), e);
        } finally {
            lock.unlock();
        }

    }
    
    public static void putRecentConnectTime(Long redirectId){
        if(!Objects.isNull(redirectId)){
            recentConnectTimeMap.put(redirectId,System.currentTimeMillis());
        }
    }

    public static boolean cannotConnect(String ip, Integer port,Long redirectId){
        Long recentConnectTime = recentConnectTimeMap.get(redirectId);
        if(recentConnectTime != null){
            long restMs = recentConnectTime + 5000L - System.currentTimeMillis();
            if(restMs>0){
                //最近一段时间内，有连接
                log.info("不能重连,ip:{} port:{}, 继续等待{}ms",ip,port,restMs);
                return true;
            }
        }
        return false;
    }

    public static void putRedirectConnectFailedTime(Long redirectId){
        if(!Objects.isNull(redirectId)){
            latestConnectFailedTimeMap.put(redirectId,System.currentTimeMillis());
        }
    }
    public static boolean recentConnectFail(Long redirectId,Long timeGap){
        if(timeGap < 60000L){
            timeGap = 60000L;
        }
        Long latestConnectFailTime = latestConnectFailedTimeMap.get(redirectId);
//        log.info("===latestTime:{},currentTime:{},timeGap:{},redirectId:{}====",latestConnectFailTime,System.currentTimeMillis(),timeGap,redirectId);
        if(latestConnectFailTime != null){
            if(System.currentTimeMillis() < (latestConnectFailTime + timeGap)){
                //最近一段时间内，有连接失败
//                log.info("========fail wait=======");
                return true;
            }
        }
        return false;
    }
}
