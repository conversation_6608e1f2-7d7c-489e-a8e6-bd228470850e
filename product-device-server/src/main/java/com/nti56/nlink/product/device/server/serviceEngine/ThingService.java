package com.nti56.nlink.product.device.server.serviceEngine;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.scriptApi.SpiUtil;
import lombok.extern.slf4j.Slf4j;

import javax.script.Invocable;
import javax.script.ScriptException;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName ThingService
 * @date 2022/4/19 14:23
 * @Version 1.0
 */
@Slf4j
public class ThingService extends BaseService implements SyncServiceTask, AsyncServiceTask {

    @Override
    public Result<Object> doTask() {
        DeviceServiceLogEntity logEntity = this.getLogEntity();
        String functionName = genFunctionName();
        String code = initServiceCode(functionName);
        Object result = null;
        try {
            //加载服务代码
            engine.engine.eval(code);
            Invocable invocable = (Invocable) engine.engine;
            result = invocable.invokeFunction(functionName,this.thing,this.engineering,this.input);
        } catch (ScriptException e) {
            log.error("物服务代码错误,方法名{}，输入{}",functionName, this.input!=null ? JSONObject.toJSONString(this.input):"");
            e.printStackTrace();
            if(!ObjectUtil.isEmpty(logEntity.getIsRecordLog()) && !logEntity.getIsRecordLog()){
                log.debug("ThingService doTask ScriptException and message is {}", e.getMessage());
            }else {
                SpiUtil.dealLog(getDeviceDataResource(),logEntity, e.getMessage(),false);
            }
            return Result.error(ServiceCodeEnum.THING_SERVICE_CODE_ERROR,e.getMessage());
        } catch (NoSuchMethodException e) {
            log.error("物服务代码执行异常,方法名{}，输入{}",functionName, this.input!=null ? JSONObject.toJSONString(this.input):"");
            if(!ObjectUtil.isEmpty(logEntity.getIsRecordLog()) && !logEntity.getIsRecordLog()){
                log.debug("ThingService doTask NoSuchMethodException and message is {}", e.getMessage());
            }else {
                SpiUtil.dealLog(getDeviceDataResource(),logEntity, e.getMessage(),false);
            }
            e.printStackTrace();
            return Result.error(ServiceCodeEnum.THING_SERVICE_INVOKE_JS_CODE_FAIL,e.getMessage());
        } catch (Exception e) {
            log.error("服务调用异常,方法名{}，输入{}",functionName, this.input!=null ? JSONObject.toJSONString(this.input):"");
            log.error(e.getMessage());
            if(!ObjectUtil.isEmpty(logEntity.getIsRecordLog()) && !logEntity.getIsRecordLog()){
                log.debug("ThingService doTask Exception and message is {}", e.getMessage());
            }else {
                SpiUtil.dealLog(getDeviceDataResource(), logEntity, e.getMessage(), false);
            }
            return Result.error("服务调用异常：请检查物服务代码与输入参数是否正确");
        }
        if(!ObjectUtil.isEmpty(logEntity.getIsRecordLog()) && !logEntity.getIsRecordLog()){
            log.debug("ThingService doTask dealLog and message is {}", JSONUtil.toJsonStr(logEntity));
        }else {
            SpiUtil.dealLog(getDeviceDataResource(), logEntity, ObjectUtil.toString(result), true);
        }
        return Result.ok(result);
    }

    @Override
    public String initServiceCode(String functionName){
        String serviceCode = this.serviceDefined.getServiceCode();
        StringBuilder sb = new StringBuilder();
        sb.append("function ");
        sb.append(functionName);
        sb.append("(me,things,input){");
        sb.append(serviceCode);
        sb.append("}");
        return sb.toString();
    }

    private String genFunctionName(){
        String serviceName = this.getServiceName();
        StringBuilder sb = new StringBuilder();
        sb.append(serviceName);
        sb.append("_");
        sb.append(this.getDeviceId());
        return sb.toString();
    }

}
