package com.nti56.nlink.product.device.server.model;

import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import com.nti56.nlink.product.device.server.entity.ThingModelEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema
public class ThingModelVo extends ThingModelEntity {
    @Schema(description = "标记实体")
    private List<TagRsp> tags;
    @Schema(description = "是否系统内置")
    private boolean defaultFlag = false;
}
