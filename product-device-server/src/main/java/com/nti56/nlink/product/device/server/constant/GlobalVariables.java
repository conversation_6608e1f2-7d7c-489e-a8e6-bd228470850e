package com.nti56.nlink.product.device.server.constant;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.nti56.nlink.product.device.server.entity.ThingModelEntity;
import com.nti56.nlink.product.device.server.entity.ThingServiceEntity;
import com.nti56.nlink.product.device.server.service.IThingModelService;
import com.nti56.nlink.product.device.server.service.IThingServiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Component
public class GlobalVariables {

    @Autowired
    private IThingModelService thingModelService;

    @Autowired
    private IThingServiceService thingServiceService;

    private static ThingModelEntity DEFAULT_COMMON_TYPE = null;

    private static List<ThingServiceEntity> DEFAULT_COMMON_TYPE_SERVICE = null;
    @PostConstruct
    public void loadCommonType() {
        List<ThingModelEntity> commonType = thingModelService.list(Wrappers.<ThingModelEntity>lambdaQuery().eq(ThingModelEntity::getTenantId, Constant.DEFAULT_THING).eq(ThingModelEntity::getName, "Common_Type"));
        if (CollUtil.isNotEmpty(commonType)) {
            DEFAULT_COMMON_TYPE = commonType.get(0);
        }
    }
    @PostConstruct
    public void loadCommonTypeService() {
        List<ThingServiceEntity> thingServiceEntityList = thingServiceService.list( new LambdaQueryWrapper<ThingServiceEntity>()
                .eq(ThingServiceEntity::getTenantId, Constant.DEFAULT_THING));
        if (CollUtil.isNotEmpty(thingServiceEntityList)) {
            DEFAULT_COMMON_TYPE_SERVICE = thingServiceEntityList;
        }
    }


    public static ThingModelEntity getDefaultCommonType() {
        return DEFAULT_COMMON_TYPE;
    }
    public static List<ThingServiceEntity> getDefaultCommonTypeService() {
        return DEFAULT_COMMON_TYPE_SERVICE!=null?DEFAULT_COMMON_TYPE_SERVICE:new ArrayList<>();
    }

}