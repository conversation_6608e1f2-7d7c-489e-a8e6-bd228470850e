package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.EventDpo;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.SubscriptionDpo;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.EventElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyElm;
import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import com.nti56.nlink.product.device.server.constant.Constant;
import com.nti56.nlink.product.device.server.domain.thing.devicemodel.DeviceModel;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelDpo;
import com.nti56.nlink.product.device.server.domain.thing.dpo.PropertyDpo;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ServiceDpo;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ModelTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ResourceTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Property;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Subscription;
import com.nti56.nlink.product.device.server.domain.thing.thingmodel.ThingModel;
import com.nti56.nlink.product.device.server.domain.thing.thingmodel.ThingModelInherit;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.*;
import com.nti56.nlink.product.device.server.model.*;
import com.nti56.nlink.product.device.server.model.edgegateway.dto.ExcelMessageDTO;
import com.nti56.nlink.product.device.server.model.inherit.ThingModelOfInherit;
import com.nti56.nlink.product.device.server.model.redirect.RedirectReferenceDto;
import com.nti56.nlink.product.device.server.model.thingModel.dto.AppendThingModelByLabelIdsDTO;
import com.nti56.nlink.product.device.server.model.thingModel.dto.CreateThingModelByLabelGroupDTO;
import com.nti56.nlink.product.device.server.model.thingModel.dto.CreateThingModelByLabelIdsDTO;
import com.nti56.nlink.product.device.server.model.thingModel.vo.*;
import com.nti56.nlink.product.device.server.service.*;
import com.nti56.nlink.product.device.server.util.CacheUtils;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.dozer.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 类说明: 物模型服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:10:52
 * @since JDK 1.8
 */
@Service
public class ThingModelServiceImpl extends BaseServiceImpl<ThingModelMapper, ThingModelEntity> implements IThingModelService {

    @Autowired
    ThingModelMapper thingModelMapper;

    @Autowired
    IThingServiceService thingServiceService;

    @Autowired
    IThingModelInheritService thingModelInheritService;

    @Autowired
    DataModelMapper dataModelMapper;

    @Autowired
    DataModelPropertyMapper dataModelPropertyMapper;

    @Autowired
    ThingModelInheritMapper thingModelInheritMapper;

    @Autowired
    ITagBindRelationService tagBindRelationService;

    @Autowired
    ThingServiceMapper thingServiceMapper;

    @Autowired
    private ILabelService labelService;

    @Autowired
    TagBindRelationMapper tagBindRelationMapper;

    @Autowired
    private IDeviceModelInheritService deviceModelInheritEntities;

    @Autowired
    CommonFetcherFactory commonFetcherFactory;

    @Autowired
    @Lazy
    IDeviceService deviceService;

    @Autowired
    private Mapper dozerMapper;

    @Autowired
    private ISubscriptionService subscriptionService;

    @Value("${fault.commonModel}")
    private String baseFaultModelJSONString;

    @Value("${device.base.model}")
    private String deviceBaseModelStr;

    //@Autowired
    //private IModelGraphService modelGraphService;

    @Autowired
    private IInstanceRedirectService instanceRedirectService;

    @Override
    @Transactional
    public Result<ThingModelEntity> createThingModel(TenantIsolation tenantIsolation, ThingModelInfoDto dto) {

        String name = dto.getName();
        List<Long> inheritThingModelIds = dto.getInheritThingModelIds();

        if (StringUtils.isAllBlank(name)) {
            return Result.error("物模型名称不能为空");
        }

        Result<Void> uniqueNameResult = this.uniqueName(dto.getName(), tenantIsolation);
        if (!uniqueNameResult.getSignal()) {
            throw new BizException(uniqueNameResult.getMessage());
        }

        if (inheritThingModelIds != null && inheritThingModelIds.size() > 0) {
            for (Long id : inheritThingModelIds) {
                ThingModelEntity thingModel = thingModelMapper.getById(tenantIsolation.getTenantId(), id);
                if (thingModel == null && !CacheUtils.SYS_COMMON_MODEL.containsKey(id)) {
                    return Result.error("查不到物模型，thingModelId:" + id);
                }
            }

        }

        ThingModelEntity thingModelEntity = ThingModelEntity.builder()
                .tenantId(tenantIsolation.getTenantId())
                .name(name).descript(dto.getDescript()).modelType(dto.getModelType())
                .build();
        thingModelMapper.insert(thingModelEntity);

        //插入模型并检查模型正确性
        if (inheritThingModelIds != null && inheritThingModelIds.size() > 0) {
            int i = 1;
            for (Long id : inheritThingModelIds) {
                ThingModelInheritEntity entity = new ThingModelInheritEntity();
                //判断是否继承了公共模型，如果继承了公共模型，需要转换成租户下自己的模型
                if (CacheUtils.SYS_COMMON_MODEL.containsKey(id)) {
                    //公共模型模板
                    ThingModelSimpleBo thingModelSimpleBo = CacheUtils.SYS_COMMON_MODEL.get(id);
                    //检查租户是不是拥有该模型了
                    Result<Void> result = this.uniqueName(thingModelSimpleBo.getName(), tenantIsolation);
                    if (result.getSignal()) {
                        //不存在，拷贝一份公共模型到租户下
                        ThingModelEntity commonModel = ThingModelEntity.builder()
                                .tenantId(tenantIsolation.getTenantId())
                                .name(thingModelSimpleBo.getName()).modelType(thingModelSimpleBo.getModelType())
                                .build();
                        thingModelMapper.insert(commonModel);
                        entity.setInheritThingModelId(commonModel.getId());
                        //modelGraphService.saveModelNode(commonModel, null);
                        //判断公共模型是否带有公共服务，有服务就创建一份到租户下
                        Map<Long, ThingServiceEntity> modelServices = CacheUtils.SYS_COMMON_THING_SERVICE.entrySet().stream()
                                .filter(ks -> id.equals(ks.getValue().getThingModelId()))
                                .collect(Collectors.toMap(p -> p.getKey(), p -> p.getValue()));
                        if (MapUtil.isNotEmpty(modelServices)) {
                            modelServices.forEach((k, v) -> {
                                ThingServiceEntity thingServiceEntity = new ThingServiceEntity();
                                BeanUtil.copyProperties(v, thingServiceEntity);
                                thingServiceEntity.setId(null);
                                thingServiceEntity.setThingModelId(commonModel.getId());
                                thingServiceEntity.setTenantId(tenantIsolation.getTenantId());
                                thingServiceEntity.setModuleId(tenantIsolation.getModuleId());
                                thingServiceEntity.setSpaceId(tenantIsolation.getSpaceId());
                                thingServiceEntity.setEngineeringId(tenantIsolation.getSpaceId());
                                thingServiceMapper.insert(thingServiceEntity);
                            });
                        }
                    } else {
                        //查找已有公共模型备份
                        QueryWrapper<ThingModelEntity> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("name", thingModelSimpleBo.getName());
                        queryWrapper.eq("tenant_id", tenantIsolation.getTenantId());
                        ThingModelEntity one = this.getOne(queryWrapper);
                        entity.setInheritThingModelId(one.getId());
                    }
                } else {
                    entity.setInheritThingModelId(id);
                }
                entity.setThingModelId(thingModelEntity.getId());
                entity.setTenantId(tenantIsolation.getTenantId());
                entity.setSortNo(i++);
                thingModelInheritMapper.insert(entity);
            }
            //有继承模型，需要检查模型正确性
            CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());
            Result<ThingModel> thingModelResult = ThingModel.checkInfo(
                    thingModelEntity,
                    commonFetcher
            );

            if (!thingModelResult.getSignal()) {
                throw new BizException(thingModelResult.getMessage());
            }
        }

        Result<Void> saveList = tagBindRelationService.saveList(
                tenantIsolation.getTenantId(), thingModelEntity.getId(), dto.getTagIds(), ResourceTypeEnum.THING_MODEL
        );
        if (!saveList.getSignal()) {
            throw new BizException(saveList.getMessage());
        }
        //模型关系图
        /*Result<Void> saveResult = modelGraphService.saveModelNode(thingModelEntity, inheritThingModelIds);
        if (!saveResult.getSignal()) {
            throw new BizException(saveResult.getMessage());
        }*/
        return Result.ok(thingModelEntity);
    }

    @Override
    public Result<Boolean> deleteThingModel(TenantIsolation tenantIsolation, Long thingModelId) {
        ThingModelEntity thingModel = thingModelMapper.getById(tenantIsolation.getTenantId(), thingModelId);
        if (thingModel == null) {
            return Result.error("不存在");
        }

        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());
        Result<String> deleteResult = ThingModel.checkDelete(thingModelId, commonFetcher);
        if (!deleteResult.getSignal()) {
            return Result.error(deleteResult.getMessage());
        }

        Integer count = thingModelMapper.deleteById(tenantIsolation.getTenantId(), thingModelId);
        if (count == null || count <= 0) {
            return Result.error("删除失败");
        }

        thingModelInheritMapper.deleteByThingModelId(tenantIsolation.getTenantId(), thingModelId);

        thingServiceMapper.deleteByThingModelId(tenantIsolation.getTenantId(), thingModelId);

        tagBindRelationService.deleteByTargetId(tenantIsolation.getTenantId(), ResourceTypeEnum.THING_MODEL, thingModelId);

        //删除模型图节点
        //modelGraphService.deleteModelNode(thingModelId);

        QueryWrapper<SubscriptionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("directly_model_id", thingModelId);
        List<SubscriptionEntity> subscriptionEntities = subscriptionService.list(queryWrapper);
        RedirectReferenceDto delRef = RedirectReferenceDto.builder()
                .updateType(0)
                .build();
        if (CollectionUtil.isNotEmpty(subscriptionEntities)) {
            for (SubscriptionEntity subscriptionEntity : subscriptionEntities) {
                delRef.setRefId(subscriptionEntity.getDirectlyModelId());
                delRef.setRedirectId(subscriptionEntity.getCallbackId());
                instanceRedirectService.updateReference(tenantIsolation, delRef);
            }
        }

        subscriptionService.batchDeleteByDirectlyModelIds(tenantIsolation.getTenantId(), Collections.singletonList(thingModelId), ModelTypeEnum.THING_MODEL.getValue());

        return Result.ok(true, "删除成功");
    }

    @Override
    @Transactional
    public Result<Boolean> editThingModelModel(TenantIsolation tenantIsolation, ThingModelModelDto thingModelModel) {
        ThingModelEntity entity = thingModelMapper.getById(tenantIsolation.getTenantId(), thingModelModel.getId());
        if (entity == null) {
            return Result.error("不存在");
        }

        //模型是否修改
        Boolean modelChanged = false;
        {
            ModelField oldModel = entity.getModel();
            ModelField newModel = thingModelModel.getModel();
            modelChanged = ThingModelInherit.checkModelDiff(oldModel, newModel);
        }

        entity.setModel(thingModelModel.getModel());
        entity.setTenantId(tenantIsolation.getTenantId());
        Integer count = thingModelMapper.updateById(entity);

        List<String> baseNames = DeviceModel.getBaseModel().getProperties().stream().map(Property::getName).collect(Collectors.toList());
        List<PropertyElm> properties = thingModelModel.getModel().getProperties();
        for(PropertyElm propertyElm :properties){
            if(baseNames.contains(propertyElm.getName())){
                throw new BizException("属性保留字冲突");
            }
        }

        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());
        Result<ThingModel> thingModelResult = ThingModel.checkInfo(
                entity,
                commonFetcher
        );

        if (!thingModelResult.getSignal()) {
            throw new BizException(thingModelResult.getMessage());
        }

        if (modelChanged) {
            //模型有修改，先检查被继承模型是否冲突，冲突则不允许修改
            ThingModel thingModel = thingModelResult.getResult();
            Result<List<Long>> beInheritResult = thingModel.checkBeInherit(commonFetcher);
            if (!beInheritResult.getSignal()) {
                throw new BizException(beInheritResult.getMessage());
            }
            //再查找会影响到哪些设备
            deviceService.notifyDeviceSyncByThingModelId(thingModel.getId(), tenantIsolation.getTenantId(), commonFetcher);
        }

        return Result.ok();
    }

    @Override
    @Transactional
    public Result<Boolean> editThingModelInfo(TenantIsolation tenantIsolation, ThingModelInfoDto thingModelInfo) {
        ThingModelEntity entity = thingModelMapper.getById(tenantIsolation.getTenantId(), thingModelInfo.getId());
        if (entity == null) {
            return Result.error("不存在");
        }


        List<Long> inheritThingModelIds = thingModelInfo.getInheritThingModelIds();
        if (inheritThingModelIds != null && inheritThingModelIds.size() > 0) {
            for (Long id : inheritThingModelIds) {
                ThingModelEntity e = thingModelMapper.getById(tenantIsolation.getTenantId(), id);
                if (e == null) {
                    return Result.error("查不到物模型，thingModelId:" + id);
                }
            }
        }

        //模型是否修改
        Boolean modelChanged = false;
        List<ThingModelInheritEntity> oldInheritThingModelList = thingModelInheritMapper.listByThingModelId(tenantIsolation.getTenantId(), thingModelInfo.getId());
        Set<Long> oldInheritThingModelIds = oldInheritThingModelList.stream().map(ThingModelInheritEntity::getInheritThingModelId).collect(Collectors.toSet());
        Set<Long> newSet = inheritThingModelIds.stream().collect(Collectors.toSet());
        modelChanged = ThingModelInherit.checkInheritDiff(oldInheritThingModelIds, newSet);


        entity.setName(thingModelInfo.getName());
        entity.setDescript(thingModelInfo.getDescript());
        entity.setTenantId(tenantIsolation.getTenantId());
        Integer count = thingModelMapper.updateById(entity);

        Integer deleteCount = thingModelInheritMapper.deleteByThingModelId(tenantIsolation.getTenantId(), thingModelInfo.getId());

        if (inheritThingModelIds != null && inheritThingModelIds.size() > 0) {
            int i = 1;
            for (Long id : inheritThingModelIds) {
                ThingModelInheritEntity e = new ThingModelInheritEntity();
                e.setTenantId(tenantIsolation.getTenantId());
                e.setThingModelId(thingModelInfo.getId());
                e.setInheritThingModelId(id);
                e.setSortNo(i++);
                thingModelInheritMapper.insert(e);
            }
        }

        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());
        Result<ThingModel> thingModelResult = ThingModel.checkInfo(
                entity,
                commonFetcher
        );

        if (!thingModelResult.getSignal()) {
            throw new BizException(thingModelResult.getMessage());
        }

        if (modelChanged) {
            //模型有修改，先检查被继承模型是否冲突，冲突则不允许修改
            ThingModel thingModel = thingModelResult.getResult();
            Result<List<Long>> beInheritResult = thingModel.checkBeInherit(commonFetcher);
            if (!beInheritResult.getSignal()) {
                throw new BizException(beInheritResult);
            }
            //再查找会影响到哪些设备
            deviceService.notifyDeviceSyncByThingModelId(thingModel.getId(), tenantIsolation.getTenantId(), commonFetcher);
        }


        tagBindRelationService.deleteByTargetId(tenantIsolation.getTenantId(), ResourceTypeEnum.THING_MODEL, thingModelInfo.getId());
        Result<Void> saveList = tagBindRelationService.saveList(
                tenantIsolation.getTenantId(), thingModelInfo.getId(), thingModelInfo.getTagIds(), ResourceTypeEnum.THING_MODEL
        );
        if (!saveList.getSignal()) {
            throw new BizException(saveList.getMessage());
        }
        //物模型修改
        /*Result<Void> updateResult = modelGraphService.updateModelNode(entity, inheritThingModelIds);
        if (!updateResult.getSignal()) {
            throw new BizException(updateResult.getMessage());
        }*/

        return Result.ok();
    }

    @Override
    public Result<Page<ThingModelVo>> listThingModel(Page<ThingModelVo> page, TenantIsolation tenantIsolation, String searchStr, List<Long> tagIds) {
        Result<List<TagBindRelationEntity>> listResult = Result.error();
        if (Optional.ofNullable(tagIds).isPresent() && tagIds.size() > 0) {
            listResult = tagBindRelationService.listByTagIds(tenantIsolation.getTenantId(), BeanUtilsIntensifier.getIds2String(tagIds, null), ResourceTypeEnum.THING_MODEL);
        }
        QueryWrapper<ThingModelVo> wrapper = new QueryWrapper<>();
        wrapper.like(searchStr != null && !"".equals(searchStr), "name", searchStr);
        wrapper.in(listResult.getSignal() && listResult.getResult().size() > 0, "id", BeanUtilsIntensifier.getIds(listResult.getResult(), TagBindRelationEntity::getTargetId));
        wrapper.and(w-> w.eq("tenant_id", tenantIsolation.getTenantId())
                .or()
                .eq("tenant_id", Constant.DEFAULT_THING));
        wrapper.ne("model_type", ModelTypeEnum.COMMON_MODEL.getValue());
        Page<ThingModelVo> list = thingModelMapper.listThingModel(page, wrapper);
        if (Objects.isNull(list.getRecords()) || list.getRecords().isEmpty()) {
            return Result.ok(list);
        }
        list.getRecords().forEach(i->{
            if (ObjectUtil.equals(i.getTenantId(),Constant.DEFAULT_THING)){
                i.setDefaultFlag(true);
            }
        });
        Map<Long, List<TagRsp>> tagMap = tagBindRelationService.getTagListGroupByTargetId(tenantIsolation.getTenantId(), ResourceTypeEnum.THING_MODEL, BeanUtilsIntensifier.getIds(list.getRecords(), ThingModelVo::getId));
        Optional.ofNullable(list.getRecords()).orElse(new ArrayList<>()).forEach(modelVo -> {
            modelVo.setTags(tagMap.get(modelVo.getId()));
        });
        return Result.ok(list);
    }

    @Override
    public Result<ThingModelDto> getThingModelById(TenantIsolation tenantIsolation, Long thingModelId) {
        ThingModelEntity thingModelEntity = thingModelMapper.getById(tenantIsolation.getTenantId(), thingModelId);

        if (thingModelEntity == null) {
            return Result.error("找不到物模型");
        }

        ThingModelDto dto = BeanUtilsIntensifier.copyBean(thingModelEntity, ThingModelDto.class);
        if (ObjectUtil.equals(thingModelEntity.getTenantId(),Constant.DEFAULT_THING)){
            dto.setDefaultFlag(true);
        }
        ModelFieldDto model = new ModelFieldDto();
        dto.setModel(model);
        if (thingModelEntity.getModel() != null) {
            model.setProperties(thingModelEntity.getModel().getProperties());
            model.setEvents(thingModelEntity.getModel().getEvents());
        }

        List<Long> inheritList = thingModelInheritMapper.listInheritIdByThingModelId(tenantIsolation.getTenantId(), thingModelId);
        dto.setInheritThingModelIds(inheritList);

        List<ThingServiceEntity> services = thingServiceMapper.listByThingModelId(tenantIsolation.getTenantId(), thingModelId);
        model.setServices(services);

        SubscriptionEntity subscriptionEntity = new SubscriptionEntity();
        subscriptionEntity.setTenantId(tenantIsolation.getTenantId());
        subscriptionEntity.setDirectlyModelId(thingModelId);
        subscriptionEntity.setModelType(ModelTypeEnum.THING_MODEL.getValue());

        model.setSubscriptions(subscriptionService.list(subscriptionEntity).getResult());

        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());
        Result<ThingModel> fullModelResult = ThingModel.checkInfo(
                thingModelEntity,
                commonFetcher
        );
        ThingModel fullModel = fullModelResult.getResult();
        dto.setFullModel(fullModel.toDpo());
        List<ModelDpo> modelContainsInherits = new ArrayList<>();
        List<ThingModel> thingModelList = fullModel.getInherit().getThingModelList();
        if (thingModelList != null && thingModelList.size() > 0) {
            for (ThingModel thingModel : thingModelList) {
                ModelDpo dpo = thingModel.toDpo();
                modelContainsInherits.add(dpo);
            }
        }
        boolean containFaultModel = checkContainFaultModel(thingModelList);
        if (thingModelEntity.getModelType().equals(ModelTypeEnum.FAULT_MODEL.getValue())) {
            containFaultModel = true;
        }
        dto.setModelContainsInherits(modelContainsInherits);
        if (containFaultModel) {
            //存在故障模型，赋值基础故障模型,拿配置
            ModelDpo baseFaultModel = JSONUtil.toBean(baseFaultModelJSONString, ModelDpo.class);
            dto.setCommonFaultModel(baseFaultModel);
        }
        ModelDpo baseModel = JSONUtil.toBean(deviceBaseModelStr, ModelDpo.class);
        dto.setDeviceBaseModel(baseModel);
        dto.getFullModel().merge(baseModel);
        dto.setTags(tagBindRelationService.getTags(tenantIsolation.getTenantId(), ResourceTypeEnum.THING_MODEL, thingModelId));
        return Result.ok(dto);
    }

    private boolean checkContainFaultModel(List<ThingModel> thingModelList) {
        if (Objects.isNull(thingModelList)) return false;
        return JSONUtil.toJsonStr(thingModelList).contains("\"modelType\":3");

        //退出条件
        /*if(CollectionUtils.isEmpty(thingModelList)){
            return false;
        }
        boolean flag = false;
        for (ThingModel thingModel : thingModelList) {
            if(thingModel.getModelType().equals(ModelTypeEnum.FAULT_MODEL.getValue())){
                flag = true;
                break;
            }
            if(!CollectionUtils.isEmpty(thingModel.getInherit().getThingModelList())){
                return flag || checkContainFaultModel(thingModel.getInherit().getThingModelList());
            }

        }
        return flag;*/
    }

    @Override
    public Result<List<ThingModelSimpleBo>> listInheritAvailable(TenantIsolation tenantIsolation, Long thingModelId) {
        if (thingModelId == null) {
            List<ThingModelSimpleBo> list = thingModelMapper.listAllThingModelSimpleBo(tenantIsolation.getTenantId());
            list.forEach(i->{
                if (ObjectUtil.equals(i.getId(),Constant.DEFAULT_THING)){
                    i.setDefaultFlag(true);
                }
            });
            //添加系统公共模型 todo 暂时去掉公共模型入口
//            list.addAll(CacheUtils.SYS_COMMON_MODEL.values());
            return Result.ok(list);
        }
        ThingModelEntity entity = thingModelMapper.getById(tenantIsolation.getTenantId(), thingModelId);

        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());
        Result<ThingModel> modelResult = ThingModel.checkInfo(
                entity,
                commonFetcher
        );
        if (!modelResult.getSignal()) {
            return Result.error("创建失败" + modelResult.getMessage());
        }
        ThingModel thingModel = modelResult.getResult();
        List<Long> thingModelIdList = thingModel.listAllThingModelId();
        List<ThingModelSimpleBo> list = thingModelMapper.listThingModelSimpleBoNotIn(tenantIsolation.getTenantId(), thingModelIdList);
        list.forEach(i->{
            if (ObjectUtil.equals(i.getId(),Constant.DEFAULT_THING)){
                i.setDefaultFlag(true);
            }
        });
        return Result.ok(list);
    }

    @Override
    public Result<ThingModelOfInherit> getInheritPartModel(TenantIsolation tenantIsolation, Long thingModelId) {
        ThingModelEntity entity = thingModelMapper.getById(tenantIsolation.getTenantId(), thingModelId);

        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());
        Result<ThingModel> modelResult = ThingModel.checkInfo(
                entity,
                commonFetcher
        );
        if (!modelResult.getSignal()) {
            return Result.error("创建失败" + modelResult.getMessage());
        }
        ThingModel thingModel = modelResult.getResult();
        ThingModelOfInherit model = thingModel.getInherit().getInheritPartModel();

        return Result.ok(model);
    }

    @Override
    public Result<List<ThingModelVO>> listByDeviceId(Long id) {
        List<ThingModelVO> thingModelVOS = this.deviceModelInheritEntities.listByDeviceId(id);
        List<Long> thingModelIds = thingModelVOS.stream().map(ThingModelVO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(thingModelIds)) {
            LambdaQueryWrapper<ThingModelEntity> lqw = new LambdaQueryWrapper<>();
            lqw.select(ThingModelEntity::getId, ThingModelEntity::getName)
                    .in(ThingModelEntity::getId, thingModelIds);
            List<ThingModelEntity> thingModelEntities = thingModelMapper.selectList(lqw);
            return Result.ok(BeanUtilsIntensifier.copyBeanList(thingModelEntities, ThingModelVO.class));
        }
        return Result.ok();
    }


    private Result<Void> uniqueName(String name, TenantIsolation tenantIsolation) {
        if ("Common_Type".equals(name)){
            return Result.error("名称不能为Common_Type");
        }
        return this.uniqueName(null, name, tenantIsolation);
    }

    /**
     * 判断名称是否唯一
     *
     * @param id
     * @param name
     * @param tenantIsolation
     * @return
     */
    private Result<Void> uniqueName(Long id, String name, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<ThingModelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(id != null, ThingModelEntity::getId, id)
                .eq(ThingModelEntity::getName, name)
                .eq(ThingModelEntity::getTenantId, tenantIsolation.getTenantId());

        if (thingModelMapper.selectCount(lqw) > 0) {
            return Result.error("已经存在该名称的物模型,名称：" + name);
        }

        return Result.ok();
    }

    @Override
    public Result<List<ModelDpo>> getInheritModel(TenantIsolation tenant, List<Long> thingModelIdList) {

        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenant.getTenantId());
        Result<ThingModelInherit> inheritResult = ThingModelInherit.checkInfo(
                thingModelIdList,
                commonFetcher
        );
        if (!inheritResult.getSignal()) {
            return Result.error(inheritResult.getMessage() + ", thingModelIdList:" + thingModelIdList);
        }

        ThingModelInherit inheritModel = inheritResult.getResult();
        List<ModelDpo> dpoList = inheritModel.toDpo();
        return Result.ok(dpoList);
    }

    @Override
    public Result<ModelDpo> getModel4Service(TenantIsolation tenantIsolation, Long entityId) {
        ThingModelEntity thingModelEntity = thingModelMapper.getById(tenantIsolation.getTenantId(), entityId);
        if (thingModelEntity == null) {
            return Result.error("找不到物模型");
        }
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());
        Result<ThingModel> fullModelResult = ThingModel.checkInfo(thingModelEntity, commonFetcher);
        if (fullModelResult.getSignal()) {
            ModelDpo deviceBaseModel = JSONUtil.toBean(deviceBaseModelStr, ModelDpo.class);
            return Result.ok(fullModelResult.getResult().toDpo().merge(deviceBaseModel));
        }
        return Result.error(fullModelResult.getServiceCode(), fullModelResult.getMessage());
    }

    @Override
    @Transactional
    public Result<Object> createThingModelByLabelIds(CreateThingModelByLabelIdsDTO dto, TenantIsolation tenantIsolation) {

        Result<Void> uniqueNameResult = this.uniqueName(dto.getName(), tenantIsolation);
        if (!uniqueNameResult.getSignal()) {
            throw new BizException(uniqueNameResult.getMessage());
        }

        ThingModelEntity thingModelEntity = dozerMapper.map(dto, ThingModelEntity.class);
        List<LabelEntity> labels = labelService.listByIds(dto.getLabelIds(), tenantIsolation);
        List<String> baseNames = DeviceModel.getBaseModel().getProperties().stream().map(Property::getName).collect(Collectors.toList());
        for(LabelEntity labelEntity :labels){
            if(baseNames.contains(labelEntity.getName())){
                throw new BizException("标签名与属性保留字冲突");
            }
        }
        this.createThingModelByLabels(labels, thingModelEntity, tenantIsolation);

        Result<Void> saveList = tagBindRelationService.saveList(
                tenantIsolation.getTenantId(), thingModelEntity.getId(), dto.getTagIds(), ResourceTypeEnum.THING_MODEL
        );
        if (!saveList.getSignal()) {
            throw new BizException(saveList.getMessage());
        }
        //保存模型节点
        /*Result<Void> result = modelGraphService.saveModelNode(thingModelEntity, null);
        if (!result.getSignal()) {
            throw new BizException(result.getMessage());
        }*/
        return Result.ok(thingModelEntity.getId());
    }

    @Override
    @Transactional
    public Result<Object> createThingModelByLabelGroup(CreateThingModelByLabelGroupDTO dto, TenantIsolation tenantIsolation) {
        Result<Void> uniqueNameResult = this.uniqueName(dto.getName(), tenantIsolation);
        if (!uniqueNameResult.getSignal()) {
            throw new BizException(uniqueNameResult.getMessage());
        }

        ThingModelEntity thingModelEntity = dozerMapper.map(dto, ThingModelEntity.class);
        List<LabelEntity> labels = labelService.listByLabelGroupId(dto.getLabelGroupId(), tenantIsolation);
        this.createThingModelByLabels(labels, thingModelEntity, tenantIsolation);

        Result<Void> saveList = tagBindRelationService.saveList(
                tenantIsolation.getTenantId(), thingModelEntity.getId(), dto.getTagIds(), ResourceTypeEnum.THING_MODEL
        );
        if (!saveList.getSignal()) {
            throw new BizException(saveList.getMessage());
        }
        //保存模型节点
        /*Result<Void> result = modelGraphService.saveModelNode(thingModelEntity, null);
        if (!result.getSignal()) {
            throw new BizException(result.getMessage());
        }*/
        return Result.ok(thingModelEntity.getId());
    }

    @Override
    public Result<List<ThingModelEntity>> listDeviceInheritModel(Long deviceId, TenantIsolation tenantIsolation) {
        return Result.ok(thingModelMapper.listDeviceInheritModel(deviceId, tenantIsolation));
    }

    @Override
    public Result<List<ThingModelEntity>> listInheritModel(Long thingModelId, TenantIsolation tenantIsolation) {
        return Result.ok(thingModelMapper.listInheritModel(thingModelId, tenantIsolation));
    }


    private void createThingModelByLabels(List<LabelEntity> labels, ThingModelEntity thingModelEntity
            , TenantIsolation tenantIsolation) {

        thingModelEntity.setModel(labelService.toModelFieldByLabels(labels));

        thingModelMapper.insert(thingModelEntity);


        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());
        Result<ThingModel> thingModelResult = ThingModel.checkInfo(
                thingModelEntity,
                commonFetcher
        );

        if (!thingModelResult.getSignal()) {
            throw new BizException(thingModelResult.getMessage());
        }

    }

    @Override
    public Result<ThingModelEntity> getThingModelEntity(TenantIsolation tenantIsolation, Long modelId) {
        ThingModelEntity thingModelEntity = thingModelMapper.getById(tenantIsolation.getTenantId(), modelId);
        if (ObjectUtil.isNull(thingModelEntity)) {
            return Result.error("找不到物模型");
        }
        return Result.ok(thingModelEntity);
    }

    @Override
    @Transactional
    public Result<Long> appendThingModelByLabelIds(AppendThingModelByLabelIdsDTO dto, TenantIsolation tenantIsolation) {
        Result<ThingModelEntity> entityResult = getThingModelEntity(tenantIsolation, dto.getModelId());
        if (!entityResult.getSignal()) {
            return Result.error(entityResult.getMessage());
        }
        ThingModelEntity thingModelEntity = entityResult.getResult();
        List<PropertyElm> propertyElmList = null;
        if (ObjectUtil.isNull(thingModelEntity.getModel())) {
            propertyElmList = new ArrayList<>();
        } else {
            propertyElmList = thingModelEntity.getModel().getProperties();
            if (CollectionUtil.isEmpty(propertyElmList)) {
                propertyElmList = new ArrayList<>();
            }
        }
        List<LabelEntity> labels = labelService.listByIds(dto.getLabelIds(), tenantIsolation);
        List<PropertyElm> propertyElms = new ArrayList<>();

        List<String> baseNames = DeviceModel.getBaseModel().getProperties().stream().map(Property::getName).collect(Collectors.toList());

        for (LabelEntity label : labels) {
            if(baseNames.contains(label.getName())){
                throw new BizException("标签名与属性保留字冲突");
            }
            propertyElms.add(label.toPropertyElm());
        }
        propertyElmList.addAll(propertyElms);
        ModelField modelField = new ModelField();
        BeanUtil.copyProperties(thingModelEntity.getModel(), modelField);
        modelField.setProperties(propertyElmList);
        thingModelEntity.setModel(modelField);
        int result = thingModelMapper.updateById(thingModelEntity);
        if (result == 0) {
            throw new BizException("更新失败");
        }
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());
        Result<ThingModel> thingModelResult = ThingModel.checkInfo(
                thingModelEntity,
                commonFetcher
        );

        if (!thingModelResult.getSignal()) {
            throw new BizException(thingModelResult.getMessage());
        }
        return Result.ok(thingModelEntity.getId());
    }

    @Override
    public Result<List<ThingServiceEntity>> listModelServices(Long modelId,Long tenantId) {

        //Result<Object> inheritModelIds = modelGraphService.queryInheritByModelId(modelId);
        List<Long> ids = queryInheritIdsByModelId(modelId, tenantId);
//        if (inheritModelIds.getSignal()) {
//            JSONObject inheritIds = JSONUtil.parseObj(inheritModelIds.getResult());
//            JSONArray inherit = inheritIds.getJSONArray("inherit");
//            inherit.forEach(item -> {
//                ids.add(JSONUtil.parseObj(item).getLong("modelId"));
//            });
//        }
        QueryWrapper<ThingServiceEntity> wrapper = new QueryWrapper<>();
        wrapper.in("thing_model_id", ids);
        return Result.ok(thingServiceService.list(wrapper));
    }

    @Override
    public Result listModelProperties(Long modelId,Long tenantId) {

//        Result<Object> inheritModelIds = modelGraphService.queryInheritByModelId(modelId);
//        List<Long> ids = Lists.newArrayList(modelId);
//        if (inheritModelIds.getSignal()) {
//            JSONObject inheritIds = JSONUtil.parseObj(inheritModelIds.getResult());
//            JSONArray inherit = inheritIds.getJSONArray("inherit");
//            inherit.forEach(item -> {
//                ids.add(JSONUtil.parseObj(item).getLong("modelId"));
//            });
//        }
        List<Long> ids = queryInheritIdsByModelId(modelId, tenantId);
        List<ThingModelEntity> thingModelEntities = this.listByIds(ids);
        List<PropertyElm> propertyElms = Lists.newArrayList();
        thingModelEntities.forEach(m -> {
            propertyElms.addAll(m.getModel().getProperties());
        });
        return Result.ok(propertyElms);
    }

    private List<Long> queryInheritIdsByModelId(Long modelId, Long tenantId) {
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantId);
        List<ThingModelInheritEntity> thingModelInheritEntityList = commonFetcher.list("tenant_id", tenantId, ThingModelInheritEntity.class);
        Result<Set<Long>> inheritModelIds = queryInheritByModelId(modelId,tenantId,thingModelInheritEntityList);
        List<Long> ids = Lists.newArrayList(modelId);
        if(inheritModelIds.getSignal() && CollectionUtil.isNotEmpty(inheritModelIds.getResult())){
            ids.addAll(inheritModelIds.getResult());
        }
        return ids;
    }

    @Override
    public Result<ThingModelValidRepeatVo> validRepeat(TenantIsolation tenantIsolation, ThingModelValidRepeatDto thingModelValidRepeatDto) {
        if (CollectionUtil.isEmpty(thingModelValidRepeatDto.getIdList())) {
            return Result.error("请选择物模型！");
        }
        List<ThingModelDto> list = new ArrayList<>();
        thingModelValidRepeatDto.getIdList().forEach(id -> {
            Result<ThingModelDto> thingModelById = getThingModelById(tenantIsolation, id);
            if (!thingModelById.getSignal()) {
                throw new BizException(thingModelById.getMessage());
            }
            list.add(thingModelById.getResult());
        });
        List<ThingModelEventVo> eventVoList = new ArrayList<>();
        List<ThingModelPropertyVo> propertyVoList = new ArrayList<>();
        List<ThingModelServiceVo> serviceVoList = new ArrayList<>();
        List<ThingModelSubscriptionVo> subscriptionVoList = new ArrayList<>();
        for (ThingModelDto thingModelDto : list) {
            // 拼接物模型本身属性信息
            ModelFieldDto model = thingModelDto.getModel();
            List<ThingModelEventVo> modelEvents = BeanUtil.copyToList(model.getEvents(), ThingModelEventVo.class);
            if (CollectionUtil.isNotEmpty(modelEvents)) {
                modelEvents.forEach(i -> i.setBelongModel(thingModelDto.getName()));
                eventVoList.addAll(modelEvents);
            }
            List<ThingModelPropertyVo> modelPropertys = BeanUtil.copyToList(model.getProperties(), ThingModelPropertyVo.class);
            if (CollectionUtil.isNotEmpty(modelPropertys)) {
                modelPropertys.forEach(i -> i.setBelongModel(thingModelDto.getName()));
                propertyVoList.addAll(modelPropertys);
            }
            // 拼接物模型继承的属性信息
            List<ModelDpo> modelContainsInherits = thingModelDto.getModelContainsInherits();
            for (ModelDpo modelContainsInherit : modelContainsInherits) {
                List<ThingModelEventVo> eventElms = BeanUtil.copyToList(modelContainsInherit.getEvents(), ThingModelEventVo.class);
                if (CollectionUtil.isNotEmpty(eventElms)) {
                    eventElms.forEach(i -> i.setBelongModel(thingModelDto.getName()));
                    eventVoList.addAll(eventElms);
                }
                List<ThingModelPropertyVo> propertyElms = BeanUtil.copyToList(modelContainsInherit.getProperties(), ThingModelPropertyVo.class);
                if (CollectionUtil.isNotEmpty(propertyElms)) {
                    propertyElms.forEach(i -> i.setBelongModel(thingModelDto.getName()));
                    propertyVoList.addAll(propertyElms);
                }
                List<ThingModelServiceVo> serviceVos = BeanUtil.copyToList(modelContainsInherit.getServices(), ThingModelServiceVo.class);
                if (CollectionUtil.isNotEmpty(serviceVos)) {
                    serviceVos.forEach(i -> i.setBelongModel(thingModelDto.getName()));
                    serviceVoList.addAll(serviceVos);
                }
                List<ThingModelSubscriptionVo> subscriptionVos = BeanUtil.copyToList(modelContainsInherit.getSubscriptions(), ThingModelSubscriptionVo.class);
                if (CollectionUtil.isNotEmpty(subscriptionVos)) {
                    subscriptionVos.forEach(i -> i.setBelongModel(thingModelDto.getName()));
                    subscriptionVoList.addAll(subscriptionVos);
                }
            }

            List<ThingModelServiceVo> modelServiceVos = BeanUtil.copyToList(model.getServices(), ThingModelServiceVo.class);
            if (CollectionUtil.isNotEmpty(modelServiceVos)) {
                modelServiceVos.forEach(i -> i.setBelongModel(thingModelDto.getName()));
                serviceVoList.addAll(modelServiceVos);
            }
            List<ThingModelSubscriptionVo> modelSubscriptionVos = BeanUtil.copyToList(model.getSubscriptions(), ThingModelSubscriptionVo.class);
            if (CollectionUtil.isNotEmpty(modelSubscriptionVos)) {
                modelSubscriptionVos.forEach(i -> i.setBelongModel(thingModelDto.getName()));
                subscriptionVoList.addAll(modelSubscriptionVos);
            }
        }
        Map<String, List<ThingModelEventVo>> eventMap = eventVoList.stream().collect(Collectors.groupingBy(i -> i.getName()));
        Map<String, List<ThingModelPropertyVo>> propertyMap = propertyVoList.stream().collect(Collectors.groupingBy(i -> i.getName()));
        Map<String, List<ThingModelServiceVo>> serviceMap = serviceVoList.stream().collect(Collectors.groupingBy(i -> i.getServiceName()));
        Map<String, List<ThingModelSubscriptionVo>> subscriptionMap = subscriptionVoList.stream().collect(Collectors.groupingBy(i -> i.getName()));
        eventVoList.removeIf(i -> eventMap.get(i.getName()).size() == 1);
        propertyVoList.removeIf(i -> propertyMap.get(i.getName()).size() == 1);
        serviceVoList.removeIf(i -> serviceMap.get(i.getServiceName()).size() == 1);
        subscriptionVoList.removeIf(i -> subscriptionMap.get(i.getName()).size() == 1);
        ThingModelValidRepeatVo thingModelValidRepeatVo = ThingModelValidRepeatVo.builder().eventList(eventVoList).propertyList(propertyVoList).serviceList(serviceVoList).subscriptionList(subscriptionVoList).build();
        return Result.ok(thingModelValidRepeatVo);
    }

    @Override
    public Result<ThingTransmitModelDto> exportModel(TenantIsolation tenantIsolation, Long entityId) {
        ThingModelEntity thingModelEntity = thingModelMapper.getById(tenantIsolation.getTenantId(), entityId);
        if (ObjectUtil.isNull(thingModelEntity)) {
            return Result.error("找不到物模型");
        }
        ThingTransmitModelDto dto = BeanUtilsIntensifier.copyBean(thingModelEntity, ThingTransmitModelDto.class);
        ModelFieldDto model = new ModelFieldDto();
        dto.setModel(model);
        if (thingModelEntity.getModel() != null) {
            //属性
            model.setProperties(thingModelEntity.getModel().getProperties());
            //事件
            model.setEvents(thingModelEntity.getModel().getEvents());
        }

        //物模型
        List<ThingServiceEntity> services = thingServiceMapper.listByThingModelId(tenantIsolation.getTenantId(), entityId);
        if(CollectionUtil.isNotEmpty(services)){
            model.setServices(services);
        }
        //订阅
        SubscriptionEntity subscriptionEntity = new SubscriptionEntity();
        subscriptionEntity.setTenantId(tenantIsolation.getTenantId());
        subscriptionEntity.setDirectlyModelId(entityId);
        subscriptionEntity.setModelType(ModelTypeEnum.THING_MODEL.getValue());
        model.setSubscriptions(subscriptionService.list(subscriptionEntity).getResult());
        return Result.ok(dto);
    }

    @Override
    public Result<ThingModelImportVo> importModel(TenantIsolation tenantIsolation, ThingTransmitModelDto thingTransmitModelDto) {
        Result<ThingModelImportVo> selfModelResult = checkSelfModel(thingTransmitModelDto);
        if(!selfModelResult.getSignal()){
            throw new BizException(selfModelResult.getMessage());
        }
        if(CollectionUtil.isNotEmpty(selfModelResult.getResult().getPropertyMessageList()) || CollectionUtil.isNotEmpty(selfModelResult.getResult().getEventMessageList())
                || CollectionUtil.isNotEmpty(selfModelResult.getResult().getServiceMessageList()) || CollectionUtil.isNotEmpty(selfModelResult.getResult().getSubscriptionMessageList())){
            return selfModelResult;
        }
        importModel(tenantIsolation,thingTransmitModelDto,null,new ArrayList<>());
        return Result.ok();
    }

    @Transactional
    public Result<Void> importModel(TenantIsolation tenantIsolation, ThingTransmitModelDto thingTransmitModelDto,Long modelId,List<Long> inheritThingModelIds) {
        String name = thingTransmitModelDto.getName();
        if (StringUtils.isAllBlank(name)) {
            return Result.error("物模型名称不能为空");
        }

        name = getNotRepeatName(tenantIsolation, name);


        ModelField model = new ModelField();
        List<PropertyElm> propertyElms = thingTransmitModelDto.getModel().getProperties();
        if(CollectionUtil.isNotEmpty(propertyElms)){
            model.setProperties(propertyElms);
        }

        List<EventElm> eventElms = thingTransmitModelDto.getModel().getEvents();
        if(CollectionUtil.isNotEmpty(eventElms)){
            model.setEvents(eventElms);
        }
        Long thingModelId = IdUtil.getSnowflake().nextId();
        ThingModelEntity thingModelEntity = null;
        if(ObjectUtil.isNotNull(modelId)){
            thingModelId = modelId;
            thingModelEntity = ThingModelEntity.builder()
                    .id(thingModelId)
                    .tenantId(tenantIsolation.getTenantId())
                    .name(name).descript(thingTransmitModelDto.getDescript()).modelType(thingTransmitModelDto.getModelType()).model(model)
                    .build();
            thingModelMapper.updateById(thingModelEntity);
            thingServiceService.deleteByThingModelId(tenantIsolation.getTenantId(), thingModelId);
            subscriptionService.deleteByThingModelId(tenantIsolation.getTenantId(), thingModelId);
        }else {
            thingModelEntity = ThingModelEntity.builder()
                    .id(thingModelId)
                    .tenantId(tenantIsolation.getTenantId())
                    .name(name).descript(thingTransmitModelDto.getDescript()).modelType(thingTransmitModelDto.getModelType()).model(model)
                    .build();
            thingModelMapper.insert(thingModelEntity);
        }

        List<ThingServiceEntity> thingServiceEntities = thingTransmitModelDto.getModel().getServices();
        if(CollectionUtil.isNotEmpty(thingServiceEntities)){
            List<ThingServiceEntity> insertThingServiceEntities = new ArrayList<>();
            for(ThingServiceEntity thingServiceEntity : thingServiceEntities){
                if(!RegexUtil.checkName(thingServiceEntity.getServiceName())){
                    throw new BizException(ServiceCodeEnum.THING_SERVICE_NAME_ERROR.getMessage());
                }
                if (RegexUtil.checkInputNameRepeat(thingServiceEntity.getInputData())) {
                    throw new BizException(ServiceCodeEnum.SERVICE_INPUT_NAME_REPEAT.getMessage());
                }
                ThingServiceEntity insertThingServiceEntity = getThingServiceEntity(thingServiceEntity, thingModelId);
                insertThingServiceEntities.add(insertThingServiceEntity);
            }
            if(CollectionUtil.isNotEmpty(insertThingServiceEntities)){
                thingServiceService.saveBatch(insertThingServiceEntities);
            }
        }


        List<SubscriptionEntity> subscriptionEntities = thingTransmitModelDto.getModel().getSubscriptions();
        if(CollectionUtil.isNotEmpty(subscriptionEntities)){
            List<SubscriptionEntity> insertSubscriptionEntities = new ArrayList<>();
            Map<String,Long> nameIdMap = new HashMap<>();
            Map<Long,String> idNameMap = new HashMap<>();

            InstanceRedirectEntity quertInstanceRedirectEntity = new InstanceRedirectEntity();
            quertInstanceRedirectEntity.setTenantId(tenantIsolation.getTenantId());
            Result<List<InstanceRedirectEntity>> listResult = instanceRedirectService.list(quertInstanceRedirectEntity);
            if(!listResult.getSignal()){
                for(InstanceRedirectEntity redirectEntity : listResult.getResult()) {
                    idNameMap.put(redirectEntity.getId(),redirectEntity.getRedirectName());
                    if(redirectEntity.getTenantId().toString().equals(tenantIsolation.getTenantId().toString())){
                        nameIdMap.put(redirectEntity.getRedirectName(),redirectEntity.getId());
                    }
                }
            }
            for (SubscriptionEntity subscriptionEntity : subscriptionEntities){
                Result<Subscription> result = Subscription.checkInfo(thingModelId, null, subscriptionEntity);
                if (!result.getSignal()) {
                    throw new BizException(result.getMessage());
                }
                if (BeanUtilsIntensifier.checkBeanAndProperties(subscriptionEntity, SubscriptionEntity::getName, SubscriptionEntity::getProperties,
                        SubscriptionEntity::getSendOneByOne, SubscriptionEntity::getOutType,
                        SubscriptionEntity::getEventType, SubscriptionEntity::getDirectlyModelId, SubscriptionEntity::getModelType)) {
                    throw new BizException(ServiceCodeEnum.CODE_PARAM_ERROR.getMessage());
                }
                if(!subscriptionService.checkSubscription(subscriptionEntity)){
                    throw new BizException(ServiceCodeEnum.CODE_PARAM_ERROR.getMessage());
                }
                SubscriptionEntity insertSubscriptionEntity = getSubscriptionEntity(subscriptionEntity, thingModelId);
                String redirectName = idNameMap.get(insertSubscriptionEntity.getCallbackId());
                if(ObjectUtil.isNotNull(redirectName) && ObjectUtil.isNotNull(nameIdMap.get(redirectName))){
                    insertSubscriptionEntity.setCallbackId(nameIdMap.get(redirectName));
                }
                insertSubscriptionEntities.add(insertSubscriptionEntity);
            }
            if(CollectionUtil.isNotEmpty(insertSubscriptionEntities)){
                subscriptionService.saveBatch(insertSubscriptionEntities);
            }
        }
        //模型关系图
        /*Result<Void> saveResult = modelGraphService.saveModelNode(thingModelEntity, inheritThingModelIds);
        if (!saveResult.getSignal()) {
            throw new BizException(saveResult.getMessage());
        }*/
        return Result.ok();
    }

    private String getNotRepeatName(TenantIsolation tenantIsolation, String name) {
        Result<Void> uniqueNameResult = this.uniqueName(name, tenantIsolation);
        if (!uniqueNameResult.getSignal()) {
            name = name + "_副本" ;
          return this.getNotRepeatName(tenantIsolation,name);
        }
        return name;
    }


    @Override
    public Result<ThingModelImportVo> coverImportModel(TenantIsolation tenantIsolation, ThingTransmitModelDto thingTransmitModelDto, Long entityId) {
        Result<ThingModelDto> thingModelDtoResult = getThingModelById(tenantIsolation,entityId);
        if(!thingModelDtoResult.getSignal()){
            throw new BizException(thingModelDtoResult.getMessage());
        }
        Result<ThingModelImportVo> checkInheritModelResult = checkInheritModel(thingModelDtoResult.getResult(),tenantIsolation,thingTransmitModelDto,entityId);
        if(!checkInheritModelResult.getSignal()){
            throw new BizException(checkInheritModelResult.getMessage());
        }
        Result<ThingModelImportVo> selfModelResult = checkSelfModel(thingTransmitModelDto);
        if(!selfModelResult.getSignal()){
            throw new BizException(selfModelResult.getMessage());
        }

        ThingModelImportVo thingModelImportVo = new ThingModelImportVo();

        List<ExcelMessageDTO> propertyMessageList = new ArrayList<>();
        propertyMessageList.addAll(checkInheritModelResult.getResult().getPropertyMessageList());
        propertyMessageList.addAll(selfModelResult.getResult().getPropertyMessageList());
        thingModelImportVo.setPropertyMessageList(propertyMessageList);

        List<ExcelMessageDTO> eventMessageList = new ArrayList<>();
        eventMessageList.addAll(checkInheritModelResult.getResult().getEventMessageList());
        eventMessageList.addAll(selfModelResult.getResult().getEventMessageList());
        thingModelImportVo.setEventMessageList(eventMessageList);

        List<ExcelMessageDTO> serviceMessageList = new ArrayList<>();
        serviceMessageList.addAll(checkInheritModelResult.getResult().getServiceMessageList());
        serviceMessageList.addAll(selfModelResult.getResult().getServiceMessageList());
        thingModelImportVo.setServiceMessageList(serviceMessageList);

        List<ExcelMessageDTO> subscriptionMessageList = new ArrayList<>();
        subscriptionMessageList.addAll(checkInheritModelResult.getResult().getSubscriptionMessageList());
        subscriptionMessageList.addAll(selfModelResult.getResult().getSubscriptionMessageList());
        thingModelImportVo.setSubscriptionMessageList(subscriptionMessageList);

        if(CollectionUtil.isNotEmpty(propertyMessageList) || CollectionUtil.isNotEmpty(eventMessageList) || CollectionUtil.isNotEmpty(serviceMessageList) || CollectionUtil.isNotEmpty(subscriptionMessageList)){
            return Result.ok(thingModelImportVo);
        }
        importModel(tenantIsolation,thingTransmitModelDto,entityId,thingModelDtoResult.getResult().getInheritThingModelIds());
        return Result.ok();
    }

    @Override
    public Result<Long> copyThingModel(TenantIsolation tenantIsolation, Long sourceThingModelId) {
        ThingModelEntity entity = this.getById(sourceThingModelId);
        TenantIsolation sourceTenant = new TenantIsolation();
        sourceTenant.setTenantId(entity.getTenantId());
        Result<ThingModelDto> thingModelById = this.getThingModelById(sourceTenant, sourceThingModelId);
        if (!thingModelById.getSignal()){
            throw new BizException(thingModelById.getMessage());
        }
        ThingModelDto thingModelDto = thingModelById.getResult();

        // 拼接物模型本身属性信息
        ModelFieldDto model = thingModelDto.getModel();
        List<ThingServiceEntity> serviceEntityList = BeanUtil.copyToList(model.getServices(), ThingServiceEntity.class);

        List<SubscriptionEntity> subscriptionEntityList = BeanUtil.copyToList(model.getSubscriptions(), SubscriptionEntity.class);

        // 拼接物模型继承的属性信息
        List<ModelDpo> modelContainsInherits = thingModelDto.getModelContainsInherits();
        for (ModelDpo modelContainsInherit : modelContainsInherits) {
            List<EventElm> eventElms = BeanUtil.copyToList(modelContainsInherit.getEvents(), EventElm.class);
            if (CollectionUtil.isNotEmpty(eventElms)) {
                entity.getModel().getEvents().addAll(eventElms);
            }
            List<PropertyElm> propertyElms = BeanUtil.copyToList(modelContainsInherit.getProperties(), PropertyElm.class);
            if (CollectionUtil.isNotEmpty(propertyElms)) {
                entity.getModel().getProperties().addAll(propertyElms);
            }
            List<ThingServiceEntity> services = BeanUtil.copyToList(modelContainsInherit.getServices(), ThingServiceEntity.class);
            if (CollectionUtil.isNotEmpty(services)) {
                serviceEntityList.addAll(services);
            }
            List<SubscriptionEntity> subscriptions = BeanUtil.copyToList(modelContainsInherit.getSubscriptions(), SubscriptionEntity.class);
            if (CollectionUtil.isNotEmpty(subscriptions)) {
                subscriptionEntityList.addAll(subscriptions);
            }
        }
        long thingModelId = IdGenerator.generateId();
        entity.setId(thingModelId);
        entity.setTenantId(tenantIsolation.getTenantId());
        entity.setCreator(null);
        entity.setCreateTime(null);
        entity.setCreatorId(null);
        entity.setUpdatorId(null);
        entity.setUpdateTime(null);
        entity.setUpdator(null);
        this.save(entity);
        for (ThingServiceEntity thingServiceEntity : serviceEntityList) {
            thingServiceEntity.setThingModelId(thingModelId);
            thingServiceEntity.setId(IdGenerator.generateId());
            thingServiceEntity.setTenantId(tenantIsolation.getTenantId());
            thingServiceEntity.setCreator(null);
            thingServiceEntity.setCreateTime(null);
            thingServiceEntity.setCreatorId(null);
            thingServiceEntity.setUpdatorId(null);
            thingServiceEntity.setUpdateTime(null);
            thingServiceEntity.setUpdator(null);
            thingServiceService.save(tenantIsolation,thingServiceEntity);
        }
        for (SubscriptionEntity subscriptionEntity : subscriptionEntityList) {
            subscriptionEntity.setDirectlyModelId(thingModelId);
            subscriptionEntity.setId(IdGenerator.generateId());
            subscriptionEntity.setTenantId(tenantIsolation.getTenantId());
            subscriptionEntity.setCreator(null);
            subscriptionEntity.setCreateTime(null);
            subscriptionEntity.setCreatorId(null);
            subscriptionEntity.setUpdatorId(null);
            subscriptionEntity.setUpdateTime(null);
            subscriptionEntity.setUpdator(null);
            subscriptionService.save(tenantIsolation,subscriptionEntity);
        }

        return Result.ok(thingModelId);
    }

    public Result<ThingModelImportVo> checkInheritModel(ThingModelDto thingModelDto,TenantIsolation tenantIsolation, ThingTransmitModelDto thingTransmitModelDto, Long entityId) {
        if(ObjectUtil.isNull(entityId)){
            throw new BizException("覆盖目标模型ID为不能为空");
        }

        List<ModelDpo> inheritModelDpoList = thingModelDto.getModelContainsInherits();
        Set<String> inheritModelNameSet = new HashSet<>();
        Set<String> inheritPropertyNameSet = new HashSet<>();
        Set<String> inheritEventNameSet = new HashSet<>();
        Set<String> inheritServiceNameSet = new HashSet<>();
        Set<String> inheritSubscriptionNameSet = new HashSet<>();
        for(ModelDpo modelDpo :inheritModelDpoList){
            inheritModelNameSet.add(modelDpo.getName());
            for(PropertyDpo propertyDpo :modelDpo.getProperties()){
                inheritPropertyNameSet.add(propertyDpo.getName());
            }
            for(EventDpo eventDpo :modelDpo.getEvents()){
                inheritEventNameSet.add(eventDpo.getName());
            }
            for(ServiceDpo serviceDpo :modelDpo.getServices()){
                inheritServiceNameSet.add(serviceDpo.getServiceName());
            }
            for(SubscriptionDpo subscriptionDpo :modelDpo.getSubscriptions()){
                inheritSubscriptionNameSet.add(subscriptionDpo.getName());
            }
        }
        ThingModelImportVo thingModelImportVo = new ThingModelImportVo();
        if(inheritModelNameSet.contains(thingTransmitModelDto.getName())){
            throw new BizException("模型名称与继成模型名称重复");
        }
        List<ExcelMessageDTO> propertyMessageList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(thingTransmitModelDto.getModel().getProperties())){
            for(PropertyElm propertyElm : thingTransmitModelDto.getModel().getProperties()){
                if(inheritPropertyNameSet.contains(propertyElm.getName())){
                    ExcelMessageDTO excelMessageDTO = new ExcelMessageDTO();
                    excelMessageDTO.setMessageName(propertyElm.getName());
                    excelMessageDTO.setMessageContent("属性名称与继承重复");
                    propertyMessageList.add(excelMessageDTO);
                }
            }
        }
        thingModelImportVo.setPropertyMessageList(propertyMessageList);

        List<ExcelMessageDTO> eventMessageList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(thingTransmitModelDto.getModel().getEvents())) {
            for (EventElm eventElm : thingTransmitModelDto.getModel().getEvents()) {
                if (inheritEventNameSet.contains(eventElm.getName())) {
                    ExcelMessageDTO excelMessageDTO = new ExcelMessageDTO();
                    excelMessageDTO.setMessageName(eventElm.getName());
                    excelMessageDTO.setMessageContent("事件名称与继承重复");
                    eventMessageList.add(excelMessageDTO);
                }
            }
        }
        thingModelImportVo.setEventMessageList(eventMessageList);

        List<ExcelMessageDTO> thingServiceMessageList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(thingTransmitModelDto.getModel().getServices())) {
            for (ThingServiceEntity thingServiceEntity : thingTransmitModelDto.getModel().getServices()) {
                if (inheritServiceNameSet.contains(thingServiceEntity.getServiceName())) {
                    ExcelMessageDTO excelMessageDTO = new ExcelMessageDTO();
                    excelMessageDTO.setMessageName(thingServiceEntity.getServiceName());
                    excelMessageDTO.setMessageContent("服务名称与继承重复");
                    thingServiceMessageList.add(excelMessageDTO);
                }
            }
        }
        thingModelImportVo.setServiceMessageList(thingServiceMessageList);

        List<ExcelMessageDTO> subscriptionMessageList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(thingTransmitModelDto.getModel().getSubscriptions())) {
            for (SubscriptionEntity subscriptionEntity : thingTransmitModelDto.getModel().getSubscriptions()) {
                if (inheritSubscriptionNameSet.contains(subscriptionEntity.getName())) {
                    ExcelMessageDTO excelMessageDTO = new ExcelMessageDTO();
                    excelMessageDTO.setMessageName(subscriptionEntity.getName());
                    excelMessageDTO.setMessageContent("订阅名称与继承重复");
                    subscriptionMessageList.add(excelMessageDTO);
                }
            }
        }
        thingModelImportVo.setSubscriptionMessageList(subscriptionMessageList);

        return Result.ok(thingModelImportVo);
    }

    public Result<ThingModelImportVo> checkSelfModel(ThingTransmitModelDto thingTransmitModelDto) {
        List<String> propertyNameList = getDuplicateValue(thingTransmitModelDto.getModel().getProperties(),p->p.getName());
        List<String> eventNameList = getDuplicateValue(thingTransmitModelDto.getModel().getEvents(),e->e.getName());
        List<String> thingServiceNameList = getDuplicateValue(thingTransmitModelDto.getModel().getServices(),s->s.getServiceName());
        List<String> subscriptionNameList = getDuplicateValue(thingTransmitModelDto.getModel().getSubscriptions(),s->s.getName());


        ThingModelImportVo thingModelImportVo = new ThingModelImportVo();
        List<ExcelMessageDTO> propertyMessageList = new ArrayList<>();
        for(String name : propertyNameList){
            ExcelMessageDTO excelMessageDTO = new ExcelMessageDTO();
            excelMessageDTO.setMessageName(name);
            excelMessageDTO.setMessageContent("属性名称与自身存在重复");
            propertyMessageList.add(excelMessageDTO);
        }
        List<String> baseNames = DeviceModel.getBaseModel().getProperties().stream().map(Property::getName).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(thingTransmitModelDto.getModel().getProperties())){
            for(PropertyElm propertyElm :thingTransmitModelDto.getModel().getProperties()){
                if(baseNames.contains(propertyElm.getName())){
                    ExcelMessageDTO excelMessageDTO = new ExcelMessageDTO();
                    excelMessageDTO.setMessageName(propertyElm.getName());
                    excelMessageDTO.setMessageContent("属性保留字冲突");
                    propertyMessageList.add(excelMessageDTO);
                }
            }
        }
        thingModelImportVo.setPropertyMessageList(propertyMessageList);

        List<ExcelMessageDTO> eventMessageList = new ArrayList<>();
        for(String name : eventNameList){
            ExcelMessageDTO excelMessageDTO = new ExcelMessageDTO();
            excelMessageDTO.setMessageName(name);
            excelMessageDTO.setMessageContent("事件名称与自身存在重复");
            eventMessageList.add(excelMessageDTO);
        }
        thingModelImportVo.setEventMessageList(eventMessageList);

        List<ExcelMessageDTO> thingServiceMessageList = new ArrayList<>();
        for(String name : thingServiceNameList){
            ExcelMessageDTO excelMessageDTO = new ExcelMessageDTO();
            excelMessageDTO.setMessageName(name);
            excelMessageDTO.setMessageContent("服务名称与自身存在重复");
            thingServiceMessageList.add(excelMessageDTO);
        }
        thingModelImportVo.setServiceMessageList(thingServiceMessageList);

        List<ExcelMessageDTO> subscriptionMessageList = new ArrayList<>();
        for(String name :  subscriptionNameList){
            ExcelMessageDTO excelMessageDTO = new ExcelMessageDTO();
            excelMessageDTO.setMessageName(name);
            excelMessageDTO.setMessageContent("订阅名称与自身存在重复");
            subscriptionMessageList.add(excelMessageDTO);
        }
        thingModelImportVo.setSubscriptionMessageList(subscriptionMessageList);
        return Result.ok(thingModelImportVo);
    }

    private SubscriptionEntity getSubscriptionEntity(SubscriptionEntity subscriptionEntity, Long thingModelId) {
        SubscriptionEntity insertSubscriptionEntity = new SubscriptionEntity();
        insertSubscriptionEntity.setName(subscriptionEntity.getName());
        insertSubscriptionEntity.setDescript(subscriptionEntity.getDescript());
        insertSubscriptionEntity.setProperties(subscriptionEntity.getProperties());
        insertSubscriptionEntity.setEvents(subscriptionEntity.getEvents());
        insertSubscriptionEntity.setEventType(subscriptionEntity.getEventType());
        insertSubscriptionEntity.setCallbackId(subscriptionEntity.getCallbackId());
        insertSubscriptionEntity.setDirectlyModelId(thingModelId);
        insertSubscriptionEntity.setModelType(subscriptionEntity.getModelType());
        insertSubscriptionEntity.setSendOneByOne(subscriptionEntity.getSendOneByOne());
        insertSubscriptionEntity.setOutType(subscriptionEntity.getOutType());
        insertSubscriptionEntity.setTargetDevice(subscriptionEntity.getTargetDevice());
        insertSubscriptionEntity.setTargetService(subscriptionEntity.getTargetService());
        insertSubscriptionEntity.setReceivers(subscriptionEntity.getReceivers());
        insertSubscriptionEntity.setEnable(subscriptionEntity.getEnable());
        insertSubscriptionEntity.setNoChangeSeconds(subscriptionEntity.getNoChangeSeconds());
        insertSubscriptionEntity.setDataConversionCode(subscriptionEntity.getDataConversionCode());
        insertSubscriptionEntity.setFromId(subscriptionEntity.getFromId());
        return insertSubscriptionEntity;
    }

    private ThingServiceEntity getThingServiceEntity(ThingServiceEntity thingServiceEntity, Long thingModelId) {
        ThingServiceEntity insertThingServiceEntity = new ThingServiceEntity();
        insertThingServiceEntity.setThingModelId(thingModelId);
        insertThingServiceEntity.setServiceName(thingServiceEntity.getServiceName());
        insertThingServiceEntity.setDescript(thingServiceEntity.getDescript());
        insertThingServiceEntity.setOverride(thingServiceEntity.getOverride());
        insertThingServiceEntity.setAsync(thingServiceEntity.getAsync());
        insertThingServiceEntity.setInputData(thingServiceEntity.getInputData());
        insertThingServiceEntity.setOutputData(thingServiceEntity.getOutputData());
        insertThingServiceEntity.setServiceCode(thingServiceEntity.getServiceCode());
        insertThingServiceEntity.setServiceType(thingServiceEntity.getServiceType());
        return insertThingServiceEntity;
    }

    private  <E, R> List<R> getDuplicateValue(List<E> list, Function<E, R> function) {
        if(CollectionUtil.isEmpty(list)){
            return new ArrayList<>();
        }
        Map<R, Long> frequencies = list.stream().collect(Collectors.groupingBy(function, Collectors.counting()));
        return frequencies.entrySet().stream()
                .filter(entry -> entry.getValue() > 1).map(entry -> entry.getKey()).collect(Collectors.toList());

    }

    public Result<Set<Long>> queryInheritByModelId(Long modelId,Long tenantId,List<ThingModelInheritEntity> thingModelInheritEntityList) {
        //继承的模型ID
        Set<Long> modelIdList = new HashSet<>();
        modelIdList.add(modelId);
        Set<Long> allModelId = new HashSet<>();
        for(Long id : modelIdList){
            allModelId.add(id);
            findParentIds(thingModelInheritEntityList,id,allModelId);
        }
        return Result.ok(allModelId);
    }


    public void findParentIds(List<ThingModelInheritEntity> thingModelInheritEntityList, Long id, Set<Long> parentIds) {
        for (ThingModelInheritEntity thingModelInheritEntity : thingModelInheritEntityList) {
            if (thingModelInheritEntity.getThingModelId() == id) {
                parentIds.add(thingModelInheritEntity.getInheritThingModelId());
                findParentIds(thingModelInheritEntityList, thingModelInheritEntity.getInheritThingModelId(), parentIds);
            }
        }
    }

    @Override
    public void sinkSubscribe() {
        LambdaQueryWrapper<ThingModelEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(ThingModelEntity::getId,999L)
                .eq(ThingModelEntity::getName,"Common_Type");
        List<ThingModelEntity> thingModelEntities = thingModelMapper.selectList(lqw);
        if (CollUtil.isEmpty(thingModelEntities)){
            return;
        }
        List<Long> modelIdList = thingModelEntities.stream().map(ThingModelEntity::getId).collect(Collectors.toList());
        modelIdList.add(999L);
        List<ThingModelInheritEntity> inheritEntityList = thingModelInheritService.list(Wrappers.<ThingModelInheritEntity>lambdaQuery().in(ThingModelInheritEntity::getInheritThingModelId, modelIdList)
                .eq(ThingModelInheritEntity::getDeleted, 0));

        List<SubscriptionEntity> subscriptionEntityList = subscriptionService.list(Wrappers.<SubscriptionEntity>lambdaQuery().in(SubscriptionEntity::getDirectlyModelId, modelIdList)
                        .eq(SubscriptionEntity::getName,"deviceStateChange")
                .eq(SubscriptionEntity::getDeleted, 0));
        List<SubscriptionEntity> addList = new ArrayList<>();
        for (ThingModelEntity thingModelEntity : thingModelEntities) {
            SubscriptionEntity subscriptionEntity = subscriptionEntityList.stream().filter(s -> s.getDirectlyModelId().equals(thingModelEntity.getId())).findFirst().orElse(null);
            if (subscriptionEntity!=null){
                inheritEntityList.stream().filter(thingModelInheritEntity -> thingModelInheritEntity.getTenantId().equals(subscriptionEntity.getTenantId())).forEach(thingModelInheritEntity -> {
                    SubscriptionEntity addSubscript = BeanUtilsIntensifier.copyBean(subscriptionEntity, SubscriptionEntity.class);
                    addSubscript.setDirectlyModelId(thingModelInheritEntity.getThingModelId());
                    addSubscript.setId(IdGenerator.generateId());
                    addSubscript.setCreateTime(LocalDateTime.now());
                    addSubscript.setUpdateTime(LocalDateTime.now());
                    addList.add(addSubscript);
                });
            }
        }
        subscriptionService.saveBatch(addList);

    }


}
