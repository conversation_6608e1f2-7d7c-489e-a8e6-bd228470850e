package com.nti56.nlink.product.device.server.openapi.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.it.ITResult;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelDpo;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.EventTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpEventTopic;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.DeviceServiceEntity;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.model.DeviceDto;
import com.nti56.nlink.product.device.server.model.DeviceTwinRequestBo;
import com.nti56.nlink.product.device.server.model.GateWayDeviceListDTO;
import com.nti56.nlink.product.device.server.model.device.vo.DeviceOnlineStatusVO;
import com.nti56.nlink.product.device.server.model.deviceModel.DeviceModelBo;
import com.nti56.nlink.product.device.server.openapi.convertor.ITResultConvertor;
import com.nti56.nlink.product.device.server.openapi.domain.request.*;
import com.nti56.nlink.product.device.server.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 类说明: 设备controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:32:04
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/it")
@Tag(name = "设备模块")
public class ITDeviceController {

    @Autowired
    @Lazy
    IDeviceService deviceService;

    @Autowired
    ITaskService taskService;

    @Autowired
    IDeviceModelService deviceModelService;

    @Autowired
    IDeviceServiceService productServiceService;

    @Autowired
    IDeviceDebugService deviceDebugService;

    @Autowired
    private ICommonServiceService commonServiceService;

    @PostMapping(path = "device/page")
    @Operation(summary = "获取设备分页")
    public ITResult<Page<DeviceDto>> pageDevice(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                                @RequestBody PageDevicesRequest request) {
        BeanUtilsIntensifier.propertyInjection(tenantIsolation, request.getDto());
        Page<DeviceDto> page = request.getPageParam().toPage(DeviceDto.class);
        Result<Page<DeviceDto>> result = deviceService.getDevicePage(request.getDto(), page);
        return ITResultConvertor.convert(R.result(result));
    }

    @PostMapping(path = "device/list")
    @Operation(summary = "获取设备列表")
    public ITResult<List<DeviceEntity>> listDevice(@RequestHeader("ot_headers") TenantIsolation tenantIsolation) {
        DeviceEntity entity = DeviceEntity.builder().build();
        BeanUtilsIntensifier.propertyInjection(tenantIsolation, entity);
        Result<List<DeviceEntity>> result = deviceService.listDevice(entity);
        return ITResultConvertor.convert(R.result(result));
    }

    @PostMapping(path = "device/list-by-name")
    @Operation(summary = "获取设备列表")
    public ITResult<List<DeviceEntity>> listDeviceByName(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                                   @RequestBody ListDevicesRequest request) {
        Result<List<DeviceEntity>> result = deviceService.listDeviceByName(tenantIsolation,request);
        return ITResultConvertor.convert(R.result(result));
    }

    @PostMapping(path = "device/byId")
    @Operation(summary = "获取设备", description = "通过ID一个设备", parameters = {
            @Parameter(name = "deviceId", description = "设备ID", required = true)
    })
    public ITResult<DeviceDto> getDevice(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                         @RequestBody GetDeviceRequest request) {
        DeviceEntity build = DeviceEntity.builder().id(request.getDeviceId()).build();
        BeanUtilsIntensifier.propertyInjection(tenantIsolation, build);
        Result<DeviceDto> result = deviceService.getDevice(build);
        return ITResultConvertor.convert(R.result(result));
    }

    @PostMapping("list/model")
    @Operation(summary = "根据设备ID列表 获取设备模型列表")
    public R listDeviceModel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestBody @Param("deviceIds") List<Long> deviceIds) {
        if (CollectionUtil.isEmpty(deviceIds)) {
            return R.ok();
        }
        Result<Set<ModelDpo>> result = deviceModelService.listModelByDeviceIds(deviceIds, tenantIsolation);
        return R.result(result);
    }

    @PostMapping("list/model/byDeviceName")
    @Operation(summary = "根据设备名称列表 获取设备模型列表")
    public R listDeviceModelByDeviceName(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestBody @Param("deviceNames") List<String> deviceNames) {
        if (CollectionUtil.isEmpty(deviceNames)) {
            return R.ok();
        }
        Result<Set<ModelDpo>> result = deviceModelService.listModelByDeviceNames(deviceNames, tenantIsolation);
        return R.result(result);
    }

    @PostMapping(path = "device/property/byId")
    @Operation(summary = "通过设备获取属性")
    public ITResult<DeviceModelBo> getDeviceProperties(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                                       @RequestBody GetDeviceRequest request) {
        return ITResultConvertor
                .convert(R.result(deviceService.getDeviceProperties(tenantIsolation.getTenantId(), request.getDeviceId())));
    }

    @PostMapping(path = "device/do-service")
    @Operation(summary = "服务调用")
    public ITResult<Object> doServiceTask(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                          @RequestBody DoServiceTaskRequest request) {
        DeviceServiceLogEntity logEntity = DeviceServiceLogEntity.builder()
                .createTime(LocalDateTime.now()).callType(3).deviceId(request.getDeviceId())
                .serviceName(request.getServiceName()).build();
        R r = deviceService.doServiceTaskWithoutContext(tenantIsolation.getTenantId(), request.getDeviceId(),
                request.getServiceName(), request.getInput(), logEntity);
        return ITResultConvertor.convert(r);
    }

    @PostMapping(path = "device/do-service-by-name")
    @Operation(summary = "服务调用")
    public ITResult<Object> doServiceTaskByName(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                          @RequestBody DoServiceTaskRequest request) {
        DeviceServiceLogEntity logEntity = DeviceServiceLogEntity.builder()
                .createTime(LocalDateTime.now()).callType(3).deviceId(request.getDeviceId())
                .serviceName(request.getServiceName()).build();
        String deviceName = request.getDeviceName();
        DeviceEntity deviceEntity = deviceService.getDeviceByName(tenantIsolation.getTenantId(), deviceName);
        if(deviceEntity == null){
            return ITResultConvertor.convert(R.error("找不到设备: " + deviceName));
        }
        R r = deviceService.doServiceTask(tenantIsolation.getTenantId(), deviceEntity.getId(),
                request.getServiceName(), request.getInput(), logEntity,true);
        return ITResultConvertor.convert(r);
    }

    @PostMapping(path = "device/do-service-by-name-without-context")
    @Operation(summary = "服务调用")
    public ITResult<Object> doServiceTaskByNameWithoutContext(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                          @RequestBody DoServiceTaskRequest request) {
        DeviceServiceLogEntity logEntity = DeviceServiceLogEntity.builder()
                .createTime(LocalDateTime.now()).callType(3).deviceId(request.getDeviceId())
                .serviceName(request.getServiceName()).build();
        String deviceName = request.getDeviceName();
        DeviceEntity deviceEntity = deviceService.getDeviceByName(tenantIsolation.getTenantId(), deviceName);
        if(deviceEntity == null){
            return ITResultConvertor.convert(R.error("找不到设备: " + deviceName));
        }
        R r = deviceService.doServiceTaskWithoutContext(tenantIsolation.getTenantId(), deviceEntity.getId(),
                request.getServiceName(), request.getInput(), logEntity);
        return ITResultConvertor.convert(r);
    }

    @PostMapping(path = "device/do-services")
    @Operation(summary = "服务调用")
    public ITResult<Object> doServiceTask(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                          @RequestBody List<DoServiceTaskRequest> requests) {
        List<R> results = new ArrayList<>(requests.size());
        for (DoServiceTaskRequest request : requests) {
            DeviceServiceLogEntity logEntity = DeviceServiceLogEntity.builder()
                    .createTime(LocalDateTime.now()).callType(3).deviceId(request.getDeviceId())
                    .serviceName(request.getServiceName()).build();
            R r = deviceService.doServiceTask(tenantIsolation.getTenantId(), request.getDeviceId(),
                    request.getServiceName(), request.getInput(), logEntity,true);
            results.add(r);
        }
        return ITResultConvertor.convert(R.ok(results));
    }

    @PostMapping(path = "device/twin")
    @Operation(summary = "根据租户ID获取所有设备属性值")
    public ITResult<Object> getDeviceTwinsByTenant(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestBody DeviceTwinRequestBo request) {
        Result r = deviceDebugService.getByTenantId(tenantIsolation, request);
        return ITResultConvertor.convert(R.result(r));
    }

    @PostMapping(path = "device/onlineStatus")
    @Operation(summary = "获取设备状态实时信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DeviceOnlineStatusVO.class)
                    )})
    })
    public ITResult<List<DeviceOnlineStatusVO>> onlineStatus(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestBody GateWayDeviceListDTO dto) {
        return ITResultConvertor.convert(R.result(deviceService.listDeviceOnlineStatus(dto, tenantIsolation)));
    }


    @PostMapping(path = "device/doCommonService")
    @Operation(summary = "公共服务调用")
    public ITResult<Object> doSwitchService(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                            @RequestBody DoCommonServiceRequest request) {

        Result execute = commonServiceService.execute(tenantIsolation, request);
        if (execute.getSignal()) {
            return ITResult.succeed(execute.getResult(), "success");
        } else {
            return ITResult.fail(execute.getMessage());
        }

    }

    @PostMapping(path = "device/deleteServiceTask")
    @Operation(summary = "删除定时服务")
    public ITResult<Object> doSwitchService(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                            @RequestParam ("bId") Long bId) {

        Result execute = commonServiceService.deleteServiceTask(tenantIsolation, bId);
        if (execute.getSignal()) {
            return ITResult.succeed(execute.getResult(), "success");
        } else {
            return ITResult.fail(execute.getMessage());
        }

    }

    @PostMapping(path = "device/listByModelId")
    @Operation(summary = "获取设备列表")
    public ITResult<List<DeviceEntity>> listDeviceByModel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestParam("modelId") Long modelId) {
        Result<List<DeviceEntity>> result = deviceService.listByInheritModelId(modelId,tenantIsolation.getTenantId());
        return ITResultConvertor.convert(R.result(result));
    }


    @PostMapping(path = "device/listServices")
    @Operation(summary = "根据设备ID获取所有设备服务")
    public ITResult<List<DeviceServiceEntity>> listServices(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestParam("deviceId") Long deviceId) {

        Result r = deviceService.listDeviceService(tenantIsolation, deviceId);
        return ITResultConvertor.convert(R.result(r));
    }


    @PostMapping(path = "device/propValues")
    @Operation(summary = "根据设备ID获取所有属性值")
    public ITResult<Object> getDevicePropValue(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestParam("deviceId") Long deviceId) {
        DeviceTwinRequestBo request = new DeviceTwinRequestBo();
        request.setIds(Lists.newArrayList(deviceId));
        Result r = deviceDebugService.getByTenantId(tenantIsolation, request);
        return ITResultConvertor.convert(R.result(r));
    }


    @PostMapping(path = "device/endFault")
    @Operation(summary = "结束故障")
    public ITResult<Object> endFault(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                     @RequestBody EndFaultRequest request) {
        GwUpEventTopic.TopicInfo build = GwUpEventTopic.TopicInfo.builder().tenantId(tenantIsolation.getTenantId().toString())
                .deviceId(request.getDeviceId())
                .eventName(request.getEventName())
                .eventType(EventTypeEnum.FAULT.getName())
                .build();
        taskService.endFault(build, request.getDeviceName(), request.getLevel());
        return ITResultConvertor
                .convert(R.ok());
    }

}
