package com.nti56.nlink.product.device.server.domain.thing.topic;

import com.nti56.nlink.product.device.server.domain.thing.enumerate.MqttTopicEnum;

import lombok.Builder;
import lombok.Data;

/**
 * 类说明: 网关上报标签topic
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 09:01:54
 * @since JDK 1.8
 */
public class GwUpLabelTopic {
    
    public static final String TYPE_LABEL_CHANGE = "labelChange";
    public static final String TYPE_LABEL_REPORT = "labelReport";

    @Data
    @Builder
    public static class TopicInfo{
        private String tenantId;
        private String edgeGatewayId;
        private String type;
        private String labelId;
    }

    public static TopicInfo parseTopic(String topic){
        String[] split = topic.split("/");
        String tenantId = split[2];
        String edgeGatewayId = split[3];
        String type = split[4];
        String labelId = split[5];
        
        return TopicInfo.builder()
            .tenantId(tenantId)
            .edgeGatewayId(edgeGatewayId)
            .type(type)
            .labelId(labelId)
            .build();
    }

    /**
     * 创建标签改变订阅topic
     */
    public static String createChangeSubscribeTopic(String group){
        return "$share/" + group + "/" + MqttTopicEnum.GW_UP.getPrefix() 
                + "+/+/"
                + "labelChange/#";
    }
    /**
     * 创建标签改变触发topic
     */
    public static String  createChangeTopic(Long tenantId, Long edgeGatewayId, Long labelId){
        return MqttTopicEnum.GW_UP.getPrefix() 
                + tenantId + "/"
                + edgeGatewayId + "/"
                + "labelChange/" + labelId;
    }
    /**
     * 创建标签改变触发topic
     */
    public static String  createChangeTopic(Long tenantId, Long edgeGatewayId, Integer intervalMs){
        return MqttTopicEnum.GW_UP.getPrefix() 
                + tenantId + "/"
                + edgeGatewayId + "/"
                + "labelChange/" + intervalMs;
    }

    /**
     * 创建标签上报订阅topic
     */
    public static String createReportSubscribeTopic(String group){
        return "$share/" + group + "/" + MqttTopicEnum.GW_UP.getPrefix() 
                + "+/+/"
                + "labelReport/#";
    }
    /**
     * 创建标签上报触发topic
     */
    public static String createReportTopic(Long tenantId, Long edgeGatewayId, Long labelId){
        return MqttTopicEnum.GW_UP.getPrefix() 
                + tenantId + "/"
                + edgeGatewayId + "/"
                + "labelReport/" + labelId;
    }
 
}
