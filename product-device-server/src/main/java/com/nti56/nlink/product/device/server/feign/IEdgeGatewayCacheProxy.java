package com.nti56.nlink.product.device.server.feign;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.device.AccessElm;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.model.ConnectResult;

import java.util.List;

public interface IEdgeGatewayCacheProxy {

    Result<List<ConnectResult>> labelValue(Long edgeGatewayId,
                                Long tenantId,
                                List<AccessElm> labelList);

    Result<List<ConnectResult>> channelConnection(Long edgeGatewayId,
                                Long tenantId,
                                List<ChannelElm> channelList);
}
