package com.nti56.nlink.product.device.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.AuditLogEntity;
import com.nti56.nlink.product.device.server.enums.ActionEnum;
import com.nti56.nlink.product.device.server.enums.AuditTargetEnum;
import com.nti56.nlink.product.device.server.mapper.AuditLogMapper;
import com.nti56.nlink.product.device.server.service.AuditLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【audit_log(操作审计日志表)】的数据库操作Service实现
* @createDate 2023-09-18 14:34:53
*/
@Service
public class AuditLogServiceImpl extends ServiceImpl<AuditLogMapper, AuditLogEntity>
    implements AuditLogService {

    @Autowired
    private AuditLogMapper auditLogMapper;

    @Override
    public Result<Page<AuditLogEntity>> getPage(AuditLogEntity entity, Page<AuditLogEntity> page) {

        QueryWrapper<AuditLogEntity> queryWrapper = new QueryWrapper<AuditLogEntity>()
                .orderByDesc("action_timestamp")
                .eq("tenant_id",entity.getTenantId());

        Page<AuditLogEntity> pageResult = this.page(page, queryWrapper);
        return Result.ok(pageResult);
    }

    @Override
    public Result<AuditLogEntity> getLastSync() {

        QueryWrapper<AuditLogEntity> queryWrapper = new QueryWrapper<AuditLogEntity>()
                .eq("details","同步CommonType网关")
                .eq("action_info", ActionEnum.SYNC.getDesc())
                .eq("target", AuditTargetEnum.GATEWAY.getDesc())
                .orderByDesc("action_timestamp")
                .last("limit 1");

        return Result.ok(auditLogMapper.selectOne(queryWrapper));
    }


}




