package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.domain.thing.devicemodel.DeviceModel;
import com.nti56.nlink.product.device.server.entity.DeviceServiceEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.DeviceModelMapper;
import com.nti56.nlink.product.device.server.mapper.DeviceServiceMapper;
import com.nti56.nlink.product.device.server.model.product.dto.ProofreadDeviceServiceDTO;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import com.nti56.nlink.product.device.server.service.IDeviceServiceService;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/27 16:33<br/>
 * @since JDK 1.8
 */
@Service
@Slf4j
public class DeviceServiceServiceImpl extends BaseServiceImpl<DeviceServiceMapper, DeviceServiceEntity> implements IDeviceServiceService {

    @Autowired
    DeviceServiceMapper mapper;

    @Autowired
    DeviceModelMapper deviceModelMapper;

    @Autowired
    CommonFetcherFactory commonFetcherFactory;

    @Autowired
    @Lazy
    private IDeviceService deviceService;

    @Override
    @Transactional
    public Result<DeviceServiceEntity> save(TenantIsolation tenantIsolation, DeviceServiceEntity entity) {
        if(!RegexUtil.checkName(entity.getServiceName())){
            return Result.error(ServiceCodeEnum.THING_SERVICE_NAME_ERROR);
        }
        if (RegexUtil.checkInputNameRepeat(entity.getInputData())) {
            return Result.error(ServiceCodeEnum.SERVICE_INPUT_NAME_REPEAT);
        }
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());
        Result<DeviceModel> modelResult = DeviceModel.checkInfo(
                deviceModelMapper.selectById(entity.getDeviceId()),
                commonFetcher
        );
        if (!modelResult.getSignal()) {
            return Result.error(modelResult.getMessage());
        }
        DeviceModel model = modelResult.getResult();
        List<com.nti56.nlink.product.device.server.domain.thing.modelbase.Service> services = model.getServices();
        if (Optional.ofNullable(services).isPresent() && services.size() > 0) {
            for (com.nti56.nlink.product.device.server.domain.thing.modelbase.Service service : services) {
                if (service.getServiceName().equals(entity.getServiceName()) && !service.getOverride()) {
                    return Result.error(ServiceCodeEnum.THING_SERVICE_OVERRIDE_ERROR);
                }
            }
        }
        try{
            if (mapper.insert(entity) == 1) {
                deviceService.setNotSyncById(entity.getDeviceId());

                return Result.ok(entity);
            }
        }catch (Exception e){
            return Result.error(ServiceCodeEnum.THING_SERVICE_OVERRIDE_ERROR);
        }
        return Result.error(ServiceCodeEnum.CODE_CREATE_FAIL);
    }

    @Override
    public Result<Page<DeviceServiceEntity>> getPage(DeviceServiceEntity entity, Page<DeviceServiceEntity> page) {
        Page<DeviceServiceEntity> list = mapper.selectPage(page, new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    @Override
    public Result<List<DeviceServiceEntity>> list(DeviceServiceEntity entity) {
        List<DeviceServiceEntity> list = mapper.selectList(new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    @Override
    @Transactional
    public Result update(@NotNull DeviceServiceEntity entity, TenantIsolation tenantIsolation) {
        DeviceServiceEntity deviceServiceEntity = this.getByIdAndTenantIsolation(entity.getId(), tenantIsolation).getResult();
        if (deviceServiceEntity == null){
            throw new BizException("该租户下找不到该设备服务");
        }
        if(!entity.getServiceName().equals(deviceServiceEntity.getServiceName())){
            return Result.error(ServiceCodeEnum.SERVICE_NAME_REVISE_ERROR);
        }
        if (RegexUtil.checkInputNameRepeat(entity.getInputData())) {
            return Result.error(ServiceCodeEnum.SERVICE_INPUT_NAME_REPEAT);
        }
        String oldProofreadProductService = JSON.toJSONString(BeanUtilsIntensifier.copyBean(deviceServiceEntity, ProofreadDeviceServiceDTO.class), SerializerFeature.MapSortField);
        String newProofreadProductService = JSON.toJSONString(BeanUtilsIntensifier.copyBean(entity, ProofreadDeviceServiceDTO.class), SerializerFeature.MapSortField);
        if (!oldProofreadProductService.equals(newProofreadProductService)){
            deviceService.setNotSyncById(deviceServiceEntity.getDeviceId());
        }

        if (mapper.updateById(entity) == 1) {
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
    }

    @Override
    public Result<DeviceServiceEntity> getByIdAndTenantIsolation(Long id, TenantIsolation tenantIsolation){
        LambdaQueryWrapper<DeviceServiceEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(DeviceServiceEntity::getId,id)
                .eq(DeviceServiceEntity::getTenantId,tenantIsolation.getTenantId());
        return Result.ok(mapper.selectOne(lqw));
    }

    @Override
    public Result<List<DeviceServiceEntity>> listByDeviceId(TenantIsolation tenantIsolation, Long deviceId) {
        LambdaQueryWrapper<DeviceServiceEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(DeviceServiceEntity::getTenantId, tenantIsolation.getTenantId())
                .eq(DeviceServiceEntity::getDeviceId, deviceId)
                .orderByDesc(DeviceServiceEntity::getCreateTime);
        return Result.ok(mapper.selectList(lqw));
    }

    @Override
    public void deleteByDeviceId(TenantIsolation tenantIsolation, Long deviceId) {
        LambdaUpdateWrapper<DeviceServiceEntity> eq = new LambdaUpdateWrapper<DeviceServiceEntity>()
                .eq(DeviceServiceEntity::getDeviceId, deviceId)
                .eq(DeviceServiceEntity::getTenantId, tenantIsolation.getTenantId());
        mapper.delete(eq);
    }

    @Override
    public void batchDeleteByDeviceIds(TenantIsolation tenantIsolation, List<Long> ids) {
        if (CollectionUtil.isNotEmpty(ids)) {
            LambdaUpdateWrapper<DeviceServiceEntity> eq = new LambdaUpdateWrapper<DeviceServiceEntity>()
                    .in(DeviceServiceEntity::getDeviceId, ids)
                    .eq(DeviceServiceEntity::getTenantId, tenantIsolation.getTenantId());
            mapper.delete(eq);
        }
    }

    @Override
    public Result<Void> deleteBatchByIds(List<Long> ids, TenantIsolation tenantIsolation) {
        List<DeviceServiceEntity> list = new LambdaQueryChainWrapper<>(mapper).select(DeviceServiceEntity::getDeviceId)
                .eq(DeviceServiceEntity::getTenantId, tenantIsolation.getTenantId())
                .in(DeviceServiceEntity::getId, ids).list();
        Set<Long> deviceIds = list.stream().map(DeviceServiceEntity::getDeviceId).collect(Collectors.toSet());
        log.info("批量删除设备服务，tenant:{},ids:{}",tenantIsolation,ids);
        LambdaUpdateWrapper<DeviceServiceEntity> eq = new LambdaUpdateWrapper<DeviceServiceEntity>()
                .eq(DeviceServiceEntity::getTenantId, tenantIsolation.getTenantId())
                .in(DeviceServiceEntity::getId, ids);
        mapper.delete(eq);
        if (CollectionUtil.isNotEmpty(deviceIds)) {
            deviceService.setNotSyncByIds(deviceIds);
        }
        return Result.ok();
    }

    @Override
    public Result deleteById(@NotNull Long entityId, TenantIsolation tenantIsolation) {

        DeviceServiceEntity deviceServiceEntity = this.getByIdAndTenantIsolation(entityId, tenantIsolation).getResult();
        if (deviceServiceEntity == null){
            throw new BizException("该租户下找不到该设备服务");
        }

        if (mapper.deleteById(entityId) == 1) {
            deviceService.setNotSyncById(deviceServiceEntity.getDeviceId());
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }

}
