package com.nti56.nlink.product.device.server.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.model.DeviceBo;
import com.nti56.nlink.product.device.server.model.DeviceChannelBo;
import com.nti56.nlink.product.device.server.model.DeviceDto;
import com.nti56.nlink.product.device.server.model.device.dto.TenantIdDeviceNameDto;
import com.nti56.nlink.product.device.server.openapi.domain.request.ListDcmpDeviceRequest;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;


/**
 * 类说明: 设备mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:08:50
 * @since JDK 1.8
 */
public interface DeviceMapper extends CommonMapper<DeviceEntity> {

    @Select({
            "<script>",
                "select ",
                    "d.id, d.name, d.edge_gateway_id,d.status,d.tenant_id ",
                "from device d ",
                "where d.DELETED = 0",
                    "and d.tenant_id = #{tenantId}",
                    "and d.id in",
                        "<foreach collection='ids' item='id' open='(' separator=',' close=')'>",
                        "#{id}",
                        "</foreach>",
            "</script>"
    })
    List<DeviceBo> listByIds(@Param("ids") List<Long> ids,@Param("tenantId") Long tenantId);

    @Update({
            "<script>",
                "<foreach collection='list' item='device' open='' separator='' close=''>",
                    "update device ",
                    "set source = #{device.source} ,sync_status = 0 ",
                    "where tenant_id = #{tenantId} ",
                    "and id = #{device.id};",
                "</foreach>",
            "</script>"
    })
    void updateDeviceSource(@Param("tenantId") Long tenantId, @Param("list") List<DeviceEntity> list);

    Page<DeviceDto> pageDevice(IPage<DeviceDto> page,@Param(Constants.WRAPPER) Wrapper<DeviceDto> device);

    List<DeviceDto> listExportDevice(@Param(Constants.WRAPPER) Wrapper<DeviceDto> device);

    DeviceDto getDevice(@Param(Constants.WRAPPER) Wrapper<DeviceEntity> device);

    @Select("select d.id,d.name,d.status,d.tenant_id from device d where d.DELETED = 0 and d.tenant_id = #{tenantId}")
    List<DeviceBo> allList(@Param("tenantId") Long tenantId);

    void updateSyncStatusByLabelIdList(@Param("labelIdList") List<Long> labelIdList,@Param("syncStatus")Integer syncStatus);

    List<DeviceEntity> listDevice(@Param("tenantIsolation")TenantIsolation tenantIsolation,@Param("request") ListDcmpDeviceRequest request);

    List<DeviceChannelBo> listGatewayDevices(@Param("edgeGatewayId") Long edgeGatewayId,@Param("tenantId") Long tenantId);

    @Select({
            "<script>",
                "select ",
                    "d.* ",
                "from device d ",
                "where d.DELETED = 0",
                    "and d.tenant_id = #{tenantId}",
                    "and d.name in",
                        "<foreach collection='deviceNames' item='deviceName' open='(' separator=',' close=')'>",
                        "#{deviceName}",
                        "</foreach>",
            "</script>"
    })
    List<DeviceEntity> listDeviceByNames(@Param("tenantId")Long tenantId, @Param("deviceNames")List<String> deviceNames);

    @Select({
        "<script>",
            "select ",
                "d.id, d.name ",
            "from device d ",
            "where d.DELETED = 0",
                "and d.tenant_id in",
                    "<foreach collection='tenantIds' item='tenantId' open='(' separator=',' close=')'>",
                        "#{tenantId}",
                    "</foreach>",
                    "and d.name in",
                        "<foreach collection='deviceNames' item='deviceName' open='(' separator=',' close=')'>",
                        "#{deviceName}",
                        "</foreach>",
        "</script>"
    })
    List<DeviceEntity> listDeviceByNameTanentIds(@Param("tenantIds")Set<Long> tenantIds, @Param("deviceNames")List<String> deviceNames);

   @Select({
            "<script>",
                "select ",
                    "d.* ",
                "from device d ",
                "where d.DELETED = 0",
                    "and d.tenant_id = #{tenantId}",
                    "and d.name = #{deviceName}",
            "</script>"
    })
    DeviceEntity getDeviceByName(@Param("tenantId")Long tenantId, @Param("deviceName")String deviceName);

    @Select({
        "<script>",
            "select ",
                "d.* ",
            "from device d ",
            "where d.DELETED = 0",
                "and ",
                    "<foreach collection='tenantIdDeviceNames' item='tenantIdDeviceName' open='(' separator='OR' close=')'>",
                    "( d.name = #{tenantIdDeviceName.deviceName} AND d.tenant_id = #{tenantIdDeviceName.tenantId} )",
                    "</foreach>",
        "</script>"
    })
    List<DeviceEntity> listDeviceByTenantIdDeviceNames(@Param("tenantIdDeviceNames") List<TenantIdDeviceNameDto> tenantIdDeviceNames);


    @Select("update device set status = #{status},last_sync_time = #{lastSyncTime},sync_status = #{syncStatus} where tenant_id = #{tenantId} ")
    void updateSyncStatus(@Param("status")Integer status, @Param("lastSyncTime")LocalDateTime lastSyncTime,  @Param("syncStatus")Integer syncStatus,@Param("tenantId")Long tenantId);
}
