package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelDpo;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelSelectDpo;
import com.nti56.nlink.product.device.server.entity.ThingModelEntity;
import com.nti56.nlink.product.device.server.entity.ThingModelInheritEntity;
import com.nti56.nlink.product.device.server.entity.ThingServiceEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.model.*;
import com.nti56.nlink.product.device.server.model.inherit.ThingModelOfInherit;
import com.nti56.nlink.product.device.server.model.thingModel.dto.AppendThingModelByLabelIdsDTO;
import com.nti56.nlink.product.device.server.model.thingModel.dto.CreateThingModelByLabelGroupDTO;
import com.nti56.nlink.product.device.server.model.thingModel.dto.CreateThingModelByLabelIdsDTO;
import com.nti56.nlink.product.device.server.model.thingModel.vo.ThingModelVO;
import com.nti56.nlink.product.device.server.model.thingModel.vo.ThingModelValidRepeatVo;

import java.util.List;
import java.util.Set;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:12:02
 * @since JDK 1.8
 */
public interface IThingModelService extends IBaseService<ThingModelEntity> {

    Result<ThingModelEntity> createThingModel(TenantIsolation tenantIsolation, ThingModelInfoDto dto);

    Result<Boolean> deleteThingModel(TenantIsolation tenantIsolation, Long thingModelId);

    Result<Boolean> editThingModelModel(TenantIsolation tenantIsolation, ThingModelModelDto thingModelModel);

    Result<Boolean> editThingModelInfo(TenantIsolation tenantIsolation, ThingModelInfoDto thingModelInfo);

    Result<Page<ThingModelVo>> listThingModel(Page<ThingModelVo> page, TenantIsolation tenantIsolation, String searchStr, List<Long> tagIds);

    /**
     * 自定义部分的模型
     */
    Result<ThingModelDto> getThingModelById(TenantIsolation tenantIsolation, Long thingModelId);
    
    Result<List<ThingModelSimpleBo>> listInheritAvailable(TenantIsolation tenantIsolation, Long thingModelId);

    /**
     * 继承部分的模型
     * @param tenantIsolation
     */
    Result<ThingModelOfInherit> getInheritPartModel(TenantIsolation tenantIsolation, Long thingModelId);

    Result<List<ThingModelVO>> listByDeviceId(Long id);

    Result<List<ModelDpo>> getInheritModel(TenantIsolation tenant, List<Long> thingModelIdList);

    Result<ModelDpo> getModel4Service(TenantIsolation tenantIsolation, Long entityId);

    Result<Object> createThingModelByLabelIds(CreateThingModelByLabelIdsDTO dto, TenantIsolation tenantIsolation);

    Result<Object> createThingModelByLabelGroup(CreateThingModelByLabelGroupDTO dto, TenantIsolation tenantIsolation);

    Result<List<ThingModelEntity>> listDeviceInheritModel(Long deviceId, TenantIsolation tenantIsolation);

    Result<List<ThingModelEntity>> listInheritModel(Long thingModelId, TenantIsolation tenantIsolation);

    Result<ThingModelEntity> getThingModelEntity(TenantIsolation tenantIsolation, Long modelId);

    Result<Long> appendThingModelByLabelIds(AppendThingModelByLabelIdsDTO dto, TenantIsolation tenantIsolation);

    default Result<ModelSelectDpo> getModelSelectList(Long id, TenantIsolation tenantIsolation){
        Result<ModelDpo> deviceModel4Service = getModel4Service(tenantIsolation, id);
        if (deviceModel4Service.getSignal()) {
            return Result.ok(new ModelSelectDpo(deviceModel4Service.getResult()));
        }
        throw new BizException(deviceModel4Service.getMessage());
    }

    Result<List<ThingServiceEntity>> listModelServices(Long modelId,Long tenantId);

    Result listModelProperties(Long modelId,Long tenantId);

    Result<ThingModelValidRepeatVo> validRepeat(TenantIsolation tenantIsolation, ThingModelValidRepeatDto thingModelValidRepeatDto);

    Result<ThingTransmitModelDto> exportModel(TenantIsolation tenantIsolation, Long entityId);
    Result<ThingModelImportVo> importModel(TenantIsolation tenantIsolation, ThingTransmitModelDto thingTransmitModelDto);

    Result<ThingModelImportVo> coverImportModel(TenantIsolation tenantIsolation, ThingTransmitModelDto thingTransmitModelDto, Long entityId);

    Result<Long> copyThingModel(TenantIsolation tenantIsolation, Long sourceThingModelId);

    Result<Set<Long>> queryInheritByModelId(Long modelId,Long tenantId,List<ThingModelInheritEntity> thingModelInheritEntityList);

    void findParentIds(List<ThingModelInheritEntity> thingModelInheritEntityList, Long id, Set<Long> parentIds);

    void sinkSubscribe();
}
