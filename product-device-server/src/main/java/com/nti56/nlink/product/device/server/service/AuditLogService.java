package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.AuditLogEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nti56.nlink.product.device.server.entity.SubscriptionEntity;

/**
* <AUTHOR>
* @description 针对表【audit_log(操作审计日志表)】的数据库操作Service
* @createDate 2023-09-18 14:34:53
*/
public interface AuditLogService extends IService<AuditLogEntity> {

    Result<Page<AuditLogEntity>> getPage(AuditLogEntity entity, Page<AuditLogEntity> page);

    Result<AuditLogEntity> getLastSync();
}
