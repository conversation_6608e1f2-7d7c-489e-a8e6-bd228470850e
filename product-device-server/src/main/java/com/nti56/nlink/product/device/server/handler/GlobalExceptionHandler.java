package com.nti56.nlink.product.device.server.handler;

import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.exception.BizDetailException;
import com.nti56.nlink.product.device.server.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.stream.Collectors;

/**
 * 类说明: 全局异常处理<br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2021/8/24 11:11<br/>
 * @since JDK 1.8
 */
@Slf4j
@ControllerAdvice(basePackages = {"com.nti56.nlink.product.device.server.controller","com.nti56.nlink.product.device.server.openapi.controller"})
public class GlobalExceptionHandler {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public R exceptionHandler(MethodArgumentNotValidException e) {
        log.error("exceptionHandler error:{}",e.getMessage(),e);
        return R.error("参数校验失败：" + e.getBindingResult().getAllErrors().stream()
                .map(ObjectError::getDefaultMessage)
                .reduce((o1, o2) -> o1 + "；" + o2)
                .orElse(null));
    }

    @ExceptionHandler(BizDetailException.class)
    @ResponseBody
    public R bizDetailExceptionHandler(BizDetailException e) {
        log.error("bizDetailExceptionHandler error:{}",e.getMessage(),e);
        return R.error( e.getCode() ,  null)
            .put("messageList", e.getMessageList());
    }

    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseBody
    public R constraintViolationException(ConstraintViolationException exception) {
        log.error("constraintViolationException error:{}",exception.getMessage(),exception);
        String msg = exception.getConstraintViolations().stream().map(ConstraintViolation::getMessage).collect(Collectors.joining("；"));
        return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode(), "参数校验失败：" + msg);
    }


    @ExceptionHandler(BizException.class)
    @ResponseBody
    public R bizExceptionHandler(BizException e) {
        log.error("bizExceptionHandler error:{}",e.getMessage(),e);
        return R.error( e.getCode() ,  e.getMessage());
    }


    @ExceptionHandler(Exception.class)
    @ResponseBody
    public R exceptionHandler(Exception exception) {
        String msg = exception.getMessage();
        log.error("system error:{}",msg,exception);
        return R.error(ServiceCodeEnum.CODE_PARAM_ERROR.getCode(), msg);
    }

}