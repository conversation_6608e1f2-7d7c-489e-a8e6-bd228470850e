package com.nti56.nlink.product.device.server.proxy;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.device.AccessElm;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.MqttTopicEnum;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewaySpiProxy;
import com.nti56.nlink.product.device.server.model.ConnectResult;
import com.nti56.nlink.product.device.server.model.edgegateway.WriteParam;
import com.nti56.nlink.product.device.server.service.IEdgeGatewayService;
import com.nti56.nlink.product.device.server.util.MqttProxyEventBusUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 类说明: 边缘网关SPI代理 - EventBus实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-01-01 00:00:00
 * @since JDK 1.8
 */
@Component
@Slf4j
public class EdgeGatewaySpiProxy implements IEdgeGatewaySpiProxy {

    @Autowired
    private MqttProxyEventBusUtil mqttProxyEventBusUtil;
    
    @Autowired
    private IEdgeGatewayService edgeGatewayService;

    @Override
    public Result<List<ConnectResult>> connectLabel(Long edgeGatewayId, Long tenantId, 
                                                   List<AccessElm> labelList, 
                                                   List<ChannelElm> channelList) {
        try {
            // 检查网关是否在线
            Result<Boolean> onlineResult = edgeGatewayService.edgeGatewayOnline(tenantId, edgeGatewayId);
            if (!onlineResult.getResult()) {
                log.warn("网关离线，无法执行connectLabel操作 - tenantId: {}, edgeGatewayId: {}, labelCount: {}, channelCount: {}", 
                         tenantId, edgeGatewayId, 
                         labelList != null ? labelList.size() : 0, 
                         channelList != null ? channelList.size() : 0);
                return Result.error("网关离线，无法连接标签");
            }
            
            Map<String, Object> params = new HashMap<>();
            params.put("labelList", labelList);
            params.put("channelList", channelList);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "connectLabel", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, new TypeReference<Result<List<ConnectResult>>>() {});
        } catch (Exception e) {
            log.error("连接标签失败 - tenantId: {}, edgeGatewayId: {}, error: {}", 
                     tenantId, edgeGatewayId, e.getMessage(), e);
            return Result.error("连接标签失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<ConnectResult>> connectChannel(Long edgeGatewayId, Long tenantId, 
                                                     List<ChannelElm> channelList) {
        try {
            // 检查网关是否在线
            Result<Boolean> onlineResult = edgeGatewayService.edgeGatewayOnline(tenantId, edgeGatewayId);
            if (!onlineResult.getResult()) {
                log.warn("网关离线，无法执行connectChannel操作 - tenantId: {}, edgeGatewayId: {}, channelCount: {}", 
                         tenantId, edgeGatewayId, 
                         channelList != null ? channelList.size() : 0);
                return Result.error("网关离线，无法连接通道");
            }
            
            Map<String, Object> params = new HashMap<>();
            params.put("channelList", channelList);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "connectChannel", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, new TypeReference<Result<List<ConnectResult>>>() {});
        } catch (Exception e) {
            log.error("连接通道失败 - tenantId: {}, edgeGatewayId: {}, error: {}", 
                     tenantId, edgeGatewayId, e.getMessage(), e);
            return Result.error("连接通道失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> writeBool(Long edgeGatewayId, Long tenantId, AccessElm access, Boolean value) {
        try {
            // 检查网关是否在线
            Result<Boolean> onlineResult = edgeGatewayService.edgeGatewayOnline(tenantId, edgeGatewayId);
            if (!onlineResult.getResult()) {
                log.warn("网关离线，无法执行writeBool操作 - tenantId: {}, edgeGatewayId: {}, access: {}, value: {}", 
                         tenantId, edgeGatewayId, access, value);
                return Result.error("网关离线，无法执行写操作");
            }
            
            Map<String, Object> params = new HashMap<>();
            params.put("access", access);
            params.put("value", value);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "writeBool", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            log.error("写布尔值失败 - tenantId: {}, edgeGatewayId: {}, error: {}", 
                     tenantId, edgeGatewayId, e.getMessage(), e);
            return Result.error("写布尔值失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> writeByte(Long edgeGatewayId, Long tenantId, AccessElm access, Byte value) {
        try {
            // 检查网关是否在线
            Result<Boolean> onlineResult = edgeGatewayService.edgeGatewayOnline(tenantId, edgeGatewayId);
            if (!onlineResult.getResult()) {
                log.warn("网关离线，无法执行writeByte操作 - tenantId: {}, edgeGatewayId: {}, access: {}, value: {}", 
                         tenantId, edgeGatewayId, access, value);
                return Result.error("网关离线，无法执行写操作");
            }
            
            Map<String, Object> params = new HashMap<>();
            params.put("access", access);
            params.put("value", value);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "/writeByte", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            log.error("写字节值失败 - tenantId: {}, edgeGatewayId: {}, error: {}", 
                     tenantId, edgeGatewayId, e.getMessage(), e);
            return Result.error("写字节值失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> writeShort(Long edgeGatewayId, Long tenantId, AccessElm access, Short value) {
        try {
            // 检查网关是否在线
            Result<Boolean> onlineResult = edgeGatewayService.edgeGatewayOnline(tenantId, edgeGatewayId);
            if (!onlineResult.getResult()) {
                log.warn("网关离线，无法执行writeShort操作 - tenantId: {}, edgeGatewayId: {}, access: {}, value: {}", 
                         tenantId, edgeGatewayId, access, value);
                return Result.error("网关离线，无法执行写操作");
            }
            
            Map<String, Object> params = new HashMap<>();
            params.put("access", access);
            params.put("value", value);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "writeShort", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            log.error("写短整型值失败 - tenantId: {}, edgeGatewayId: {}, error: {}", 
                     tenantId, edgeGatewayId, e.getMessage(), e);
            return Result.error("写短整型值失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> writeInt(Long edgeGatewayId, Long tenantId, AccessElm access, Integer value) {
        try {
            // 检查网关是否在线
            Result<Boolean> onlineResult = edgeGatewayService.edgeGatewayOnline(tenantId, edgeGatewayId);
            if (!onlineResult.getResult()) {
                log.warn("网关离线，无法执行writeInt操作 - tenantId: {}, edgeGatewayId: {}, access: {}, value: {}", 
                         tenantId, edgeGatewayId, access, value);
                return Result.error("网关离线，无法执行写操作");
            }
            
            Map<String, Object> params = new HashMap<>();
            params.put("access", access);
            params.put("value", value);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "writeInt", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            log.error("写整型值失败 - tenantId: {}, edgeGatewayId: {}, error: {}", 
                     tenantId, edgeGatewayId, e.getMessage(), e);
            return Result.error("写整型值失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> writeLong(Long edgeGatewayId, Long tenantId, AccessElm access, Long value) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access", access);
            params.put("value", value);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "writeLong", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("写长整型值失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> writeFloat(Long edgeGatewayId, Long tenantId, AccessElm access, Float value) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access", access);
            params.put("value", value);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "writeFloat", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("写浮点值失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> writeDouble(Long edgeGatewayId, Long tenantId, AccessElm access, Double value) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access", access);
            params.put("value", value);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "writeDouble", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("写双精度值失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> writeString(Long edgeGatewayId, Long tenantId, AccessElm access, String value) {
        try {
            // 检查网关是否在线
            Result<Boolean> onlineResult = edgeGatewayService.edgeGatewayOnline(tenantId, edgeGatewayId);
            if (!onlineResult.getResult()) {
                log.warn("网关离线，无法执行writeString操作 - tenantId: {}, edgeGatewayId: {}, access: {}, value: {}", 
                         tenantId, edgeGatewayId, access, value);
                return Result.error("网关离线，无法执行写操作");
            }
            
            Map<String, Object> params = new HashMap<>();
            params.put("access", access);
            params.put("value", value);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "writeString", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            log.error("写字符串值失败 - tenantId: {}, edgeGatewayId: {}, error: {}", 
                     tenantId, edgeGatewayId, e.getMessage(), e);
            return Result.error("写字符串值失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> writeBoolArray(Long edgeGatewayId, Long tenantId, AccessElm access, Boolean[] value) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access", access);
            params.put("value", value);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "writeBoolArray", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("写布尔数组失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> writeShortArray(Long edgeGatewayId, Long tenantId, AccessElm access, Short[] value) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access", access);
            params.put("value", value);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "writeShortArray", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("写短整型数组失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> writeIntArray(Long edgeGatewayId, Long tenantId, AccessElm access, Integer[] value) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access", access);
            params.put("value", value);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "writeIntArray", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("写整型数组失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> writeLongArray(Long edgeGatewayId, Long tenantId, AccessElm access, Long[] value) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access", access);
            params.put("value", value);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "writeLongArray", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("写长整型数组失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> writeFloatArray(Long edgeGatewayId, Long tenantId, AccessElm access, Float[] value) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access", access);
            params.put("value", value);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "writeFloatArray", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("写浮点数组失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> writeDoubleArray(Long edgeGatewayId, Long tenantId, AccessElm access, Double[] value) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access", access);
            params.put("value", value);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "writeDoubleArray", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("写双精度数组失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> writeStringArray(Long edgeGatewayId, Long tenantId, AccessElm access, String[] value) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access", access);
            params.put("value", value);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "writeStringArray", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("写字符串数组失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> writeByteArray(Long edgeGatewayId, Long tenantId, AccessElm access, byte[] value) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("access", access);
            params.put("value", value);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "writeByteArray", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("写字节数组失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<ConnectResult>> multiWrite(Long edgeGatewayId, Long tenantId, List<WriteParam> params) {
        try {
            // 检查网关是否在线
            Result<Boolean> onlineResult = edgeGatewayService.edgeGatewayOnline(tenantId, edgeGatewayId);
            if (!onlineResult.getResult()) {
                log.warn("网关离线，无法执行multiWrite操作 - tenantId: {}, edgeGatewayId: {}, params: {}", 
                         tenantId, edgeGatewayId, params);
                return Result.error("网关离线，无法执行批量写操作");
            }

            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("params", params);
            String body = JSONObject.toJSONString(requestParams);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_SPI, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "multiWrite", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, new TypeReference<Result<List<ConnectResult>>>() {});
        } catch (Exception e) {
            log.error("批量写入失败 - tenantId: {}, edgeGatewayId: {}, error: {}", 
                     tenantId, edgeGatewayId, e.getMessage(), e);
            return Result.error("批量写入失败: " + e.getMessage());
        }
    }
}