package com.nti56.nlink.product.device.server.verticle.post.processor.event;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.condition.TriggerConditionElm;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.EventDpo;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.*;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpEventTopic;
import com.nti56.nlink.product.device.server.domain.thing.up.UpData;
import com.nti56.nlink.product.device.server.domain.thing.up.UpProp;
import com.nti56.nlink.product.device.server.model.device.dto.TriggerEventInstance;
import com.nti56.nlink.product.device.server.model.product.vo.DeviceVO;
import com.nti56.nlink.product.device.server.service.*;
import com.nti56.nlink.product.device.server.verticle.post.processor.PostProcessorHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-08-24 17:22:35
 * @since JDK 1.8
 */
@Component
@Slf4j
public class TriggerEventHandler extends PostProcessorHandler<GwUpEventTopic.TopicInfo> {

    @Autowired
    private IDeviceModelService deviceModelService;

    @Autowired
    private ISubscriptionService subscriptionService;

    @Async
    @Override
    public void process(GwUpEventTopic.TopicInfo topicInfo, UpData upData){
        super.process(topicInfo,upData);
    }

    @Override
    public void doProcess(GwUpEventTopic.TopicInfo topicInfo, UpData upData) {
        String eventType = topicInfo.getEventType();
        EventTypeEnum eventTypeEnum = EventTypeEnum.typeOfName(eventType);
        switch (eventTypeEnum){
            case TRIGGER:
                handleTriggerEvent(topicInfo,upData);
                break;
            default:
                //do nothing
        }
    }

    private void handleTriggerEvent(GwUpEventTopic.TopicInfo topicInfo, UpData upData) {
        log.debug("handleTriggerEvent: {}, {}", topicInfo, upData);
        Long deviceId = Long.valueOf(topicInfo.getDeviceId());
        Long tenantId = Long.valueOf(topicInfo.getTenantId());
        TenantIsolation tenantIsolation = new TenantIsolation();
        tenantIsolation.setTenantId(tenantId);
        Result<DeviceVO> deviceModelResult = deviceModelService.getDeviceModel(deviceId, tenantIsolation);
        if(!deviceModelResult.getSignal()){
            log.warn("cant find any device model.check whether device{} is exist first!",deviceId);
            return;
        }
        DeviceVO deviceInfo = deviceModelResult.getResult();

        String triggerCondition = buildCondition(upData.getProp(), deviceInfo.getFullModel().getEvents(), topicInfo.getEventName());

        TriggerEventInstance triggerEventInstance = new TriggerEventInstance();
        triggerEventInstance.setEventName(topicInfo.getEventName());
        triggerEventInstance.setTriggerCondition(triggerCondition);
        triggerEventInstance.setUpData(upData);
        subscriptionService.sendTriggerEventSubscription(deviceId, tenantId,  triggerEventInstance);

    }

    private String buildCondition(List<UpProp> prop, List<EventDpo> events, String occurEventName) {

        EventDpo occurEvent = null;
        for (int i = 0; i < events.size(); i++) {
            EventDpo eventDpo = events.get(i);
            if(occurEventName.equals(eventDpo.getName())){
                occurEvent = eventDpo;
                break;
            }
        }
        if(Objects.isNull(occurEvent)){
            return "";
        }
        try {
            Map<String, Object> collect = Maps.newHashMap();
            if(CollectionUtil.isNotEmpty(prop)){
                collect = prop.stream().collect(Collectors.toMap(UpProp::getProperty, UpProp::getValue, (key1, key2) -> key2));
            }
            List<TriggerConditionElm> triggerConditionElms = occurEvent.getEventDefine().getTrigger();
            if (CollectionUtil.isNotEmpty(triggerConditionElms)) {
                for (TriggerConditionElm triggerConditionElm : triggerConditionElms) {
                    triggerConditionElm.setValue(collect.get(triggerConditionElm.getLeft().getProperty()));
                }
            }
            return JSONUtil.toJsonStr(triggerConditionElms);
        } catch (Exception e) {
            return "";
        }
    }
}
