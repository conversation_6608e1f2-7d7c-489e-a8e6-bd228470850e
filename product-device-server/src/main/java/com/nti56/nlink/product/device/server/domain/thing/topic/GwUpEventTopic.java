package com.nti56.nlink.product.device.server.domain.thing.topic;

import com.nti56.nlink.product.device.server.domain.thing.enumerate.MqttTopicEnum;

import lombok.Builder;
import lombok.Data;

/**
 * 类说明: 网关上报事件topic
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 09:01:44
 * @since JDK 1.8
 */
public class GwUpEventTopic {
    
    @Data
    @Builder
    public static class TopicInfo{
        private String tenantId;
        private String edgeGatewayId;
        private String deviceId;
        private String eventType;
        private String eventName;
    }

    public static TopicInfo parseTopic(String topic){
        String[] split = topic.split("/");
        String tenantId = split[2];
        String edgeGatewayId = split[3];
        String deviceId = split[4];
        String eventType = split[7];
        String eventName = split[8];

        return TopicInfo.builder()
            .tenantId(tenantId)
            .edgeGatewayId(edgeGatewayId)
            .deviceId(deviceId)
            .eventType(eventType)
            .eventName(eventName)
            .build();
    }

    /**
     * 创建事件触发订阅topic
     */
    public static String createSubscribeTopic(String group){
        return "$share/" + group + "/" + MqttTopicEnum.GW_UP.getPrefix() 
                + "+/+/+/thing/events/+/#";
    }

    /**
     * 创建事件触发topic
     */
    public static String createTopic(Long tenantId, Long edgeGatewayId, Long deviceId, String eventType, String eventName){
        return MqttTopicEnum.GW_UP.getPrefix()
            + tenantId + "/"
            + edgeGatewayId + "/"
            + deviceId + "/"
            + "thing/events/"
            + eventType + "/"
            + eventName
            ;
    }
    
}
