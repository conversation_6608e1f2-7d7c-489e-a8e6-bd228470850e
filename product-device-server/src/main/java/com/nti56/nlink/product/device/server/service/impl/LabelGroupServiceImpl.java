package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.annotation.AuditLog;
import com.nti56.nlink.product.device.server.domain.thing.channel.Channel;
import com.nti56.nlink.product.device.server.domain.thing.label.LabelGroup;
import com.nti56.nlink.product.device.server.entity.AbsolutePathLabelEntity;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.entity.LabelEntity;
import com.nti56.nlink.product.device.server.entity.LabelGroupEntity;
import com.nti56.nlink.product.device.server.enums.ActionEnum;
import com.nti56.nlink.product.device.server.enums.AuditTargetEnum;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.AbsolutePathLabelMapper;
import com.nti56.nlink.product.device.server.mapper.LabelGroupMapper;
import com.nti56.nlink.product.device.server.mapper.LabelMapper;
import com.nti56.nlink.product.device.server.model.CountByIdDTO;
import com.nti56.nlink.product.device.server.model.LabelGroupDto;
import com.nti56.nlink.product.device.server.model.label.CopyLabelGroupReq;
import com.nti56.nlink.product.device.server.model.label.dto.LabelDTO;
import com.nti56.nlink.product.device.server.model.label.dto.LabelGroupDTO;
import com.nti56.nlink.product.device.server.model.label.dto.MoveOrCopyLabelGroupDTO;
import com.nti56.nlink.product.device.server.model.label.vo.LabelGroupVO;
import com.nti56.nlink.product.device.server.service.*;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-15 14:11:44
 * @since JDK 1.8
 */
@Service
@Slf4j
public class LabelGroupServiceImpl extends BaseServiceImpl<LabelGroupMapper, LabelGroupEntity> implements ILabelGroupService {
    
    @Autowired
    private LabelGroupMapper labelGroupMapper;

    @Autowired
    private ILabelService labelService;

    @Autowired
    private LabelMapper labelMapper;

    @Lazy
    @Autowired
    private IChannelService channelService;

    @Autowired
    private CommonFetcherFactory commonFetcherFactory;

    @Autowired
    private IEdgeGatewayService edgeGatewayService;

    @Override
    public Result<List<LabelGroupEntity>> listLabelGroupByChannelIds(List<Long> channelIds, TenantIsolation tenant) {
        if(channelIds == null || channelIds.size() <= 0){
            return Result.ok(new ArrayList<>());
        }
        List<LabelGroupEntity> list = new LambdaQueryChainWrapper<>(labelGroupMapper)
                .in(LabelGroupEntity::getChannelId, channelIds)
                .eq(LabelGroupEntity::getTenantId, tenant.getTenantId())
                .list();
        return Result.ok(list);
    }

    @Override
    public Result<List<LabelGroupDto>> listLabelGroupByChannelId(Long channelId, TenantIsolation tenantIsolation) {
        ChannelEntity channelEntity = channelService.getByIdAndTenantIsolation(channelId, tenantIsolation).getResult();
        if (channelEntity == null){
            throw new BizException("该租户下找不到该标签分组");
        }

        List<LabelGroupEntity> list = new LambdaQueryChainWrapper<>(labelGroupMapper)
                .eq(LabelGroupEntity::getChannelId,channelId)
                .eq(LabelGroupEntity::getTenantId, tenantIsolation.getTenantId())
                .list();
        List<LabelGroupDto> labelGroupDtos = BeanUtilsIntensifier.copyBeanList(list, LabelGroupDto.class);
        labelGroupDtos.forEach(labelGroupDto -> labelGroupDto.setChannelName(channelEntity.getName()));
        return Result.ok(labelGroupDtos);
    }

    @Override
    public Result<List<LabelGroupDto>> labelAndGroupTree(String name,Long channelId , TenantIsolation tenantIsolation,Boolean getAll){
        ChannelEntity channelEntity = channelService.getByIdAndTenantIsolation(channelId, tenantIsolation).getResult();
        if (channelEntity == null){
            throw new BizException("该租户下找不到该渠道");
        }

        List<LabelGroupDto> labelGroupDtos = labelGroupMapper.selectLabelGroupList(tenantIsolation.getTenantId(), name ,channelId,getAll);
        Optional.ofNullable(labelGroupDtos).isPresent();
        Iterator<LabelGroupDto> iterator = labelGroupDtos.iterator();
        while (iterator.hasNext()) {
            LabelGroupDto next = iterator.next();
            if (!Optional.ofNullable(next.getLabelDtoList()).isPresent() || next.getLabelDtoList().size() == 0) {
                iterator.remove();
            }
        }
        return Result.ok(labelGroupDtos);
    }

    @Override
    public Result<List<LabelGroupVO>> getByChannelId(Long id) {
        LambdaQueryWrapper<LabelGroupEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(LabelGroupEntity::getChannelId,id)
            .eq(LabelGroupEntity::getDeleted,0)
            .orderByDesc(LabelGroupEntity::getCreateTime);
        List<LabelGroupEntity> labelGroupList = labelGroupMapper.selectList(lqw);
        if(CollectionUtils.isEmpty(labelGroupList)){
            return Result.ok();
        }
        ArrayList<LabelGroupVO> labelGroupVOS = new ArrayList<>();
        for (LabelGroupEntity labelGroup : labelGroupList) {
            LabelGroupVO labelGroupVO = BeanUtilsIntensifier.copyBean(labelGroup, LabelGroupVO.class);
            labelGroupVO.setLabelList(labelService.listVOByLabelGroupId(labelGroup.getId()).getResult());
            labelGroupVOS.add(labelGroupVO);
        }
        return Result.ok(labelGroupVOS);
    }

    @Override
    public Result<Integer> countByChannelId(Long channelId) {
        LambdaQueryWrapper<LabelGroupEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(LabelGroupEntity::getChannelId,channelId);
        return Result.ok(labelGroupMapper.selectCount(lqw));
    }

    @Override
    public Result<Void> deleteByIdsAndTenantIsolation(List<Long> deleteLabelGroupIds, TenantIsolation tenantIsolation) {
        if (CollectionUtils.isEmpty(deleteLabelGroupIds)){
            return Result.ok();
        }

        Result<List<CountByIdDTO>> countByLabelGroupIdsResult = labelService.countByLabelGroupIds(tenantIsolation, deleteLabelGroupIds);
        List<CountByIdDTO> result = countByLabelGroupIdsResult.getResult();
        StringBuilder errorMsg = new StringBuilder();
        if (CollectionUtils.isNotEmpty(result)){
            for (CountByIdDTO countByIdDTO : result) {
                errorMsg.append(countByIdDTO.getName()).append(";\n");
            }
        }

        if (errorMsg.length() > 0){
            throw new BizException("以下标签分组含有未删除的标签，无法删除：\n"+errorMsg.toString());
        }

        LambdaQueryWrapper<LabelGroupEntity> lqw = new LambdaQueryWrapper<>();
        lqw.in(LabelGroupEntity::getId,deleteLabelGroupIds)
                .eq(LabelGroupEntity::getTenantId,tenantIsolation.getTenantId());
        labelGroupMapper.delete(lqw);
        return Result.ok();
    }


    @Transactional
    @Override
    @AuditLog(action = ActionEnum.CREATE,target = AuditTargetEnum.LABEL, details = "标签分组创建")
    public Result<LabelGroup> creatLabelGroup(Long channelId, String newLevelName, TenantIsolation tenantIsolation, LabelGroupDTO labelGroupDTO) {
        if (!RegexUtil.checkLabelGroupName(newLevelName).getSignal()) {
            throw new BizException(ServiceCodeEnum.LABEL_GROUP_NAME_ERROR);
        }
        String labelGroupName = StringUtils.isEmpty(labelGroupDTO.getName()) ? newLevelName : labelGroupDTO.getName() + LabelGroup.LABEL_GROUP_GRADE_SYMBOL + newLevelName;
        if (!RegexUtil.checkLabelGroupLevelName(labelGroupName).getSignal()) {
            throw new BizException(ServiceCodeEnum.LABEL_GROUP_NAME_ERROR);
        }
        labelGroupDTO.setName(labelGroupName);
        checkUniqueName(channelId,labelGroupName,tenantIsolation);
        LabelGroupEntity build = LabelGroupEntity.builder()
                .name(labelGroupName)
                .channelId(channelId)
                .descript(labelGroupDTO.getDescript())
                .build();
        labelGroupMapper.insert(build);
        this.setEdgeGatewayNotSyncByChannelId(channelId);
        Result<ChannelEntity> channelResult = channelService.getByIdAndTenantIsolation(channelId, tenantIsolation);
        return Result.ok(LabelGroup.builder()
                .id(build.getId())
                .channelId(channelId)
                .name(labelGroupName)
                .levelName(newLevelName)
                .level(labelGroupName.split(LabelGroup.LABEL_GROUP_SPLIT_SYMBOL).length -1)
                .descript(labelGroupDTO.getDescript())
                .driver(channelResult.getResult().getDriver())
                .build());
    }

    @Transactional
    @Override
    @AuditLog(action = ActionEnum.UPDATE,target = AuditTargetEnum.LABEL, details = "标签分组修改")
    public Result<LabelGroup> updateLabelGroup(String newLevelName, Long channelId, TenantIsolation tenantIsolation, LabelGroupDTO labelGroupDTO) {
        if (!Optional.ofNullable(labelGroupDTO.getName()).isPresent()) {
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        if (!RegexUtil.checkLabelGroupName(newLevelName).getSignal()) {
            throw new BizException(ServiceCodeEnum.LABEL_GROUP_NAME_ERROR);
        }
        String[] split = labelGroupDTO.getName().split(LabelGroup.LABEL_GROUP_SPLIT_SYMBOL);
        if (!newLevelName.equals(split[split.length - 1])) {
            checkUniqueName(channelId,LabelGroup.rename(labelGroupDTO.getName(),newLevelName),tenantIsolation);
        }
        labelGroupMapper.updateById(LabelGroupEntity.builder()
                .id(labelGroupDTO.getId())
                .descript(labelGroupDTO.getDescript()).build());
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());
        List<LabelGroupEntity> list = LabelGroup.updateLevelNameByPrefix(labelGroupDTO.getName(), newLevelName, channelId, commonFetcher);
        if (CollectionUtil.isEmpty(list)) {
            return Result.ok();
        }
        if (split.length == 1) {
            Set<LabelGroupEntity> collect = list.stream().filter(labelGroupEntity -> labelGroupEntity.getName().equals(newLevelName)).collect(Collectors.toSet());
            if (collect.size() <= 0) {
                labelGroupDTO.setName(null);
                creatLabelGroup(channelId,newLevelName,tenantIsolation,labelGroupDTO);
            }
        }
        labelGroupMapper.batchUpdate(list,tenantIsolation.getTenantId());
        List<LabelGroup> beans = LabelGroup.getBeans(list, commonFetcher, null);
        split[split.length - 1] = newLevelName;
        LabelGroup result = LabelGroup.getLevelBean(split, beans);
        if (result != null){
            Result<ChannelEntity> channelResult = channelService.getByIdAndTenantIsolation(channelId, tenantIsolation);
            result.setDriver(channelResult.getResult().getDriver());
        }
        if(!newLevelName.equals(labelGroupDTO.getName())){
            LabelGroupEntity labelGroupEntity = labelGroupMapper.selectById(labelGroupDTO.getId());
            Result<LabelGroup> labelGroupResult = LabelGroup.checkInfo(labelGroupEntity,commonFetcher);
            if(!labelGroupResult.getSignal()){
                return Result.error(labelGroupResult.getMessage());
            }
            labelBindRelationService.updateByLabelGroup(labelGroupResult.getResult(),tenantIsolation);
        }
        return Result.ok(result);
    }

    @Autowired
    ILabelBindRelationService labelBindRelationService;

    @Override
    @AuditLog(action = ActionEnum.UPDATE,target = AuditTargetEnum.LABEL, details = "按标签分组解绑标签")
    public Result<Integer> unbindByLabelGroup(Long channelId, TenantIsolation tenantIsolation, LabelGroupDTO labelGroupDTO) {
        List<AbsolutePathLabelEntity> absolutePathLabelEntities = getLabelIds(channelId, tenantIsolation , labelGroupDTO);
        if (CollectionUtil.isNotEmpty(absolutePathLabelEntities)) {
            List<String> labelGroupNames = BeanUtilsIntensifier.getSomething(absolutePathLabelEntities, AbsolutePathLabelEntity::getLabelGroupName);
            Set<String> set = new HashSet<>(labelGroupNames);
            labelBindRelationService.unbindByGroupNames(set,absolutePathLabelEntities.get(0).getEdgeGatewayId(),tenantIsolation.getTenantId(),absolutePathLabelEntities.get(0).getChannelName());
        }
        return Result.ok();
    }

    @Autowired
    AbsolutePathLabelMapper absolutePathLabelMapper;

    private List<AbsolutePathLabelEntity> getLabelIds(Long channelId, TenantIsolation tenantIsolation, LabelGroupDTO labelGroupDTO) {
        List<Long> labelGroupIds = LabelGroup.getLabelGroupIds(labelGroupDTO == null ? null : labelGroupDTO.getName(), channelId, commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId()));
        if (CollectionUtil.isNotEmpty(labelGroupIds)) {
            LambdaQueryWrapper<AbsolutePathLabelEntity> wrapper = new LambdaQueryWrapper<AbsolutePathLabelEntity>()
                    .in(AbsolutePathLabelEntity::getLabelGroupId, labelGroupIds);
            List<AbsolutePathLabelEntity> list = absolutePathLabelMapper.list(wrapper);
            return list;
        }
        return new ArrayList<>();
    }

    @Transactional
    @Override
    @AuditLog(action = ActionEnum.DELETE,target = AuditTargetEnum.LABEL, details = "标签分组删除")
    public Result<Void> delete(Long channelId, TenantIsolation tenantIsolation, LabelGroupDTO labelGroupDTO) {
        List<Long> labelGroupIds = LabelGroup.getLabelGroupIds(labelGroupDTO == null ? null : labelGroupDTO.getName(), channelId, commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId()));
        if (CollectionUtil.isEmpty(labelGroupIds)) {
            return Result.ok();
        }
        LambdaQueryWrapper<LabelEntity> wrapper = new LambdaQueryWrapper<LabelEntity>()
                .select(LabelEntity::getId)
                .eq(LabelEntity::getTenantId, tenantIsolation.getTenantId())
                .in(LabelEntity::getLabelGroupId, labelGroupIds);
        List<LabelEntity> list = labelMapper.selectList(wrapper);
        List<Long> labelIds = list.stream().map(LabelEntity::getId).collect(Collectors.toList());
        labelService.deleteByIdsAndTenantIsolation(labelIds, tenantIsolation);
        LambdaUpdateWrapper<LabelGroupEntity> in = new LambdaUpdateWrapper<LabelGroupEntity>()
                .eq(LabelGroupEntity::getTenantId, tenantIsolation.getTenantId())
                .in(LabelGroupEntity::getId, labelGroupIds);
        labelGroupMapper.delete(in);
        this.setEdgeGatewayNotSyncByChannelId(channelId);
        return Result.ok();
    }

    @Override
    public Result<Void> setEdgeGatewayNotSyncByChannelId(Long channelId) {
        Result<ChannelEntity> channelResult = channelService.getChannelById(channelId);
        if (channelResult.getSignal() && channelResult.getResult() != null ){
            Long edgeGatewayId = channelResult.getResult().getEdgeGatewayId();
            if (edgeGatewayId != null){
                edgeGatewayService.setNotSyncById(edgeGatewayId);
            }
        }
        return Result.ok();
    }

    @Override
    public Result<List<LabelGroup>> getLabelGroupTree(Long channelId, TenantIsolation tenantIsolation, String searchName) {
        List<LabelGroup> labelGroups = Channel.getWithLabelGroup(channelId, commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId()), searchName);
        if (CollectionUtils.isNotEmpty(labelGroups)){
            Result<ChannelEntity> channelResult = channelService.getByIdAndTenantIsolation(channelId, tenantIsolation);
            for (LabelGroup labelGroup : labelGroups) {
                labelGroup.setDriver(channelResult.getResult().getDriver());
            }
        }
        return Result.ok(labelGroups);
    }

    @Override
    public Result<LabelGroupEntity> getByIdAndTenantIsolation(Long labelGroupId, TenantIsolation tenant) {
        LambdaQueryWrapper<LabelGroupEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(LabelGroupEntity::getId, labelGroupId)
                .eq(LabelGroupEntity::getTenantId, tenant.getTenantId());
        return Result.ok(labelGroupMapper.selectOne(lqw));
    }

    @Override
    @AuditLog(action = ActionEnum.CREATE,target = AuditTargetEnum.LABEL, details = "批量标签分组创建")
    public Result<Void> batchAdd(Long channelId, List<LabelGroup> addGroups, TenantIsolation tenantIsolation) {
        log.info("批量添加标签分组，channelId:{},addGroups:{}",channelId,addGroups);
        addGroups.forEach(addGroup -> {
            LabelGroupEntity entity = LabelGroupEntity.builder()
                    .name(addGroup.getName())
                    .channelId(channelId)
                    .build();
            labelGroupMapper.insert(entity);
            addGroup.setId(entity.getId());
        });
        return Result.ok();
    }

    @Override
    @AuditLog(action = ActionEnum.EXPORT,target = AuditTargetEnum.LABEL, details = "通道批量标签导出")
    public List<LabelDTO> exportLabels(Long channelId, TenantIsolation tenantIsolation, String name) {
        List<LabelDTO> labels = LabelGroup.getLabelsByPrefix(name, channelId, commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId()));
        return labels;
    }

    @Override
    @Transactional
    @AuditLog(action = ActionEnum.UPDATE,target = AuditTargetEnum.LABEL, details = "移动或复制并移动标签分组")
    public Result<Void> moveOrCopyLabelGroup(MoveOrCopyLabelGroupDTO dto, TenantIsolation tenantIsolation) {
        if (dto.getType().equals(1) && dto.getSourceLabelGroupId().equals(dto.getTargetLabelGroupId())){
            throw new BizException("不能移动到同一个分组");
        }

        LabelGroupEntity sourceLabelGroup = this.getByIdAndTenantIsolation(dto.getSourceLabelGroupId(), tenantIsolation).getResult();
        if (sourceLabelGroup == null){
            throw new BizException("参数异常");
        }
        
        String sourceName = sourceLabelGroup.getName();
        String newPrefix = "";
        if (dto.getTargetLabelGroupId() != null){
            LabelGroupEntity targetLabelGroup = this.getByIdAndTenantIsolation(dto.getTargetLabelGroupId(), tenantIsolation).getResult();
            if (targetLabelGroup == null){
                throw new BizException("参数异常");
            }
            if (dto.getType().equals(1)
                    && targetLabelGroup.getChannelId().equals(sourceLabelGroup.getChannelId())
                    && targetLabelGroup.getName().length() >= (sourceName.length()+2)
                    && targetLabelGroup.getName().substring(0,sourceName.length()+1).equals(sourceName+".")){
                throw new BizException("无法操作到子组");
            }

            newPrefix = targetLabelGroup.getName()+".";
        }

        Result<ChannelEntity> byLabelGroupId = channelService.getByLabelGroupId(dto.getSourceLabelGroupId());
        if (!byLabelGroupId.getSignal() || byLabelGroupId.getResult() == null){
            throw new BizException("参数异常");
        }
        Result<ChannelEntity> byIdAndTenantIsolation = channelService.getByIdAndTenantIsolation(dto.getTargetChannelId(), tenantIsolation);
        if (!byIdAndTenantIsolation.getSignal() || byIdAndTenantIsolation.getResult() == null){
            throw new BizException("参数异常");
        }

        if (!byIdAndTenantIsolation.getResult().getDriver().equals(byLabelGroupId.getResult().getDriver())){
            throw new BizException("通道类型不同，操作失败");
        }

        String oldPrefix = sourceName.substring(0, sourceName.lastIndexOf(".")+1);
        String name = newPrefix + sourceName.replaceFirst(oldPrefix, "");
        this.checkUniqueName(dto.getTargetChannelId(),name,tenantIsolation);
        if (dto.getType().equals(1)){
            sourceLabelGroup.setName(name);
            sourceLabelGroup.setChannelId(dto.getTargetChannelId());
            labelGroupMapper.updateById(sourceLabelGroup);
            labelGroupMapper.moveLabelGroup(newPrefix,oldPrefix,dto.getTargetChannelId(),sourceName+".",byLabelGroupId.getResult().getId());
            edgeGatewayService.setNotSyncById(byLabelGroupId.getResult().getEdgeGatewayId());

            CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());
            Result<LabelGroup> labelGroupResult = LabelGroup.checkInfo(sourceLabelGroup,commonFetcher);
            labelBindRelationService.clearLabelGroupBindRelation(labelGroupResult.getResult(),tenantIsolation);
        }else if (dto.getType().equals(2)){
            List<LabelGroupEntity> labelGroupEntityList = labelGroupMapper.listCopyLabelGroup(sourceName + ".", byLabelGroupId.getResult().getId());
            if (CollectionUtils.isEmpty(labelGroupEntityList)){
                labelGroupEntityList = new ArrayList<>();
            }
            labelGroupEntityList.add(sourceLabelGroup);
            for (LabelGroupEntity labelGroupEntity : labelGroupEntityList) {
                Long oldId = labelGroupEntity.getId();
                labelGroupEntity.setId(null);
                labelGroupEntity.setName(newPrefix+labelGroupEntity.getName().replaceFirst(oldPrefix,""));
                labelGroupEntity.setChannelId(dto.getTargetChannelId());
                labelGroupMapper.insert(labelGroupEntity);
                List<LabelEntity> labelEntities = labelService.listByLabelGroupId(oldId, tenantIsolation);
                if (CollectionUtils.isNotEmpty(labelEntities)){
                    for (LabelEntity labelEntity : labelEntities) {
                        labelEntity.setId(null);
                        labelEntity.setGatherParam(null);
                        labelEntity.setLabelGroupId(labelGroupEntity.getId());
                    }
                    labelService.saveBatch(labelEntities);
                }
            }
        }
        edgeGatewayService.setNotSyncById(byIdAndTenantIsolation.getResult().getEdgeGatewayId());
        return Result.ok();
    }

    @Override
    @Transactional
    @AuditLog(action = ActionEnum.CREATE,target = AuditTargetEnum.LABEL, details = "复制标签分组")
    public Result<Void> copyLabelGroup(CopyLabelGroupReq req, TenantIsolation tenantIsolation) {
        LabelGroupEntity source = labelGroupMapper.getById(tenantIsolation.getTenantId(), req.getSourceLabelGroupId());
        if (ObjectUtil.isEmpty(source)) {
            throw new BizException("源标签分组不存在！");
        }
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());

        List<Long> labelGroupIds = LabelGroup.getLabelGroupIds(source.getName(), source.getChannelId(), commonFetcher);
        if (CollectionUtil.isEmpty(labelGroupIds)) {
            throw new BizException("源标签分组不存在！");
        }
        List<LabelDTO> labelDTOS = labelMapper.labelWithFullName(labelGroupIds, tenantIsolation);
        if (CollectionUtil.isEmpty(labelDTOS)) {
            throw new BizException("源标签分组下无标签，不支持复制！");
        }
        int index = source.getName().lastIndexOf(".");
        labelDTOS.forEach(labelDTO -> labelDTO.setName(labelDTO.getName().substring(index + 1)));
        Map<Long, String> sourceLabelNames = BeanUtilsIntensifier.collection2Map(labelDTOS, LabelDTO::getSourceId, LabelDTO::getName);
        if (CollectionUtil.isNotEmpty(req.getTargetLabelGroupIds())) {
            List<LabelGroupEntity> list = new LambdaQueryChainWrapper<>(labelGroupMapper)
                    .in(LabelGroupEntity::getId, req.getTargetLabelGroupIds())
                    .eq(LabelGroupEntity::getTenantId, tenantIsolation.getTenantId())
                    .list();
            if (CollectionUtil.isNotEmpty(list)) {
                list.forEach(labelGroupEntity -> {
                    labelDTOS.forEach(labelDTO -> labelDTO.setName(
                            new StringBuilder(labelGroupEntity.getName())
                                    .append(".")
                                    .append(sourceLabelNames.get(labelDTO.getSourceId()))
                                    .toString())
                    );
                    labelService.labelBatchInput(tenantIsolation, labelGroupEntity.getChannelId(), labelDTOS);
                });
            }
        }
        if (CollectionUtil.isNotEmpty(req.getTargetChannelIds())) {
            req.getTargetChannelIds().forEach(channelId -> {
                labelDTOS.forEach(labelDTO -> labelDTO.setName(sourceLabelNames.get(labelDTO.getSourceId())));
                labelService.labelBatchInput(tenantIsolation,channelId,labelDTOS);
            });
        }
        return Result.ok();
    }


    private void checkUniqueName(Long channelId,String name, TenantIsolation tenantIsolation){
        Result<Void> result = uniqueName(channelId, name, tenantIsolation);
        if (!result.getSignal()) {
            throw new BizException(ServiceCodeEnum.LABEL_GROUP_NAME_REPEAT);
        }
    }

    private Result<Void> uniqueName(Long channelId,String name, TenantIsolation tenantIsolation) {
        return this.uniqueName(null,channelId, name, tenantIsolation);
    }

    /**
     * 判断名称是否唯一
     *
     * @param id
     * @param name
     * @param tenantIsolation
     * @return
     */
    private Result<Void> uniqueName(Long id, Long channelId,String name, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<LabelGroupEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(id != null, LabelGroupEntity::getId, id)
                .eq(LabelGroupEntity::getName, name)
                .eq(LabelGroupEntity::getChannelId,channelId)
                .eq(LabelGroupEntity::getTenantId, tenantIsolation.getTenantId());

        if (labelGroupMapper.selectCount(lqw) > 0) {
            return Result.error("已经存在该名称的标签分组，名称："+name);
        }

        return Result.ok();
    }


}
