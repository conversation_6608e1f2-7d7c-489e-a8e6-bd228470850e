package com.nti56.nlink.product.device.server.service.export.handler;

import java.util.List;

import cn.hutool.extra.spring.SpringUtil;

import com.nti56.nlink.product.device.server.constant.Constant;
import org.apache.commons.collections.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.nti56.nlink.common.export.handler.AbstractExportSqlHandler;
import com.nti56.nlink.common.util.GeneratorSqlUtil;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.mapper.*;

/**
 * 类说明:物<br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/19 15:34<br/>
 * @version 1.0
 * @since JDK 1.8
 */
public class ThingExportSqlHandler extends AbstractExportSqlHandler {
  @Override
  public void exportSqlDml(Long tenantId, List<String> sqlList) {
    exportThingModel(tenantId, sqlList);
    exportThingModelIn(tenantId, sqlList);
    exportThingService(tenantId, sqlList);
    exportSubscription(tenantId,sqlList);
    if (getNext() != null) {
      getNext().exportSqlDml(tenantId, sqlList);
    }
  }

  private void exportThingModel(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<ThingModelEntity> lqw = new LambdaQueryWrapper<>();
    lqw.and(w->w.eq(ThingModelEntity::getTenantId,tenantId)).or()
            .eq(ThingModelEntity::getTenantId, Constant.DEFAULT_THING);
    List<ThingModelEntity> dtoList = SpringUtil.getBean(ThingModelMapper.class).selectList(lqw);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(ThingModelEntity.class, dtoList));
    }
  }

  private void exportThingModelIn(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<ThingModelInheritEntity> queryWrapper = new LambdaQueryWrapper<ThingModelInheritEntity>()
            .eq(ThingModelInheritEntity::getTenantId, tenantId);
    List<ThingModelInheritEntity> dtoList = SpringUtil.getBean(ThingModelInheritMapper.class).selectList(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(ThingModelInheritEntity.class, dtoList));
    }
  }

  private void exportThingService(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<ThingServiceEntity> queryWrapper = new LambdaQueryWrapper<ThingServiceEntity>()
            .eq(ThingServiceEntity::getTenantId, tenantId);
    List<ThingServiceEntity> dtoList = SpringUtil.getBean(ThingServiceMapper.class).selectList(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(ThingServiceEntity.class, dtoList));
    }
  }

  private void exportSubscription(Long tenantId, List<String> sqlList) {
    LambdaQueryWrapper<SubscriptionEntity> queryWrapper = new LambdaQueryWrapper<SubscriptionEntity>()
            .eq(SubscriptionEntity::getTenantId, tenantId);
    List<SubscriptionEntity> dtoList = SpringUtil.getBean(SubscriptionMapper.class).selectList(queryWrapper);
    if (CollectionUtils.isNotEmpty(dtoList)) {
      sqlList.addAll(GeneratorSqlUtil.getInsertSql(SubscriptionEntity.class, dtoList));
    }
  }

}
