package com.nti56.nlink.product.device.server.domain.thing.topic;

import com.nti56.nlink.product.device.server.domain.thing.enumerate.MqttTopicEnum;

import lombok.Builder;
import lombok.Data;

/**
 * 类说明: 网关同步事件topic
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-08-11 08:33:52
 * @since JDK 1.8
 */
public class GwSyncTopic {
    
    @Data
    @Builder
    public static class TopicInfo{
        private Long tenantId;
        private Long edgeGatewayId;
    }

    public static TopicInfo parseTopic(String topic){
        String[] split = topic.split("/");
        String tenantId = split[2];
        String edgeGatewayId = split[3];

        return TopicInfo.builder()
            .tenantId(Long.parseLong(tenantId))
            .edgeGatewayId(Long.parseLong(edgeGatewayId))
            .build();
    }

    /**
     * 创建事件触发订阅topic
     */
    public static String createSubscribeTopic(String group){
        return "$share/" + group + "/" + MqttTopicEnum.GW_SYNC.getPrefix() 
                + "+/+";
    }

}
