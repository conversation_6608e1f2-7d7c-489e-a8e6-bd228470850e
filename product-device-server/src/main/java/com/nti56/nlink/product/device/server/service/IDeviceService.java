package com.nti56.nlink.product.device.server.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.device.Device;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DeviceStatusEnum;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.model.*;
import com.nti56.nlink.product.device.server.model.device.dto.CreateByAssembleInfoDTO;
import com.nti56.nlink.product.device.server.model.device.dto.DeviceFaultStatistic;
import com.nti56.nlink.product.device.server.model.device.vo.AssembleByLabelIdGroupIdsVO;
import com.nti56.nlink.product.device.server.model.device.vo.DcmpDeviceVO;
import com.nti56.nlink.product.device.server.model.device.vo.DeviceOnlineStatusVO;
import com.nti56.nlink.product.device.server.model.deviceModel.DeviceModelBo;
import com.nti56.nlink.product.device.server.model.deviceModel.dto.AssembleByLabelGroupIdsDTO;
import com.nti56.nlink.product.device.server.openapi.domain.request.DoServiceTaskRequest;
import com.nti56.nlink.product.device.server.openapi.domain.request.ListDcmpDeviceRequest;
import com.nti56.nlink.product.device.server.openapi.domain.request.ListDevicesRequest;
import com.nti56.nlink.product.device.server.openapi.domain.request.ListTagRequest;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:11:44
 * @since JDK 1.8
 */
public interface IDeviceService extends IBaseService<DeviceEntity> {

    Result<DeviceDto> createDevice(DeviceDto device);

    Result<Page<DeviceDto>> getDevicePage(@Nullable DeviceDto device, Page<DeviceDto> page);

    void exportDevice(DeviceDto device, HttpServletResponse response);

    Result<List<DeviceEntity>> listDevice(DeviceEntity device);

    Result<List<DeviceEntity>> listDeviceByName( TenantIsolation tenantIsolation, ListDevicesRequest request);

    Result<Void> updateDevice(DeviceDto device);

    Result<Void> deleteDeviceById(TenantIsolation tenantIsolation, Long deviceId);

    @Transactional
    Result<Void> deleteDevice(TenantIsolation tenantIsolation, DeviceEntity entity);

    Result<DeviceDto> getDevice(DeviceEntity device);

    Result<List<DeviceBo>> listDeviceByTag(DeviceDto device);

    Result<DeviceModelBo> getDeviceProperties(Long tenantId, Long deviceId);

    DeviceEntity getDeviceByName(Long tenantId, String name);

    Result<Integer> countByEdgeGatewayId(Long edgeGatewayId);

    Result<List<EventVo>> getDeviceEventProperties(Long tenantId,List<EventVo> eventVos);

    List<DeviceEntity> getDeviceRuntimeList(Long tenantId);

    Result<Void> setNotSyncById(Long id);

    R doServiceTask(Long tenantId, Long deviceId, String serviceName, Map<String, Object> input, DeviceServiceLogEntity logEntity,Boolean isRecordLog);

    R doServiceTaskWithoutContext(Long tenantId, Long deviceId, String serviceName, Map<String, Object> input, DeviceServiceLogEntity logEntity);

    void notifyDeviceSyncByThingModelId(Long thingModelId, Long tenantId, CommonFetcher commonFetcher);

    Result<Long> createByLabelIds(TenantIsolation tenant, BatchLabelCreateDeviceParam param);

    Result<Void> setNotSyncByLabelIdList(List<Long> labelIdList);

    Result<Void> setNotSyncByIds(Set<Long> deviceIds);

    Result<List<DeviceRespondBo>> deviceBatchDelete(TenantIsolation tenantIsolation, List<Long> ids);

    Result<AssembleByLabelIdGroupIdsVO> assembleByLabelGroupIds(AssembleByLabelGroupIdsDTO dto, TenantIsolation tenant);

    Result<Object> createByAssembleInfo(CreateByAssembleInfoDTO dto, TenantIsolation tenant);

    /**
     * 获取租户下设备告故障信息
     * @param tenantId
     * @return
     */
    Result<DeviceFaultStatistic> getDeviceFaultData(Long tenantId);

    default Result<List<DeviceEntity>> listDeviceByIds(Long tenantId, List<Long> deviceIds){
        return listDeviceByIds(tenantId,deviceIds,null);
    }

    Result<List<DeviceEntity>> listDeviceByIds(Long tenantId, List<Long> deviceIds, SFunction<DeviceEntity, ?>... functions);

    Result<List<DeviceEntity>> listDeviceByNames(Long tenantId, List<String> deviceNames);

    Result<List<DcmpDeviceVO>> listBasicDeviceForDcmp(TenantIsolation tenantIsolation,ListDcmpDeviceRequest request);

    Result<List<DeviceChannelBo>> listGatewayDevices(Long edgeGatewayId,TenantIsolation tenantIsolation);
    
    Result<Void> deviceStatusProcess(DeviceEntity deviceEntity, List<ChangeNoticeEntity> noticeEntityList, Boolean gwOnline, Map<String,Boolean> channelStatusMap, Map<Object,Object> deviceStatusMap, Map<Object, Object> deviceOfflineTimesMap, String key, Map<Long, List<LabelBindRelationEntity>> labelBindRelationEntityMap, Integer type);

    List<Long> getDeviceIdsByTenantId(Long tenantId, DeviceTwinRequestBo request);

    Result<List<DeviceOnlineStatusVO>> listDeviceOnlineStatus(GateWayDeviceListDTO dto ,TenantIsolation tenantIsolation);

    Map<Object, Object> getDeviceStatusMap(Long tenantId,Long edgeGatewayId);

    void updateDeviceName(DeviceEntity deviceEntity);

    void deviceBatchStatusChangeNotify(TenantIsolation tenant, List<Long> deviceIds, DeviceStatusEnum deviceStatusEnum);

    Result<List<DeviceRespondBo>> deleteDeviceBatch(TenantIsolation tenantIsolation, List<DeviceEntity> deviceEntities, Map<Long, String> gwNameMap);
    
    Result<List<DeviceEntity>> getAllDeviceRunList();

    void initDeviceTwinData(Device deviceRun, DeviceEntity device, TenantIsolation tenant);
    
    Result<List<Long>> getAllDeviceIdList(Long tenantId);
    
    Result<Boolean>  writePropertyValue(WritePropertyValueDTO dto, TenantIsolation tenantIsolation);

    void notifyDeviceSyncByThingModelIds(Set<Long> thingModelIds, Long tenantId, CommonFetcher commonFetcher);

    Result<List<DeviceEntity>> listByInheritModelId(Long modelId,Long tenantId);

    Result<List<DeviceServiceEntity>> listDeviceService(TenantIsolation tenantIsolation, Long deviceId);

    Result<List<Long>> deviceNamesToIds(Long tenantId, DeviceNamesToIdsDto dto);

    Result<Map<Long, String>> deviceIdNameMapByNames(Long tenantId, List<String> deviceNames);

    Result<Void> jobExecute(List<DoServiceTaskRequest> doServiceTaskRequests, TenantIsolation tenantIsolation);

    Result<Set<Long>> deviceModelInherits(Long deviceId,Long tenantId);

    Result<List<DeviceEntity>> listDeviceByTag(Long tenantId, List<ListTagRequest> requestList);

    Result<Void> batchTags(Long tenantId, BatchDeviceTagDto batchDeviceTagDto);

    Result<Void> ayncBatchUpdateDevice(Collection<DeviceEntity> deviceEntities,TenantIsolation tenant);
}
