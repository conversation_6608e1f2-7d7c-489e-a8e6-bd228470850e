package com.nti56.nlink.product.device.server.feign;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.GatherParamField;
import com.nti56.nlink.product.device.client.model.dto.json.CustomDriverRuntimeInfoField;
import com.nti56.nlink.product.device.server.model.ComputeTaskBo;
import com.nti56.nlink.product.device.server.model.datasync.SyncEdgeGatewayDto;
import com.nti56.nlink.product.device.server.model.edgegateway.vo.HardwareInfo;

import java.util.List;

public interface IEdgeGatewayControlProxy {

    Result<Void> stopProxy(Long edgeGatewayId, Long tenantId);
    
    Result<Void> startProxy(Long edgeGatewayId, Long tenantId);
    
    Result<Void> syncGatherTask(Long edgeGatewayId,
                                Long tenantId,
                                List<GatherParamField> gatherParamList,
                                List<ChannelRuntimeInfoField> channelRuntimeInfoList);
    
    Result<Void> syncCustomDriver(Long edgeGatewayId,
                                Long tenantId,
                                List<CustomDriverRuntimeInfoField> customDriverRuntimeInfoList);
                            
    Result<Void> syncComputeTask(Long edgeGatewayId,
                                Long tenantId,
                                List<ComputeTaskBo> computeTaskList);

    Result<Void> disconnectOt(Long edgeGatewayId,
                              Long tenantId,
                              String heartbeatUuid);
    
    Result<String> updateLogView(Long edgeGatewayId, Long tenantId);
    
    Result<Void> downloadUpgradePackage(Long edgeGatewayId, Long tenantId, String url, String upgradeVersion, String md5Proofread, String instance);
    
    Result<Void> executeUpgrade(Long edgeGatewayId, Long tenantId, String upgradeVersion, String instance);
    
    Result<String> otSyncData(Long edgeGatewayId, Long tenantId, SyncEdgeGatewayDto syncEdgeGatewayDto);

    Result<SyncEdgeGatewayDto> pullConfig(Long edgeGatewayId, Long tenantId);

    Result<HardwareInfo> hardwareInfo(Long edgeGatewayId, Long tenantId);
}
