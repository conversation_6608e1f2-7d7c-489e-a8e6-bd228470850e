package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.DeviceRuntimeMetadataField;
import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ResourceTypeEnum;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.TagBindRelationEntity;
import com.nti56.nlink.product.device.server.scriptApi.Device;
import com.nti56.nlink.product.device.server.service.*;
import com.nti56.nlink.product.device.server.service.cache.redis2Mem.MemoryCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName ThingResourceServiceImpl
 * @date 2022/4/21 14:53
 * @Version 1.0
 */
@Service
@Slf4j
public class ThingResourceServiceImpl implements IThingResourceService {

    @Autowired
    IResourceRelationService resourceService;

    @Autowired
    ITagBindRelationService tagBindRelationService;

    @Autowired
    @Lazy
    IDeviceService deviceService;

    @Lazy
    @Autowired
    IChannelService channelService;


    /**
     * @param tenantId
     * @return
     */
    @Override
    public List<Device> getTenantDevices(Long tenantId) {
        //db查询两次，一次是通过租户找设备列表（过滤状态），一次的查询租户下的通道（过滤在线状态）
        Result<List<Device>> devices = getDevices(tenantId, deviceService::getDeviceRuntimeList, tenantId);
        log.debug("getTenantDevices:{},{}",tenantId,BeanUtilsIntensifier.getIds(devices.getResult(), Device::getId));
        return devices.getResult();
    }


    @Override
    public List<Long> getByTag(Long tenantId,String key, String value) {
        List<TagRsp> tags = new ArrayList<>();// TODO: 按标记名称获取设备 tagBindRelationService.getTags(tenantId, key, value);
        List<String> tagIds = tags.stream().map(tagRsp -> String.valueOf(tagRsp.getId())).collect(Collectors.toList());
        Result<List<TagBindRelationEntity>> listResult = tagBindRelationService.listByTagIds(tenantId, tagIds, ResourceTypeEnum.DEVICE);
        if (listResult.getSignal() && CollectionUtil.isNotEmpty(listResult.getResult())) {
            return listResult.getResult().stream().map(TagBindRelationEntity::getTargetId).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<Long> getByTagId(Long tenantId,String tagId) {
        Result<List<TagBindRelationEntity>> listResult = tagBindRelationService.listByTagIds(tenantId, Collections.singletonList(tagId), ResourceTypeEnum.DEVICE);
        if (listResult.getSignal() && CollectionUtil.isNotEmpty(listResult.getResult())) {
            return listResult.getResult().stream().map(TagBindRelationEntity::getTargetId).collect(Collectors.toList());
        }
        return null;
    }


    private <T> Result<List<Device>> getDevices(Long tenantId, Function<T,List<DeviceEntity>> getDevicesFunc,T t){
        List<DeviceEntity> deviceEntityList = getDevicesFunc.apply(t);
        List<Device> devices = new ArrayList<>();
        Result<List<ChannelEntity>> listResult = channelService.listChannelByTenantId(tenantId, true);
        Map<Long,ChannelRuntimeInfoField> channelRuntimes = new HashMap<>();
        if (listResult.getSignal()) {
            channelRuntimes = BeanUtilsIntensifier.collection2Map(listResult.getResult(), ChannelEntity::getId, ChannelEntity::getRuntimeInfo);
        }
        Map<Long, ChannelRuntimeInfoField> finalChannelRuntimes = channelRuntimes;
        Optional.ofNullable(deviceEntityList).orElse(new ArrayList<>()).forEach(deviceEntity -> {
            //todo  一台设备的资源id，对应运行时的数据，对应租户下所有现在通道<通道id，通道运行时信息>？？
            DeviceRuntimeMetadataField runtimeMetaData=MemoryCache.getDeviceRuntimeMetadataField(deviceEntity.getId());
            if(runtimeMetaData==null){
                log.warn("设备运行时不存在 设备id：{}",deviceEntity.getId());
                return;
            }
            Result<Device> instance = Device.getInstance(deviceEntity.getResourceId(), runtimeMetaData, finalChannelRuntimes);
            if (instance.getSignal()) {
                devices.add(instance.getResult());
            }
        });
        return Result.ok(devices);
    }
}
