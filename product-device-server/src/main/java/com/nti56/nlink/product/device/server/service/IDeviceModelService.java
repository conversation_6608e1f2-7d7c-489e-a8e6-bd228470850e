package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelDpo;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelSelectDpo;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.model.deviceLog.PropertyLogConditionBo;
import com.nti56.nlink.product.device.server.model.product.dto.EditDeviceModelDTO;
import com.nti56.nlink.product.device.server.model.product.vo.DeviceVO;

import java.util.List;
import java.util.Set;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:11:58
 * @since JDK 1.8
 */
public interface IDeviceModelService {

    Result<DeviceVO> getDeviceModel(Long id, TenantIsolation tenantIsolation);

    Result<Void> editDeviceModel(EditDeviceModelDTO dto, TenantIsolation tenantIsolation);

    Result<ModelDpo> getDeviceModel4Service(Long id, TenantIsolation tenantIsolation);

    default Result<ModelSelectDpo> getDeviceModelSelectList(Long id, TenantIsolation tenantIsolation){
        Result<ModelDpo> deviceModel4Service = getDeviceModel4Service(id, tenantIsolation);
        if (deviceModel4Service.getSignal()) {
            return Result.ok(new ModelSelectDpo(deviceModel4Service.getResult()));
        }
        throw new BizException(deviceModel4Service.getMessage());
    }

    Result<Set<String>> getPropertiesByDeviceIdsAndType(Long tenantId, PropertyLogConditionBo condition);

    Result<Set<ModelDpo>> listModelByDeviceIds(List<Long> deviceIds, TenantIsolation tenantIsolation);

    Result<Set<ModelDpo>> listModelByDeviceNames(List<String> deviceNames, TenantIsolation tenantIsolation);
}
