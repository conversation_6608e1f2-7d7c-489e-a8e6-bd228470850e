package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.fetcher.Preloader;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.CustomDriverRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.GatherParamField;
import com.nti56.nlink.product.device.client.model.dto.json.compute.ComputeEventItem;
import com.nti56.nlink.product.device.client.model.dto.json.compute.PropDataElm;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.WarningLevelEnum;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.FaultLevelDefineElm;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.channel.Channel;
import com.nti56.nlink.product.device.server.domain.thing.custom.CustomDriver;
import com.nti56.nlink.product.device.server.domain.thing.edgegateway.EdgeGateway;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ChannelStatusEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.YesNoEnum;
import com.nti56.nlink.product.device.server.domain.thing.label.Label;
import com.nti56.nlink.product.device.server.domain.thing.label.LabelGroup;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Event;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.FaultLevelDefine;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.FaultRedisCache;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.FaultStatus;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpEventTopic;
import com.nti56.nlink.product.device.server.domain.thing.up.UpData;
import com.nti56.nlink.product.device.server.domain.thing.up.UpProp;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewayControlProxy;
import com.nti56.nlink.product.device.server.mapper.*;
import com.nti56.nlink.product.device.server.model.ComputeTaskBo;
import com.nti56.nlink.product.device.server.model.device.dto.FaultEventInstance;
import com.nti56.nlink.product.device.server.model.thingModel.dto.TriggerEventLevelDTO;
import com.nti56.nlink.product.device.server.service.ITaskService;
import com.nti56.nlink.product.device.server.util.RedisLockUtil;
import com.nti56.nlink.product.device.server.verticle.post.processor.event.EventUpData2InfluxDBHandler;
import com.nti56.nlink.product.device.server.verticle.post.processor.event.FaultEventHandler;
import com.nti56.nlink.product.device.server.verticle.post.processor.event.TriggerEventHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 类说明: 任务服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:10:42
 * @since JDK 1.8
 */
@Service
@Slf4j
public class TaskServiceImpl extends BaseServiceImpl<ComputeTaskMapper, ComputeTaskEntity> implements ITaskService {

    @Autowired
    DeviceMapper deviceMapper;
    @Autowired
    RedisLockUtil redisLockUtil;

    @Autowired
    DeviceGroupMapper deviceGroupMapper;

    @Autowired
    ThingModelMapper thingModelMapper;

    @Autowired
    DeviceModelMapper deviceModelMapper;

    @Autowired
    LabelMapper labelMapper;

    @Autowired
    ChannelMapper channelMapper;

    @Autowired
    @Lazy
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    @Lazy
    private RedisTemplate redisTemplate;
    @Autowired
    EdgeGatewayMapper edgeGatewayMapper;

    @Autowired
    ComputeTaskMapper computeTaskMapper;

    @Autowired
    LabelGroupMapper labelGroupMapper;

    @Autowired
    ThingModelInheritMapper thingModelInheritMapper;

    @Autowired
    ThingServiceMapper thingServiceMapper;

    @Autowired
    DataModelMapper dataModelMapper;

    @Autowired
    DataModelPropertyMapper dataModelPropertyMapper;

    @Autowired
    LabelBindRelationMapper labelBindRelationMapper;

    @Autowired
    DeviceModelInheritMapper deviceModelInheritMapper;

    @Autowired
    DeviceServiceMapper deviceServiceMapper;

    @Autowired
    CommonFetcherFactory commonFetcherFactory;


    @Autowired
    IEdgeGatewayControlProxy edgeGatewayControlProxy;

    @Autowired
    CustomDriverMapper customDriverMapper;


    private Map<Long, ComputeTaskEntity> computeTaskMap = new HashMap<>();
    @Value("${fault.expireTime:20}")
    private Integer expireTime;

    /**
     * 网关调用部分
     */
    @Override
    public Result<List<GatherParamField>> listGatherParam(TenantIsolation tenant, Long edgeGatewayId) {
        log.info("listGatherParam edgeGatewayId: {}, tenant: {}", edgeGatewayId, tenant);
        List<GatherParamField> list = labelMapper.listGatherParam(
                tenant.getTenantId(), edgeGatewayId
        );
        log.info("listGatherParam size:{}", list.size());
        return Result.ok(list, "查询成功");
    }

    @Override
    public Result<List<ChannelRuntimeInfoField>> listChannelRuntimeInfo(TenantIsolation tenant, Long edgeGatewayId) {
        log.info("listChannelRuntimeInfo edgeGatewayId: {}, tenant: {}", edgeGatewayId, tenant);
        List<ChannelRuntimeInfoField> list = channelMapper.listRuntimeInfo(tenant.getTenantId(), edgeGatewayId);
        log.info("listChannelRuntimeInfo size:{}", list.size());
        return Result.ok(list, "查询成功");
    }

    @Override
    public Result<List<ComputeTaskBo>> listComputeTask(TenantIsolation tenant, Long edgeGatewayId) {
        log.info("listComputeTask edgeGatewayId: {}, tenant: {}", edgeGatewayId, tenant);
        List<ComputeTaskBo> list = computeTaskMapper.listComputeTask(
                tenant.getTenantId(), edgeGatewayId
        );
        log.info("listComputeTask size:{}", list.size());
        return Result.ok(list, "查询成功");
    }

    /**
     * 下发同步命令
     */
    @Override
    public Result<Void> syncAllTask(TenantIsolation tenant) {
        log.info("syncAllTask: {}", tenant.getTenantId());
        List<EdgeGatewayEntity> list = edgeGatewayMapper.listAll(tenant.getTenantId());
        for (EdgeGatewayEntity item : list) {
            syncTask(tenant, item.getId());
        }
        return Result.ok();
    }

    @Override
    public Result<Void> syncCustomDriver(TenantIsolation tenant, Long edgeGatewayId) {
        log.info("syncCustomDriver edgeGatewayId: {}, tenant: {}", edgeGatewayId, tenant);

        {
            // 同步自定义协议
            List<CustomDriverRuntimeInfoField> customDriverRuntimeInfoList = customDriverMapper.listAllEnabledRuntimeInfo(tenant.getTenantId());
            Result<Void> r = edgeGatewayControlProxy.syncCustomDriver(edgeGatewayId, tenant.getTenantId(), customDriverRuntimeInfoList);
            if (!r.getSignal()) {
                return Result.error(r.getMessage());
            }
        }

        log.info("syncCustomDriver edgeGatewayId end");
        return Result.ok();
    }

    private void syncing(long tenantId, Long edgeGatewayId) {
        stringRedisTemplate.opsForValue().set(
                RedisConstant.SYNCING_PREFIX + tenantId + "_" + edgeGatewayId, "syncing", RedisConstant.SYNCING_TTL, TimeUnit.SECONDS
        );
    }

    @Override
    public Result<Void> syncTask(TenantIsolation tenant, Long edgeGatewayId) {
        log.info("syncTask edgeGatewayId: {}, tenant: {}", edgeGatewayId, tenant);
        syncing(tenant.getTenantId(), edgeGatewayId);
        {
            // 同步自定义协议
            List<CustomDriverRuntimeInfoField> customDriverRuntimeInfoList = customDriverMapper.listAllEnabledRuntimeInfo(tenant.getTenantId());
            Result<Void> r = edgeGatewayControlProxy.syncCustomDriver(edgeGatewayId, tenant.getTenantId(), customDriverRuntimeInfoList);
            if (!r.getSignal()) {
                return Result.error("1.同步自定义协议失败，" + r.getMessage());
            }
        }

        {
            // 同步计算任务
            Result<List<ComputeTaskBo>> result = listComputeTask(tenant, edgeGatewayId);
            if (!result.getSignal()) {
                return Result.error("5.创建计算任务失败，" + result.getMessage());
            }
            List<ComputeTaskBo> computeTaskList = result.getResult();
            Result<Void> r = edgeGatewayControlProxy.syncComputeTask(edgeGatewayId, tenant.getTenantId(), computeTaskList);
            if (!r.getSignal()) {
                return Result.error("6.同步计算任务失败，" + r.getMessage());
            }
        }

        {
            // 同步采集任务
            Result<List<GatherParamField>> result = listGatherParam(tenant, edgeGatewayId);
            if (!result.getSignal()) {
                return Result.error("2.创建采集任务参数失败，" + result.getMessage());
            }
            Result<List<ChannelRuntimeInfoField>> result2 = listChannelRuntimeInfo(tenant, edgeGatewayId);
            if (!result2.getSignal()) {
                return Result.error("3.创建采集任务通道失败，" + result2.getMessage());
            }

            List<GatherParamField> gatherParamList = result.getResult();
            List<ChannelRuntimeInfoField> channelRuntimeInfoList = result2.getResult();

            Result<Void> r = edgeGatewayControlProxy.syncGatherTask(edgeGatewayId, tenant.getTenantId(), gatherParamList, channelRuntimeInfoList);
            if (!r.getSignal()) {
                return Result.error("4.同步采集任务失败，" + r.getMessage());
            }

        }


        log.info("syncTask edgeGatewayId end");
        return Result.ok();
    }


    /**
     * 通道信息部分
     */
    @Override
    @Transactional
    public Result<Integer> updateChannelRuntimeInfoByEdgeGatewayId(Long tenantId, Long edgeGatewayId) {
        log.info("updateChannelRuntimeInfoByEdgeGatewayId edgeGatewayId: {}, tenantId: {}", edgeGatewayId, tenantId);
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantId);

        EdgeGatewayEntity edgeGatewayEntity = edgeGatewayMapper.getById(tenantId, edgeGatewayId);

        Result<EdgeGateway> edgeGatewayResult = EdgeGateway.checkInfo(edgeGatewayEntity, commonFetcher);
        if (!edgeGatewayResult.getSignal()) {
            throw new BizException(edgeGatewayResult.getMessage());
        }

        List<Long> channelIds = channelMapper.listIdByEdgeGatewayId(tenantId, edgeGatewayId);
        Integer sum = 0;
        if (channelIds != null) {
            for (Long channelId : channelIds) {
                Integer count = updateChannelRuntimeInfoByChannelId(tenantId, channelId, commonFetcher);
                sum += count;
            }
        }
        return Result.ok(sum);
    }

    @Override
    @Transactional
    public Result<Integer> updateChannelRuntimeInfoByChannelId(Long tenantId, Long channelId) {
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantId);
        Integer count = updateChannelRuntimeInfoByChannelId(tenantId, channelId, commonFetcher);
        return Result.ok(count);
    }

    private Integer updateChannelRuntimeInfoByChannelId(Long tenantId, Long channelId, CommonFetcher commonFetcher) {
        ChannelEntity channelEntity = channelMapper.getById(tenantId, channelId);
        Result<Channel> channelResult = Channel.checkInfo(channelEntity, commonFetcher);
        if (!channelResult.getSignal()) {
            throw new BizException(channelResult.getMessage());
        }
        Channel channel = channelResult.getResult();
        ChannelRuntimeInfoField runtimeInfo = channel.createRuntimeInfo();
        Integer count = channelMapper.updateRuntimeInfo(tenantId, channelId, runtimeInfo);
        return count;
    }

    /**
     * 计算任务部分
     */
    @Override
    public Result<Void> disableDeviceComputeTask(TenantIsolation tenant, Long deviceId) {
        log.info("disableDeviceComputeTask deviceId: {}, tenant: {}", deviceId, tenant);
        ComputeTaskEntity build = ComputeTaskEntity.builder().deviceId(deviceId).tenantId(tenant.getTenantId()).build();
        int delete = computeTaskMapper.delete(new LambdaUpdateWrapper<>(build));
        return Result.ok();
    }

    @Override
    public Result<Void> enableDeviceComputeTask(TenantIsolation tenant, Long deviceId) {
        log.info("enableDeviceComputeTask deviceId: {}, tenant: {}", deviceId, tenant);
        Integer integer = computeTaskMapper.enableDeviceComputeTask(tenant.getTenantId(), deviceId);
        return Result.ok();
    }

//    @CachePut(value = "deviceComputeTask", key = "#deviceId", unless = "#result == null")
    @Transactional
    @Override
    public ComputeTaskEntity updateComputeTask(Long tenantId, Long deviceId, ComputeTaskEntity task) {
        log.debug("updateComputeTask deviceId: {}, tenantId: {}", deviceId, tenantId);
        //删除设备的计算任务
        computeTaskMapper.deleteByDeviceId(tenantId, deviceId);

        //插入计算任务
        task.setTenantId(tenantId);
        computeTaskMapper.insert(task);
        computeTaskMap.put(deviceId, task);
        return task;
    }


    

    @Transactional
    @Override
    public boolean batchUpdateComputeTask(Long tenantId, Map<Long,ComputeTaskEntity> deviceTaskMap) {
        //删除设备的计算任务
        computeTaskMapper.batchDeleteByDeviceIds(tenantId, deviceTaskMap.keySet());

        //插入计算任务
        deviceTaskMap.values().forEach(task -> {
            task.setTenantId(tenantId);
        });
        computeTaskMap.putAll(deviceTaskMap);
        return this.saveBatch(deviceTaskMap.values());
    }

    /**
     * 采集任务部分
     */
    private GatherParamField updateGatherParamByLabelId(Long tenantId, Long labelId, LabelEntity labelEntity, CommonFetcher commonFetcher) {

        Result<Label> labelResult = Label.checkInfoToEdgeGateway(labelEntity, commonFetcher);
        if (!labelResult.getSignal()) {
            throw new BizException(labelResult.getMessage());
        }
        Label label = labelResult.getResult();

        GatherParamField gatherParam = label.createGatherParam();
        return gatherParam;
    }

    @Override
    @Transactional
    public Result<GatherParamField> updateGatherParamByLabelId(Long tenantId, Long labelId) {
        log.info("updateGatherParamByLabelId labelId: {}, tenantId: {}", labelId, tenantId);

        CommonFetcher CommonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);
        LabelEntity labelEntity = labelMapper.getById(tenantId, labelId);
        GatherParamField gatherParam = updateGatherParamByLabelId(tenantId, labelId, labelEntity, CommonFetcher);

        return Result.ok(gatherParam);
    }

    private List<GatherParamField> updateGatherParamByLabelGroupId(Long tenantId, Long labelGroupId, LabelGroupEntity labelGroupEntity, CommonFetcher commonFetcher) {

        Result<LabelGroup> labelGroupResult = LabelGroup.checkInfo(labelGroupEntity, commonFetcher);
        if (!labelGroupResult.getSignal()) {
            throw new BizException(labelGroupResult.getMessage());
        }

        Preloader<LabelEntity> labelPreload = commonFetcher.preloader(LabelEntity.class).preload("label_group_id", new ArrayList<Long>() {
            {
                add(labelGroupId);
            }
        });
        List<LabelEntity> labelList = labelPreload.list();
        List<GatherParamField> gatherParamList = new ArrayList<>();
        if (labelList != null) {
            for (LabelEntity labelEntity : labelList) {
                GatherParamField gatherParam = updateGatherParamByLabelId(tenantId, labelEntity.getId(), labelEntity, commonFetcher);
                gatherParamList.add(gatherParam);
            }
        }
        return gatherParamList;
    }

    @Override
    @Transactional
    public Result<List<GatherParamField>> updateGatherParamByLabelGroupId(Long tenantId, Long labelGroupId) {
        log.info("updateGatherParamByLabelGroupId labelGroupId: {}, tenantId: {}", labelGroupId, tenantId);

        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);
        LabelGroupEntity labelGroupEntity = labelGroupMapper.getById(tenantId, labelGroupId);
        List<GatherParamField> gatherParamList = updateGatherParamByLabelGroupId(tenantId, labelGroupId, labelGroupEntity, commonFetcher);
        return Result.ok(gatherParamList);
    }

    private List<GatherParamField> updateGatherParamByChannelId(Long tenantId, Long channelId, ChannelEntity channelEntity, CommonFetcher commonFetcher) {

        Result<Channel> channelResult = Channel.checkInfo(channelEntity, commonFetcher);
        if (!channelResult.getSignal()) {
            throw new BizException(channelResult.getMessage());
        }

        Preloader<LabelGroupEntity> labelGroupPreload = commonFetcher.preloader(LabelGroupEntity.class).preload("channel_id", new ArrayList<Long>() {
            {
                add(channelId);
            }
        });
        List<LabelGroupEntity> labelGroupList = labelGroupPreload.list();
        List<GatherParamField> gatherParamList = new ArrayList<>();
        if (labelGroupList != null) {
            for (LabelGroupEntity labelGroupEntity : labelGroupList) {
                List<GatherParamField> subGatherParamList = updateGatherParamByLabelGroupId(tenantId, labelGroupEntity.getId(), labelGroupEntity, commonFetcher);
                gatherParamList.addAll(subGatherParamList);
            }
        }
        return gatherParamList;
    }

    @Override
    @Transactional
    public Result<List<GatherParamField>> updateGatherParamByChannelId(Long tenantId, Long channelId) {
        log.info("updateGatherParamByChannelId channelId: {}, tenantId: {}", channelId, tenantId);

        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);
        ChannelEntity channelEntity = channelMapper.getById(tenantId, channelId);
        if (!ChannelStatusEnum.ONLINE.getValue().equals(channelEntity.getStatus())) {
            return Result.error("通道已关闭");
        }
        List<GatherParamField> gatherParamList = updateGatherParamByChannelId(tenantId, channelId, channelEntity, commonFetcher);
        return Result.ok(gatherParamList);
    }

    private List<GatherParamField> updateGatherParamByEdgeGatewayId(Long tenantId, Long edgeGatewayId, EdgeGatewayEntity edgeGatewayEntity, CommonFetcher commonFetcher) {

        Result<EdgeGateway> edgeGatewayResult = EdgeGateway.checkInfo(edgeGatewayEntity, commonFetcher);
        if (!edgeGatewayResult.getSignal()) {
            throw new BizException(edgeGatewayResult.getMessage());
        }

        Preloader<ChannelEntity> channelPreload = commonFetcher.preloader(ChannelEntity.class).preload("edge_gateway_id", new ArrayList<Long>() {
            {
                add(edgeGatewayId);
            }
        });
        List<ChannelEntity> channelEntityList = channelPreload.list();
        List<GatherParamField> gatherParamList = new ArrayList<>();
        if (channelEntityList != null) {
            for (ChannelEntity channelEntity : channelEntityList) {
                if (!ChannelStatusEnum.ONLINE.getValue().equals(channelEntity.getStatus())) {
                    continue;
                }
                List<GatherParamField> subGatherParamList = updateGatherParamByChannelId(tenantId, channelEntity.getId(), channelEntity, commonFetcher);
                gatherParamList.addAll(subGatherParamList);
            }
        }
        return gatherParamList;
    }

    @Override
    @Transactional
    public Result<List<GatherParamField>> updateGatherParamByEdgeGatewayId(Long tenantId, Long edgeGatewayId) {
        log.info("updateGatherParamByEdgeGatewayId edgeGatewayId: {}, tenantId: {}", edgeGatewayId, tenantId);

        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);
        EdgeGatewayEntity edgeGatewayEntity = edgeGatewayMapper.getById(tenantId, edgeGatewayId);
        List<GatherParamField> gatherParamList = updateGatherParamByEdgeGatewayId(tenantId, edgeGatewayId, edgeGatewayEntity, commonFetcher);

        return Result.ok(gatherParamList);
    }

    @Override
    public Result<List<GatherParamField>> updateGatherParamByLabelIds(Long tenantId, List<Long> labelIds, CommonFetcher commonFetcher) {
        log.info("updateGatherParamByLabelIds labelIds: {}, tenantId: {}", labelIds, tenantId);

        Preloader<LabelEntity> labelPreload = commonFetcher.preloader(LabelEntity.class).preload("id", labelIds);
        List<LabelEntity> labelEntityList = labelPreload.list();
        List<GatherParamField> gatherParamList = new ArrayList<>();
        if (labelEntityList != null) {
            for (LabelEntity labelEntity : labelEntityList) {
                GatherParamField gatherParam = updateGatherParamByLabelId(tenantId, labelEntity.getId(), labelEntity, commonFetcher);
                gatherParamList.add(gatherParam);
            }
        }
        return Result.ok(gatherParamList);
    }

    @Override
    @Transactional
    public Result<Void> updateCustomDriverRuntimeInfoById(Long tenantId, Long customDriverId) {
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);
        CustomDriverEntity entity = customDriverMapper.getById(tenantId, customDriverId);
        if (entity == null) {
            return Result.ok();
        }
        YesNoEnum status = YesNoEnum.typeOfValue(entity.getStatus());
        if (status != null && YesNoEnum.YES.equals(status)) {
            Result<CustomDriver> result = CustomDriver.checkInfoToMessage(entity, commonFetcher);
            if (!result.getSignal()) {
                throw new BizException(result.getMessage());
            }
            CustomDriver customDriver = result.getResult();
            CustomDriverRuntimeInfoField runtimeInfo = customDriver.toRuntimeInfo();

            Integer c = customDriverMapper.updateRuntimeInfo(tenantId, entity.getId(), runtimeInfo);
        }
        return Result.ok();
    }

    @Override
    @Transactional
    public Result<Integer> updateCustomDriverRuntimeInfo(Long tenantId) {
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);
        List<CustomDriverEntity> list = customDriverMapper.listAllEnabled(tenantId);
        Integer count = 0;
        if (list != null && list.size() > 0) {
            for (CustomDriverEntity entity : list) {
                Result<CustomDriver> result = CustomDriver.checkInfoToMessage(entity, commonFetcher);
                if (!result.getSignal()) {
                    throw new BizException(result.getMessage());
                }
                CustomDriver customDriver = result.getResult();
                CustomDriverRuntimeInfoField runtimeInfo = customDriver.toRuntimeInfo();

                Integer c = customDriverMapper.updateRuntimeInfo(tenantId, entity.getId(), runtimeInfo);
                count++;
            }

        }
        return Result.ok(count);
    }

    @Override
    public Result<Void> batchDisableDeviceComputeTask(TenantIsolation tenant, List<Long> deviceIds) {
        log.debug("disableDeviceComputeTask deviceIds: {}, tenant: {}", deviceIds, tenant);
        if (CollectionUtil.isNotEmpty(deviceIds)) {
            LambdaQueryWrapper<ComputeTaskEntity> wrapper = new LambdaQueryWrapper<ComputeTaskEntity>().eq(ComputeTaskEntity::getTenantId, tenant.getTenantId()).in(ComputeTaskEntity::getDeviceId, deviceIds);
            computeTaskMapper.delete(wrapper);
        }
        return Result.ok();
    }

    @Override
    public void batchenableDeviceComputeTask(TenantIsolation tenant, List<Long> deviceIds) {
        log.debug("enableDeviceComputeTask deviceIds: {}, tenant: {}", deviceIds, tenant);
        if (CollectionUtil.isNotEmpty(deviceIds)) {
            computeTaskMapper.batchEnableDeviceComputeTask(tenant.getTenantId(), deviceIds);
        }
    }

    @Override
//    @Cacheable(value = "deviceComputeTask", key = "#deviceId", unless ="#result == null")
    public ComputeTaskEntity getComputeTaskByDeviceId(Long deviceId) {
        ComputeTaskEntity computeTaskEntity = computeTaskMap.get(deviceId);
        if (computeTaskEntity != null) {
            return computeTaskEntity;
        }
        try {
            List<ComputeTaskEntity> list = new LambdaQueryChainWrapper<>(computeTaskMapper).eq(ComputeTaskEntity::getDeviceId, deviceId).list();
            if (CollectionUtil.isEmpty(list)) {
                return null;
            } else {
                computeTaskMap.put(deviceId, list.get(0));
                return list.get(0);
            }
        } catch (Exception e) {
            return null;
        }
    }

    @Autowired
    EventUpData2InfluxDBHandler eventUpData2InfluxDBHandler;

    @Autowired
    FaultEventHandler faultEventHandler;

    @Autowired
    TriggerEventHandler triggerEventHandler;

    //    @Async
    @Override
    public void executeTask(ComputeTaskEntity task, Map<String, Object> upProps, Map<String, Object> actual, Long timestamp) {
        List<ComputeEventItem> faultList = task.getContent().getFaultList();
        List<ComputeEventItem> triggerList = task.getContent().getTriggerList();
        Map<String, Object> allMap = new HashMap<>();
        allMap.putAll(actual);
        allMap.putAll(upProps);
        if (CollectionUtil.isNotEmpty(triggerList)) {
            triggerList.forEach(computeEventItem -> {
                if (Collections.disjoint(computeEventItem.getConditionProperties(), upProps.keySet())) {
                    return;
                }
                if (Event.doEvalTrigger(computeEventItem.getTrigger(), allMap)) {
                    GwUpEventTopic.TopicInfo topicInfo = builTopicInfo(task, computeEventItem);
                    UpData build = UpData.builder().timestamp(timestamp).prop(extractProperties(computeEventItem.getDeviceId(), allMap, computeEventItem.getProperties(), null)).build();
                    //trigger事件入库
                    eventUpData2InfluxDBHandler.process(topicInfo, build);
                    //trigger事件触发订阅
                    triggerEventHandler.process(topicInfo, build);
                }
            });
        }
        if (CollectionUtil.isNotEmpty(faultList)) {
            faultList.forEach(computeEventItem -> {
                if (Collections.disjoint(computeEventItem.getConditionProperties(), upProps.keySet())) {
                    return;
                }
                GwUpEventTopic.TopicInfo topicInfo = builTopicInfo(task, computeEventItem);
                doFault(topicInfo, computeEventItem, allMap, timestamp);
            });
        }

    }

    private GwUpEventTopic.TopicInfo builTopicInfo(ComputeTaskEntity task, ComputeEventItem computeEventItem) {
        GwUpEventTopic.TopicInfo topicInfo = GwUpEventTopic.TopicInfo.builder().eventName(computeEventItem.getEventName()).deviceId(computeEventItem.getDeviceId().toString()).edgeGatewayId(task.getEdgeGatewayId().toString()).tenantId(task.getTenantId().toString()).eventType(computeEventItem.getType()).build();
        return topicInfo;
    }

    @Override
    public void endFault(GwUpEventTopic.TopicInfo topicInfo, String deviceName,Integer level) {
        DeviceEntity device = deviceMapper.getDeviceByName(Long.parseLong(topicInfo.getTenantId()), deviceName);
        if (device == null) {
            return;
        }
        List<UpProp> data = new ArrayList<>();
        Long timestamp = System.currentTimeMillis();
        Long deviceId = device.getId();
        topicInfo.setDeviceId(deviceId.toString());
        String eventName = topicInfo.getEventName();
        String faultStateKey = String.format(RedisConstant.DEVICE_FAULT_EVENT_STATUS, deviceId, eventName);
        FaultStatus faultState = JSONUtil.toBean(stringRedisTemplate.opsForValue().get(faultStateKey), FaultStatus.class);
        if (faultState == null) {
            faultState = new FaultStatus();
        }
        if (faultState.getStatus()) {
            //切换成停止状态并归零故障计数器
            if (level == null) {
                faultState.endAllFault();
                //上报故障结束信息
                faultProcess(topicInfo, timestamp, faultState, data,WarningLevelEnum.FATAL_WARNING.getCode());
            }else{
                faultState.clearLevelTriggerCount(level);
                String faultEventInstanceKey = String.format(RedisConstant.DEVICE_FAULT_EVENT_INSTANCE, topicInfo.getTenantId(), deviceId);
                //删除事件缓存，用于下一次事件触发时，isDiffLevel判断可以通过，否则取上一次的事件等级，如果与当前触发的等级一致则不会计数
                FaultEventInstance cacheEvents = JSONUtil.toBean(JSONUtil.toJsonStr(redisTemplate.opsForHash().get(faultEventInstanceKey, topicInfo.getEventName())), FaultEventInstance.class);
                cacheEvents.setFaultLevel(0);
                redisTemplate.opsForHash().put(faultEventInstanceKey, topicInfo.getEventName(), JSONUtil.toJsonStr(cacheEvents));
            }
            stringRedisTemplate.opsForValue().set(faultStateKey, JSONUtil.toJsonStr(faultState));
        }
    }


    private void doFault(GwUpEventTopic.TopicInfo topicInfo, ComputeEventItem item, Map<String, Object> map, Long timestamp) {
        //1、评估触发条件，判断当前故障是否触发
        Boolean triggered = Event.doEvalTrigger(item.getTrigger(), map);
        //获取设备ID
        Long deviceId = Long.valueOf(topicInfo.getDeviceId());

        //2、提取故障相关的属性数据
        List<UpProp> data = extractProperties(deviceId, map, item.getProperties(), item.getFaultInputMap());

        TriggerEventLevelDTO triggerEventLevel =  triggerFault(deviceId, item, topicInfo, map, data);
        if (!triggered) {
            // 告警结束 开启计时
            triggerEventLevel.setTriggered(true);
        }
        countTiming(item, triggered, triggerEventLevel, topicInfo, timestamp, data);
    }


    private void countTiming(ComputeEventItem item, Boolean isStart, TriggerEventLevelDTO triggerEventLevel,  GwUpEventTopic.TopicInfo topicInfo, Long timestamp, List<UpProp> data) {
        String faultAntiShakeKey = String.format(RedisConstant.DEVICE_FAULT_LOCK, item.getDeviceId(), item.getEventName());
        String faultLevelLockStartKey = String.format(RedisConstant.DEVICE_FAULT_LEVEL_LOCK_START, item.getDeviceId(), item.getEventName(), triggerEventLevel.getNewFaultLevel());
        String faultLevelLockEndKey = String.format(RedisConstant.DEVICE_FAULT_LEVEL_LOCK_END, item.getDeviceId(), item.getEventName(),WarningLevelEnum.FATAL_WARNING.getCode());

        Boolean lock = redisLockUtil.getLock(faultAntiShakeKey, IdUtil.fastUUID(), 2L);
        if (lock) {
            try{
                if(isStart){
                    // 是fault并且需要触发读秒
                    boolean faultStartLock=redisLockUtil.getLock(faultLevelLockStartKey, IdUtil.fastUUID(), expireTime.longValue());
                    if(!faultStartLock){
                        log.debug("countTiming get start lock failed, faultLevelLockStartKey:{}", faultLevelLockStartKey);
                    }
                    redisLockUtil.unLock(faultLevelLockEndKey);
                    log.debug("事件触发读秒 deviceId:{},eventName:{},status:start,level:{}",item.getDeviceId(),item.getEventName(),triggerEventLevel.getNewFaultLevel());
                }else  {
                    // 不是fault但需要触发清除读秒
                    boolean faultEndLock=redisLockUtil.getLock(faultLevelLockEndKey, IdUtil.fastUUID(), expireTime.longValue());
                    if(!faultEndLock){
                        log.debug("countTiming get end lock failed, faultLevelLockEndKey:{}", faultLevelLockEndKey);
                    }
                    for(WarningLevelEnum level:WarningLevelEnum.values()){
                        String tempFaultLevelLockStartKey = String.format(RedisConstant.DEVICE_FAULT_LEVEL_LOCK_START, item.getDeviceId(), item.getEventName(), level.getCode());
                        redisLockUtil.unLock(tempFaultLevelLockStartKey);
                    }
                    log.debug("事件消除读秒 deviceId:{},eventName:{},status:end",item.getDeviceId(),item.getEventName());
                }
                updateFaultRedisCache(item, isStart, topicInfo, timestamp, data);
            }finally{
                redisLockUtil.unLock(faultAntiShakeKey);
            }
        } else {
            // 加锁失败，说明当前存在锁，进行锁定状态判断
            log.warn("countTiming get lock failed, faultLevelLockEndKey: {}", faultLevelLockEndKey);
        }

    }

    /**
     * 更新故障状态至redis
     * @param item
     * @param isFault
     */
    private void updateFaultRedisCache(ComputeEventItem item, Boolean isFault, GwUpEventTopic.TopicInfo topicInfo, Long timestamp, List<UpProp> data) {
        // 更新故障状态到Redis
        String faultStateKey = String.format(RedisConstant.DEVICE_FAULT_EVENT_STATUS, item.getDeviceId(), item.getEventName());
        //生成故障缓存的Redis键
        String faultCacheKey = String.format(RedisConstant.DEVICE_FAULT_CACHE, item.getDeviceId(), item.getEventName());
        // 构建故障缓存对象
        FaultRedisCache cache = FaultRedisCache.builder().topicInfo(topicInfo).item(item).timestamp(timestamp).data(data).faultStateKey(faultStateKey).faultStatus(isFault).build();
        // 将故障缓存对象存入Redis
        stringRedisTemplate.opsForValue().set(faultCacheKey, JSONUtil.toJsonStr(cache));
    }



    private TriggerEventLevelDTO triggerFault(Long deviceId, ComputeEventItem item, GwUpEventTopic.TopicInfo topicInfo, Map<String, Object> map, List<UpProp> data) {
        //生成故障事件实例的Redis键
        String faultEventInstanceKey = String.format(RedisConstant.DEVICE_FAULT_EVENT_INSTANCE, topicInfo.getTenantId(), deviceId);
        // 获取异常实例缓存，取上一次的故障级别
        FaultEventInstance cacheEvents = JSONUtil.toBean(JSONUtil.toJsonStr(redisTemplate.opsForHash().get(faultEventInstanceKey, topicInfo.getEventName())), FaultEventInstance.class);
        Integer oldFaultLevel = cacheEvents.getFaultLevel();
        // 判断本次故障的故障级别
        Integer newFaultLevel = WarningLevelEnum.FATAL_WARNING.getCode();
        FaultLevelDefineElm faultLevelDefineElm = item.getFaultLevelDefineElm();
        if (!Objects.isNull(faultLevelDefineElm)) {
            if (faultLevelDefineElm.getEnableDefine()) {
                // 构建故障级别触发条件的映射
                Map<String, String> levelTriggerMap = new HashMap<>(faultLevelDefineElm.getLevelDefine().size());
                faultLevelDefineElm.getLevelDefine().forEach((k, v) -> {
                    levelTriggerMap.put(k, FaultLevelDefine.constructCondition(v));
                });
                // 根据条件获取故障级别
                newFaultLevel = FaultLevelDefine.getFaultLevelByCondition(levelTriggerMap, data);
            }
        }
        boolean needTrigger = !Objects.equals(oldFaultLevel, newFaultLevel);
        // 若故障级别不一致，则本次故障触发，标记故障开始
        return TriggerEventLevelDTO.builder().triggered(needTrigger).oldFaultLevel(oldFaultLevel).newFaultLevel(newFaultLevel).build();

    }


    /**
     * 故障锁自动到期后的处理
     * @param faultRedisCache 故障缓存对象 包含以下字段：
     *                        topicInfo topic信息
     *                        item 计算项
     *                        timestamp 时间戳
     *                        startFaultTriggered 是否触发故障开始
     *                        faultState 故障状态
     *                        data 数据
     *                        endFaultTriggered 是否触发故障结束
     *                        faultStateKey 故障状态键
     */
    @Override
    public void processFault(FaultRedisCache faultRedisCache,Integer level) {
        GwUpEventTopic.TopicInfo topicInfo = faultRedisCache.getTopicInfo();
        ComputeEventItem item = faultRedisCache.getItem();
        Long timestamp = faultRedisCache.getTimestamp();
        // boolean startFaultTriggered = faultRedisCache.isStartFaultTriggered();
        List<UpProp> data = faultRedisCache.getData();

        // boolean endFaultTriggered = faultRedisCache.isEndFaultTriggered();
        String faultStateKey = faultRedisCache.getFaultStateKey();
        FaultStatus faultState = JSONUtil.toBean(stringRedisTemplate.opsForValue().get(faultStateKey), FaultStatus.class);
        if (faultState == null) {
            faultState = new FaultStatus();
        }

        boolean isFault = faultRedisCache.getFaultStatus();

        if ((!faultState.getStatus()&&isFault)||(faultState.getStatus()&&isFault&&isDiffLevel(topicInfo,item.getDeviceId(),level))) {
            if(faultState.getLevelTriggerCount(level)<item.getFaultBeginThreshold()){
                Integer triggeredCount = faultState.addAndGetTrigger(level);
                if (triggeredCount >= item.getFaultBeginThreshold()) {
                    //超过触发阈值
                    //切换成激活状态并归零非故障计数器
                    faultState.beginFault(level);
                    stringRedisTemplate.opsForValue().set(faultStateKey, JSONUtil.toJsonStr(faultState));
                    //上报故障开始信息
                    faultProcess(topicInfo, timestamp, faultState, data,level);
                    log.info("触发阈值 deviceId:{},eventName:{},status:start,level:{}", topicInfo.getDeviceId(), topicInfo.getEventName(), level);
                }else{
                    //未超过触发阈值
                    log.info("start未超过触发阈值 faultState.getTriggerCount():{} item.getFaultBeginThreshold():{}",faultState.getLevelTriggerCount(level),item.getFaultBeginThreshold());
                    stringRedisTemplate.opsForValue().set(faultStateKey, JSONUtil.toJsonStr(faultState));
                }

            }
        }else if(faultState.getStatus()&&!isFault) {
            //本次故障没触发
            //累计非故障次数
            // if(faultState.getNotTriggerCount()<item.getFaultEndThreshold()){
            Integer notTriggerCount = faultState.addAndGetNotTrigger();
            if (notTriggerCount >= item.getFaultEndThreshold()) {
                //超过故障停止阈值
                //切换成停止状态并归零故障计数器
                faultState.endAllFault();
                stringRedisTemplate.opsForValue().set(faultStateKey, JSONUtil.toJsonStr(faultState));
                // cancelFault(topicInfo, faultState,item.getDeviceId());
                //上报故障结束信息
                faultProcess(topicInfo, timestamp, faultState, data,level);
                log.info("触发停止阈值 deviceId:{},eventName:{},status:end,level:{}", topicInfo.getDeviceId(), topicInfo.getEventName(), level);
            }else{
                //未超过触发阈值
                log.info("end未超过触发阈值 faultState.getNotTriggerCount():{} item.getFaultEndThreshold():{}",faultState.getNotTriggerCount(),item.getFaultEndThreshold());
                stringRedisTemplate.opsForValue().set(faultStateKey, JSONUtil.toJsonStr(faultState));
            }
        }
    }

    private boolean isDiffLevel(GwUpEventTopic.TopicInfo topicInfo,Long deviceId,Integer newLevel){
        //生成故障事件实例的Redis键
        String faultEventInstanceKey = String.format(RedisConstant.DEVICE_FAULT_EVENT_INSTANCE, topicInfo.getTenantId(), deviceId);
        // 获取异常实例缓存，取上一次的故障级别
        FaultEventInstance cacheEvents = JSONUtil.toBean(JSONUtil.toJsonStr(redisTemplate.opsForHash().get(faultEventInstanceKey, topicInfo.getEventName())), FaultEventInstance.class);
        Integer oldFaultLevel = cacheEvents.getFaultLevel();
        return !Objects.equals(oldFaultLevel, newLevel);
    }


    private void faultProcess(GwUpEventTopic.TopicInfo topicInfo, Long timestamp, FaultStatus faultState, List<UpProp> data,Integer level) {
        //上报故障开始信息
        UpData build = UpData.builder().timestamp(timestamp).prop(data).faultBegin(faultState.getLevelBegin(level)).faultEnd(faultState.getEnd()).faultStatus(faultState.getStatus()).build();
        //故障事件入库
        eventUpData2InfluxDBHandler.process(topicInfo, build);
        //故障事件触发订阅
        faultEventHandler.process(topicInfo, build);
    }

    private List<UpProp> extractProperties(Long deviceId, Map<String, Object> map, List<PropDataElm> properties, Map<String, String> faultInputMap) {
        List<UpProp> list = new ArrayList<>();
        properties.forEach(propDataElm -> {
            if (!map.containsKey(propDataElm.getProperty())) {
                return;
            }
            UpProp upProp = UpProp.builder().property(propDataElm.getProperty()).deviceId(deviceId).value(map.get(propDataElm.getProperty())).dataType(propDataElm.getDataType()).isArray(propDataElm.getIsArray())
                    //                    .length(propDataElm.getLength())
                    .build();
            if (faultInputMap != null) {
                String faultInput = faultInputMap.get(propDataElm.getProperty());
                if (faultInput != null) {
                    upProp.setFaultInput(faultInput);
                }
            }
            list.add(upProp);
        });
        return list;
    }
}
