package com.nti56.nlink.product.device.server.verticle;

import cn.hutool.core.util.ObjectUtil;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceDataResource;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewayControlProxy;
import com.nti56.nlink.product.device.server.loader.ConnectorScriptLoader;
import com.nti56.nlink.product.device.server.scriptApi.Engineering;
import com.nti56.nlink.product.device.server.scriptApi.EngineeringFactory;
import com.nti56.nlink.product.device.server.service.IEdgeGatewayService;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptException;
import static org.springframework.beans.factory.config.ConfigurableBeanFactory.SCOPE_PROTOTYPE;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-21 17:19:00
 * @since JDK 1.8
 */
@Component
@Scope(SCOPE_PROTOTYPE)
@Slf4j
public class MqttConnectorConsumerVerticle extends MqttBaseVerticle {
    
    public static final String EVENTBUS_CONNECTOR_STATUS = "EVENTBUS_CONNECTOR_STATUS";

    @Getter
    private String host;

    @Getter
    private Integer port;
    
    @Getter
    private String username;
    
    @Getter
    private String password;
    
    @Getter
    private Boolean ssl;
    
    @Getter
    private Integer reconnectGapTime;
    
    private Long tenantId;
    private Long connectorId;
    private Long processorId;
    private String topic;
    private Integer qos;
    
    @Autowired
    ScriptEngine scriptEngine;

    @Autowired
    EngineeringFactory engineeringFactory;
    
    @Autowired
    IEdgeGatewayService edgeGatewayService;

    @Autowired
    IEdgeGatewayControlProxy edgeGatewayControlProxy;

    @Autowired
    private DeviceDataResource deviceDataResource;

    private MessageConsumer<Object> consumer;

    private Long edgeGatewayId;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    
    @Override
    public void start(Promise<Void> startPromise) throws Exception {
        log.info("start-verticle mqttConnector");
        host = config().getString("host");
        port = config().getInteger("port");
        username = config().getString("username");
        password = config().getString("password");

        tenantId = config().getLong("tenantId");
        connectorId = config().getLong("connectorId");
        processorId = config().getLong("processorId");
        topic = config().getString("topic");
        qos = config().getInteger("qos");
        reconnectGapTime = config().getInteger("reconnectGapTime");
        ssl = config().getBoolean("ssl");
        edgeGatewayId = config().getLong("edgeGatewayId");

        consumer = vertx.eventBus().consumer(EVENTBUS_CONNECTOR_STATUS + "_" + connectorId, t -> {
            log.debug("reply connector status , {}, {}, {}", connected, host, port);
            t.reply(connected);
        });

        
        super.start(startPromise);
    }

    @Override
    public void stop(Promise<Void> stopPromise) throws Exception {
        if(consumer != null){
            log.info("unregister connector, {}, {}", host, port);
            consumer.unregister();
        }
        super.stop(stopPromise);
    }

    @Override
    public void subscribe(){
        
        this.client.publishHandler(message -> {
            Buffer payload = message.payload();
            String topic = message.topicName();
            String functionName = ConnectorScriptLoader.buildFunctionName(tenantId, connectorId, processorId);
            log.debug("receive: {} , {}", topic, payload);
            vertx.executeBlocking(p -> {
                try {
                    Engineering things = engineeringFactory.getEngineering(tenantId);
                    things.getScriptRuntimeContext().runOnContext((c) -> {
                        try {
                            Invocable invocable = (Invocable)scriptEngine;
                            Object r = invocable.invokeFunction(functionName, topic, payload.toString(), things); 
                            p.complete(r);
                        } catch (ScriptException e) {
                            e.printStackTrace();
                            p.fail(e);
                            // SpiUtil.dealLog(deviceDataResource,logEntity, e.getMessage(),false);
                            throw new RuntimeException(e);
    
                        } catch (NoSuchMethodException e) {
                            e.printStackTrace();
                            p.fail(e);
                            // SpiUtil.dealLog(deviceDataResource,logEntity, e.getMessage(),false);
                            throw new RuntimeException(e);
                        }
                        catch (Exception e) {
                            p.fail(e);
                            throw e;
                        } 
                    });
                } catch (Exception e) {
                    log.error("connector handle exception", e);
                    p.fail(e);
                }
            }, true, r -> {
                log.debug("connector result: {}", r);
            });
        });
        client.subscribe(topic, qos);
    }

    @Override
    protected void handleConnectStatusChange(Boolean connected) {
        if(ObjectUtil.isNotEmpty(edgeGatewayId) && edgeGatewayId != 0L){
            String gatewayStatus = connected ? "1" : "0";
            String redisKey = RedisConstant.EDGE_GATEWAY_STATUS + tenantId;
            stringRedisTemplate.opsForHash().put(redisKey,String.valueOf(edgeGatewayId),gatewayStatus);
        }
    }

}
