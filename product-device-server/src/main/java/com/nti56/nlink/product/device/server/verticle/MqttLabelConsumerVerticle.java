package com.nti56.nlink.product.device.server.verticle;

import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpLabelTopic;
import com.nti56.nlink.product.device.server.domain.thing.up.UpData;

import com.nti56.nlink.product.device.server.verticle.post.processor.label.LabelChangeToWSHandler;
import com.nti56.nlink.product.device.server.verticle.post.processor.label.Mapping2DeviceHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;


import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import static org.springframework.beans.factory.config.ConfigurableBeanFactory.SCOPE_PROTOTYPE;

import io.vertx.core.Promise;

import org.springframework.beans.factory.annotation.Value;


/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-22 13:30:10
 * @since JDK 1.8
 */
@Component
@Scope(SCOPE_PROTOTYPE)
@Slf4j
public class MqttLabelConsumerVerticle extends MqttBaseVerticle {
    
    @Getter
    @Value("${mqtt.host}")
    private String host;

    @Getter
    @Value("${mqtt.port}")
    private Integer port;

    @Getter
    @Value("${mqtt.username}")
    private String username;

    @Getter
    @Value("${mqtt.password}")
    private String password;

    @Getter
    @Value("${mqtt.ssl:true}")
    private Boolean ssl;
    
    @Getter
    private Integer reconnectGapTime;
    
    @Lazy
    @Autowired
    LabelChangeToWSHandler labelChangeToWSHandler;

    @Lazy
    @Autowired
    Mapping2DeviceHandler mapping2DeviceHandler;


    private Long counter = 0L;

    public static final String MQTT_LABEL_CONSUMER_COUNTER = "MQTT_LABEL_CONSUMER_COUNTER";

    @Override
    public void start(Promise<Void> startPromise) throws Exception {
        log.debug("start-verticle");
        super.start(startPromise);
    }

    @Override
    public void subscribe(){

        String labelChangeTopic = GwUpLabelTopic.createChangeSubscribeTopic(MqttBaseVerticle.shareSubscribeGroup);
        String labelReportTopic = GwUpLabelTopic.createReportSubscribeTopic(MqttBaseVerticle.shareSubscribeGroup);

        vertx.eventBus().consumer(MQTT_LABEL_CONSUMER_COUNTER).handler(t -> {
            t.reply(counter);
        });
        
        this.client.publishHandler(s1 -> {

            String topicName = s1.topicName();
            // Buffer gzip = s1.payload();
            // byte[] bs = GzipUtil.uncompress(gzip);
            // String payload = new String(bs, StandardCharsets.UTF_8);
            String payload = s1.payload().toString();

//            log.debug("topic: {}, msg: {}", topicName, payload);
            counter++;
            GwUpLabelTopic.TopicInfo topicInfo = GwUpLabelTopic.parseTopic(topicName);
            UpData upData = JSONObject.parseObject(payload, UpData.class);
            long startTime = System.currentTimeMillis();
            log.debug("上报链路，开始时间：{}，topic:{},分组数：{}", startTime,topicName,upData.getUpGroups().size());
            labelChangeToWSHandler.process(topicInfo,upData);
            mapping2DeviceHandler.process(topicInfo,upData);
            long endTime = System.currentTimeMillis();
            log.debug("上报链路，结束时间：{}，耗时:{}ms,topic:{}", endTime, endTime - startTime,topicName);
        });
        client.subscribe(labelChangeTopic, 1);
        client.subscribe(labelReportTopic, 1);
    }
    
    @Override
    protected void handleConnectStatusChange(Boolean connected) {
       
    }
}
