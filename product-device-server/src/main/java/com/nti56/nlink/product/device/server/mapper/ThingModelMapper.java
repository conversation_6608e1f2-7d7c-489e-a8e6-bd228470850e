package com.nti56.nlink.product.device.server.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.CommonMapper;
import com.nti56.nlink.product.device.server.entity.ThingModelEntity;
import com.nti56.nlink.product.device.server.model.ThingModelSimpleBo;
import com.nti56.nlink.product.device.server.model.ThingModelVo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 类说明: 物模型mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:09:46
 * @since JDK 1.8
 */
@Mapper
public interface ThingModelMapper extends CommonMapper<ThingModelEntity> {

    Page<ThingModelVo> listThingModel(IPage<ThingModelVo> page, @Param(Constants.WRAPPER) Wrapper<ThingModelVo> model);

    List<ThingModelSimpleBo> listThingModelSimpleBoNotIn(@Param("tenantId") Long tenantId,
                                                        @Param("idList") List<Long> idList);

    List<ThingModelSimpleBo> listAllThingModelSimpleBo(@Param("tenantId") Long tenantId);

    @Select("SELECT * FROM thing_model WHERE id = #{id} AND (tenant_id = #{tenantId} or tenant_id = 999)  AND deleted = 0")
    ThingModelEntity getById(@Param("tenantId") Long tenantId, @Param("id") Long id);

    @Delete("UPDATE thing_model SET deleted = 1 WHERE tenant_id = #{tenantId} AND id = #{id}")
    Integer deleteById(@Param("tenantId") Long tenantId, @Param("id") Long id);

    List<ThingModelEntity> listDeviceInheritModel( @Param("deviceId")Long deviceId,  @Param("tenantIsolation")TenantIsolation tenantIsolation);

    List<ThingModelEntity> listInheritModel(@Param("thingModelId")Long thingModelId,@Param("tenantIsolation") TenantIsolation tenantIsolation);

    List<ThingModelSimpleBo> listAllCommonThingModel(@Param("modelType") Integer modelType);

}
