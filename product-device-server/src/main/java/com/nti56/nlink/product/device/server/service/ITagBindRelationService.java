package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.IBaseService;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ResourceTypeEnum;
import com.nti56.nlink.product.device.server.entity.TagBindRelationEntity;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 标志关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-04-11 17:27:49
 * @since JDK 1.8
 */
public interface ITagBindRelationService extends IBaseService<TagBindRelationEntity> {

    Result<List<TagBindRelationEntity>> list(TagBindRelationEntity entity);

    Result<Void> deleteById(Long tenantId,Long entityId);

    /**
     * 同时绑定多个标记（目标创建时使用）
     *
     * @param tenantId
     * @param targetId 目标ID
     * @param tagIds 标签ID列表
     * @return
     */
    Result<Void> saveList(Long tenantId, Long targetId, List<Long> tagIds, ResourceTypeEnum tpye);


    Result<Void> batchSaveList(Long tenantId, Map<Long,List<Long>> deviceTagMap, ResourceTypeEnum tpye);

    Result<List<TagBindRelationEntity>> listByTagIds(Long tenantId,List<String> tagIds, ResourceTypeEnum type);

    R getTagList(TenantIsolation tenantIsolation, Integer resourceType);

    boolean deleteByTargetId(Long tenantId, ResourceTypeEnum resourceType, Long targetId);

    List<Long> getTagIdsByTargetId(Long tenantId, ResourceTypeEnum resourceType, Long targetId);

    List<TagRsp> getTags(Long tenantId, ResourceTypeEnum resourceType, Long targetId);

    Result<List<TagBindRelationEntity>> listByTargetId(Long tenantId, Long targetId);

    void batchDeleteByDeviceIds(Long tenantId, Set<Long> ids);

    Map<Long, List<TagRsp>> getTagListGroupByTargetId(Long tenantId, ResourceTypeEnum resourceTypeEnum, List<Long> ids);
}
