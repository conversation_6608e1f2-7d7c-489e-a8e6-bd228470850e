package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.constant.Constant;
import com.nti56.nlink.product.device.server.domain.thing.thingmodel.ThingModel;
import com.nti56.nlink.product.device.server.entity.ThingServiceEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.mapper.ThingModelMapper;
import com.nti56.nlink.product.device.server.mapper.ThingServiceMapper;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import com.nti56.nlink.product.device.server.service.IThingServiceService;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 物服务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 10:28:52
 * @since JDK 1.8
 */
@Service
@Slf4j
public class ThingServiceServiceImpl extends BaseServiceImpl<ThingServiceMapper, ThingServiceEntity> implements IThingServiceService {

    @Autowired
    ThingServiceMapper mapper;

    @Autowired
    ThingModelMapper thingModelMapper;

    @Autowired
    CommonFetcherFactory commonFetcherFactory;

    @Autowired
    @Lazy
    IDeviceService deviceService;

    @Override
    @Transactional
    public Result<ThingServiceEntity> save(TenantIsolation tenantIsolation, ThingServiceEntity entity) {
        if(!RegexUtil.checkName(entity.getServiceName())){
            return Result.error(ServiceCodeEnum.THING_SERVICE_NAME_ERROR);
        }
        if (RegexUtil.checkInputNameRepeat(entity.getInputData())) {
            return Result.error(ServiceCodeEnum.SERVICE_INPUT_NAME_REPEAT);
        }
        mapper.insert(entity);
        checkAndNotify(tenantIsolation.getTenantId(), entity.getThingModelId());
        return Result.ok(entity);
    }

    private void checkAndNotify(Long tenantId, Long thingModelId){
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);
        Result<ThingModel> modelResult = ThingModel.checkServiceInfo(
            thingModelMapper.getById(tenantId, thingModelId), 
            commonFetcher
        );
        if (!modelResult.getSignal()) {
            throw new BizException(modelResult.getServiceCode(), modelResult.getMessage());
        }
        ThingModel model = modelResult.getResult();
        Result<List<Long>> beInheritResult = model.checkBeInherit(commonFetcher);
        if(!beInheritResult.getSignal()){
            throw new BizException(beInheritResult.getServiceCode(), beInheritResult.getMessage());
        }
        deviceService.notifyDeviceSyncByThingModelId(thingModelId,tenantId,commonFetcher);
    }

    @Override
    public Result<Page<ThingServiceEntity>> getPage(ThingServiceEntity entity, Page<ThingServiceEntity> page) {
        Page<ThingServiceEntity> list = mapper.selectPage(page, new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    @Override
    public Result<List<ThingServiceEntity>> list(ThingServiceEntity entity) {
        List<ThingServiceEntity> list = mapper.selectList(new QueryWrapper<>(entity));
        return Result.ok(list);
    }

    @Override
    @Transactional
    public Result update(ThingServiceEntity entity, TenantIsolation tenantIsolation) {
        ThingServiceEntity thingServiceEntity = this.getByIdAndTenantIsolation(entity.getId(), tenantIsolation).getResult();
        if (thingServiceEntity == null){
            throw new BizException("该租户下找不到该物模型服务");
        }
        if(!entity.getServiceName().equals(thingServiceEntity.getServiceName())){
            return Result.error(ServiceCodeEnum.SERVICE_NAME_REVISE_ERROR);
        }
        if (RegexUtil.checkInputNameRepeat(entity.getInputData())) {
            return Result.error(ServiceCodeEnum.SERVICE_INPUT_NAME_REPEAT);
        }
        if (mapper.updateById(entity) == 1) {
            checkAndNotify(tenantIsolation.getTenantId(), entity.getThingModelId());
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
    }

    @Override
    public Result deleteById(Long entityId, TenantIsolation tenantIsolation) {
        ThingServiceEntity thingServiceEntity = this.getByIdAndTenantIsolation(entityId, tenantIsolation).getResult();
        if (thingServiceEntity == null){
            throw new BizException("该租户下找不到该物模型服务");
        }
        if (mapper.deleteById(entityId) == 1) {
            deviceService.notifyDeviceSyncByThingModelId(thingServiceEntity.getThingModelId(),tenantIsolation.getTenantId(),null);
            return Result.ok();
        }
        return Result.error(ServiceCodeEnum.CODE_DELETE_FAIL);
    }


    @Override
    public Result<ThingServiceEntity> getByIdAndTenantIsolation(Long entityId, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<ThingServiceEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ThingServiceEntity::getId,entityId)
                .and(w->w.eq(ThingServiceEntity::getTenantId,tenantIsolation.getTenantId())
                        .or().eq(ThingServiceEntity::getTenantId, Constant.DEFAULT_THING));
        return Result.ok(mapper.selectOne(lqw));
    }

    @Override
    public Result<Void> deleteBatchByIds(List<Long> ids, TenantIsolation tenantIsolation) {
        List<ThingServiceEntity> list = new LambdaQueryChainWrapper<>(mapper).select(ThingServiceEntity::getThingModelId)
                .eq(ThingServiceEntity::getTenantId, tenantIsolation.getTenantId())
                .in(ThingServiceEntity::getId, ids).list();

        Set<Long> modelIds = list.stream().map(ThingServiceEntity::getThingModelId).collect(Collectors.toSet());
        log.info("批量删除物服务，tenant:{},ids:{}",tenantIsolation,ids);
        LambdaUpdateWrapper<ThingServiceEntity> wrapper = new LambdaUpdateWrapper<ThingServiceEntity>()
                .eq(ThingServiceEntity::getTenantId, tenantIsolation.getTenantId())
                .in(ThingServiceEntity::getId, ids);
        mapper.delete(wrapper);
        if (CollectionUtil.isNotEmpty(modelIds)) {
            deviceService.notifyDeviceSyncByThingModelIds(modelIds,tenantIsolation.getTenantId(),null);
        }
        return Result.ok();
    }

    public void deleteByThingModelId(Long tenantId , Long thingModelId) {
        mapper.deleteByThingModelId(tenantId,thingModelId);
    }

}
