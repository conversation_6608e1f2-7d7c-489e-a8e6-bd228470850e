package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.mapper.DeviceModelInheritMapper;
import com.nti56.nlink.product.device.server.mapper.LabelBindRelationMapper;
import com.nti56.nlink.product.device.server.mapper.ThingServiceMapper;
import com.nti56.nlink.product.device.server.model.ThingModelSimpleBo;
import com.nti56.nlink.product.device.server.model.thingModel.vo.ThingModelVO;
import com.nti56.nlink.product.device.server.service.IDeviceModelInheritService;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import com.nti56.nlink.product.device.server.service.IThingModelService;
import com.nti56.nlink.product.device.server.util.CacheUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/27 16:33<br/>
 * @since JDK 1.8
 */
@Service
public class DeviceModelInheritServiceImpl extends BaseServiceImpl<DeviceModelInheritMapper, DeviceModelInheritEntity> implements IDeviceModelInheritService {

    @Autowired
    private DeviceModelInheritMapper deviceModelInheritMapper;

    @Autowired
    private LabelBindRelationMapper labelBindRelationMapper;

    @Autowired
    @Lazy
    private IDeviceService deviceService;

    @Autowired
    private IThingModelService thingModelService;

    @Autowired
    private ThingServiceMapper thingServiceMapper;

    //@Autowired
    //private IModelGraphService modelGraphService;

    @Override
    public Result<Void> deleteByDeviceId(Long deviceId) {
        LambdaQueryWrapper<DeviceModelInheritEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(DeviceModelInheritEntity::getDeviceId, deviceId);
        deviceModelInheritMapper.delete(lqw);
        return Result.ok();
    }

    @Override
    @Transactional
    public Result<Void> resetInheritRelation(TenantIsolation tenantIsolation, DeviceEntity deviceEntity, List<Long> thingModelIds) {
        if (CollectionUtil.isNotEmpty(thingModelIds)) {

            List<Long> actualInheritModelIds = new ArrayList<>(thingModelIds.size());
            thingModelIds.forEach(id -> {
                if (CacheUtils.SYS_COMMON_MODEL.containsKey(id)) {
                    //公共模型模板
                    ThingModelSimpleBo thingModelSimpleBo = CacheUtils.SYS_COMMON_MODEL.get(id);
                    //检查租户是不是拥有该模型了
                    QueryWrapper<ThingModelEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("name", thingModelSimpleBo.getName());
                    queryWrapper.eq("tenant_id", tenantIsolation.getTenantId());
                    ThingModelEntity one = thingModelService.getOne(queryWrapper);
                    if (Objects.isNull(one)) {
                        //不存在，拷贝一份公共模型到租户下
                        ThingModelEntity commonModel = ThingModelEntity.builder()
                                .tenantId(tenantIsolation.getTenantId())
                                .name(thingModelSimpleBo.getName()).modelType(thingModelSimpleBo.getModelType())
                                .build();
                        thingModelService.save(commonModel);
                        //modelGraphService.saveModelNode(commonModel,null);
                        actualInheritModelIds.add(commonModel.getId());
                        //判断公共模型是否带有公共服务，有服务就创建一份到租户下
                        Map<Long, ThingServiceEntity> modelServices = CacheUtils.SYS_COMMON_THING_SERVICE.entrySet().stream()
                                .filter(ks -> id.equals(ks.getValue().getThingModelId()))
                                .collect(Collectors.toMap(p -> p.getKey(), p -> p.getValue()));
                        if (MapUtil.isNotEmpty(modelServices)) {
                            modelServices.forEach((k, v) -> {
                                ThingServiceEntity thingServiceEntity = new ThingServiceEntity();
                                BeanUtil.copyProperties(v, thingServiceEntity);
                                thingServiceEntity.setId(null);
                                thingServiceEntity.setThingModelId(commonModel.getId());
                                thingServiceEntity.setTenantId(tenantIsolation.getTenantId());
                                thingServiceEntity.setModuleId(tenantIsolation.getModuleId());
                                thingServiceEntity.setSpaceId(tenantIsolation.getSpaceId());
                                thingServiceEntity.setEngineeringId(tenantIsolation.getSpaceId());
                                thingServiceMapper.insert(thingServiceEntity);
                            });
                        }
                    } else {
                        //查找已有公共模型备份
                        actualInheritModelIds.add(one.getId());
                    }
                }else{
                    actualInheritModelIds.add(id);
                }
            });
            thingModelIds = actualInheritModelIds;
        }
        List<DeviceModelInheritEntity> list1 = new LambdaQueryChainWrapper<>(deviceModelInheritMapper)
                .eq(DeviceModelInheritEntity::getTenantId, tenantIsolation.getTenantId())
                .eq(DeviceModelInheritEntity::getDeviceId, deviceEntity.getId())
                .select(DeviceModelInheritEntity::getInheritThingModelId)
                .list();
        Set<Long> oldInheritIds = list1.stream().map(DeviceModelInheritEntity::getInheritThingModelId).collect(Collectors.toSet());
        boolean thingModelIdIsNotEmpty = false;
        if (CollectionUtil.isNotEmpty(thingModelIds)) {
            thingModelIdIsNotEmpty = true;
        }
        boolean oldInheritIdsIsNotEmpty = false;
        if (CollectionUtil.isNotEmpty(oldInheritIds)) {
            oldInheritIdsIsNotEmpty = true;
        }
        if ((thingModelIdIsNotEmpty && oldInheritIdsIsNotEmpty && oldInheritIds.equals(new HashSet<>(thingModelIds))) ||
                (!thingModelIdIsNotEmpty && !oldInheritIdsIsNotEmpty)) {
            return Result.ok();
        }
        if (thingModelIdIsNotEmpty && oldInheritIdsIsNotEmpty) {
            oldInheritIds.removeAll(thingModelIds);
        }
        if (oldInheritIdsIsNotEmpty && CollectionUtil.isNotEmpty(oldInheritIds)) {
            LambdaUpdateWrapper<LabelBindRelationEntity> clearWrapper = new LambdaUpdateWrapper<LabelBindRelationEntity>()
                    .eq(LabelBindRelationEntity::getDeviceId, deviceEntity.getId())
                    .eq(LabelBindRelationEntity::getTenantId, tenantIsolation.getTenantId())
                    .in(LabelBindRelationEntity::getDirectlyModelId, oldInheritIds);
            labelBindRelationMapper.delete(clearWrapper);
        }
        LambdaUpdateWrapper<DeviceModelInheritEntity> eq = new LambdaUpdateWrapper<DeviceModelInheritEntity>()
                .eq(DeviceModelInheritEntity::getTenantId, tenantIsolation.getTenantId())
                .eq(DeviceModelInheritEntity::getDeviceId, deviceEntity.getId());
        deviceModelInheritMapper.delete(eq);
        if (thingModelIdIsNotEmpty) {
            int i = 0;
            ArrayList<DeviceModelInheritEntity> list = new ArrayList<>();
            for (Long thingModelId : thingModelIds) {
                list.add(DeviceModelInheritEntity.builder()
                        .deviceId(deviceEntity.getId())
                        .inheritThingModelId(thingModelId)
                        .sortNo(i).build());
            }
            this.saveBatch(list);
        }
        deviceService.setNotSyncById(deviceEntity.getId());
        return Result.ok();
    }

    @Override
    public List<ThingModelVO> listByDeviceId(Long id) {
        return deviceModelInheritMapper.getInheritModelsByDeviceId(id);
    }

    @Override
    public Result<Void> deleteByIdsAndDeviceId(List<Long> deleteThingModelIds, Long deviceId) {
        if (CollectionUtils.isEmpty(deleteThingModelIds)) {
            return Result.ok();
        }
        LambdaQueryWrapper<DeviceModelInheritEntity> lqw = new LambdaQueryWrapper<>();
        lqw.in(DeviceModelInheritEntity::getInheritThingModelId, deleteThingModelIds)
                .eq(DeviceModelInheritEntity::getDeviceId, deviceId);
        deviceModelInheritMapper.delete(lqw);
        return Result.ok();

    }

    @Override
    public void batchDeleteByDeviceIds(Long tenantId, List<Long> ids) {
        if (CollectionUtil.isNotEmpty(ids)) {
            LambdaQueryWrapper<DeviceModelInheritEntity> lqw = new LambdaQueryWrapper<>();
            lqw.in(DeviceModelInheritEntity::getDeviceId, ids)
                    .eq(DeviceModelInheritEntity::getTenantId, tenantId);
            deviceModelInheritMapper.delete(lqw);
        }
    }

    public Result<List<DeviceModelInheritEntity>> listByDeviceIds(Long tenantId, List<Long> ids) {
        LambdaQueryWrapper<DeviceModelInheritEntity> lqw = new LambdaQueryWrapper<>();
        if(CollectionUtils.isNotEmpty(ids)){
            lqw.in(DeviceModelInheritEntity::getDeviceId, ids);
        }
        lqw.eq(DeviceModelInheritEntity::getTenantId, tenantId);
        List<DeviceModelInheritEntity> deviceModelInheritEntities = deviceModelInheritMapper.selectList(lqw);
        return Result.ok(deviceModelInheritEntities);
    }

}
