package com.nti56.nlink.product.device.server.scriptApi;

import cn.hutool.core.collection.CollectionUtil;
import io.vertx.core.Context;
import io.vertx.core.Vertx;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import com.nti56.nlink.product.device.server.service.IThingResourceService;
import com.nti56.nlink.product.device.server.util.ApplicationContextUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName Tenant
 * @date 2022/4/15 15:30
 * @Version 1.0
 */
@Slf4j
public class Engineering implements EngineeringSpi{

    private Long tenantId;

    private Map<String,Thing> thingNameMap;

    private Map<Long,Thing> deviceIdThingMap;

    private Map<String,Thing> resourceIdThingMap;

    private IThingResourceService thingResourceService;

    private Vertx scriptRuntimeVertx;

    @Getter
    private Context scriptRuntimeContext;

    public Engineering(Long tenantId){
        this.tenantId = tenantId;
        thingResourceService = ApplicationContextUtil.getBean("thingResourceServiceImpl",IThingResourceService.class);
        List<Device> devices = thingResourceService.getTenantDevices(tenantId);
        log.info("create Engineering");
        scriptRuntimeVertx = Vertx.vertx();
        scriptRuntimeContext = scriptRuntimeVertx.getOrCreateContext();
        this.deviceIdThingMap = new HashMap<>();
        this.resourceIdThingMap = new HashMap<>();
        this.thingNameMap = new HashMap<>();
        devices.stream().forEach(device -> {
            Thing thing = SpiUtil.device2Thing(device, null);
            thingNameMap.put(device.getName(), thing);
            deviceIdThingMap.put(thing.getId(), thing);
            resourceIdThingMap.put(thing.getResourceId(), thing);
        });
    }

    public void close(){
        scriptRuntimeContext = null;
        if(scriptRuntimeVertx != null){
            scriptRuntimeVertx.close();
        }
    }

    @Override
    public Thing getByName(String deviceName) {
        if (!Optional.ofNullable(deviceName).isPresent()) {
            return null;
        }
        return thingNameMap.get(deviceName);
    }

    @Override
    public Thing getByDeviceId(Long deviceId) {
        Thing thing = deviceIdThingMap.get(deviceId);
        if(thing != null){
            thing.initData();
        }else{
            log.warn("thing not found,deviceId:{}",deviceId);
        }
        return thing;
    }

    @Override
    public Thing getByResourceId(String resourceId) {
        Thing thing = resourceIdThingMap.get(resourceId);
        if(thing != null){
            thing.initData();
        }
        return thing;
    }
    
    @Override
    public List<Thing> getByTag(String key, String value){
        List<Long> deviceIds = thingResourceService.getByTag(tenantId, key, value);
        return getByDeviceIds(deviceIds);
    }

    @Override
    public List<Thing> getByTagKey(String key){
        List<Long> deviceIds = thingResourceService.getByTag(tenantId, key, null);
        return getByDeviceIds(deviceIds);
    }

    @Override
    public List<Thing> getByTagId(String tagId){
        List<Long> deviceIds = thingResourceService.getByTagId(tenantId,tagId);
        return getByDeviceIds(deviceIds);
    }

    private List<Thing> getByDeviceIds(List<Long> deviceIds){
        List<Thing> things = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(deviceIds)) {
            deviceIds.stream().forEach(id -> {
                if (deviceIdThingMap.containsKey(id)) {
                    things.add(deviceIdThingMap.get(id));
                }
            });
        }
        return things;
    }

}
