package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.DeviceRuntimeMetadataField;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.device.Device;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceDataResource;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelDpo;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.*;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Subscription;
import com.nti56.nlink.product.device.server.domain.thing.up.UpData;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewayCacheProxy;
import com.nti56.nlink.product.device.server.listener.NoChangeKeyExpireListener.NoChangeInfo;
import com.nti56.nlink.product.device.server.mapper.DeviceMapper;
import com.nti56.nlink.product.device.server.mapper.EdgeGatewayMapper;
import com.nti56.nlink.product.device.server.mapper.LabelBindRelationMapper;
import com.nti56.nlink.product.device.server.mapper.LabelMapper;
import com.nti56.nlink.product.device.server.model.device.dto.DeviceCheckInfoContext;
import com.nti56.nlink.product.device.server.scriptApi.EngineeringFactory;
import com.nti56.nlink.product.device.server.service.*;
import com.nti56.nlink.product.device.server.service.cache.redis2Mem.MemoryCache;
import com.nti56.nlink.product.device.server.serviceEngine.DataConversionService;
import com.nti56.nlink.product.device.server.util.redis.RedisUtil;
import com.nti56.nlink.product.device.server.verticle.post.processor.label.Mapping2DeviceHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 类说明: 设备服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:10:11
 * @since JDK 1.8
 */
@Service
@Slf4j
public class DeviceOptionServiceImpl implements IDeviceOptionService {

    @Autowired
    DeviceMapper mapper;

    @Autowired
    LabelMapper labelMapper;

    @Autowired @Lazy
    IDeviceService deviceService;

    @Autowired
    EdgeGatewayMapper edgeGatewayMapper;

    @Autowired
    ITaskService taskService;

    @Autowired
    LabelBindRelationMapper labelBindRelationMapper;

    @Autowired
    CommonFetcherFactory commonFetcherFactory;

    @Autowired
    IEdgeGatewayService edgeGatewayService;

    @Autowired
    private DeviceDataResource deviceDataResource;

    @Autowired @Lazy
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private DataConversionService dataConversionService;

    @Autowired
    private EngineeringFactory engineeringFactory;

    @Autowired
    private IChangeNoticeService changeNoticeService;

    @Autowired
    private ISubscriptionService subscriptionService;

    @Autowired @Lazy
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private IEdgeGatewayCacheProxy edgeGatewayCacheProxy;

    @Autowired
    ILabelService labelService;

    //todo 删除
    @Lazy
    @Autowired
    Mapping2DeviceHandler mapping2DeviceHandler;

    
    private  <D extends DeviceEntity> Result<Void> doActivationDevice(TenantIsolation tenant, D device) {
        device.setLastSyncTime(LocalDateTime.now());
        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(device.getTenantId());
        Result<Device> deviceResult = Device.checkInfoToEvent(
                device,
                commonFetcher
        );
        return doSyncAction(tenant, device, deviceResult);
    }


    private  <D extends DeviceEntity> Result<Void> doActivationDeviceAll(TenantIsolation tenant, D device,
                                                     Map<Long,List<LabelBindRelationEntity>> labelBindRelationEntityMap, //根据设备ID
                                                     Map<Long,EdgeGatewayEntity> edgeGatewayEntityMap,
                                                     Map<Long,List<ResourceRelationEntity>> resourceRelationEntityListMap, //根据设备ID
                                                     Map<Long,ThingModelEntity> thingModelEntityMap,//根据继承modelId获取到的map
                                                     Map<Long,List<ThingModelInheritEntity>> thingModelInheritEntityMap, //根据继承modelId获取到的map
                                                     Map<Long,List<ThingServiceEntity>> thingServiceEntityMap,   //根据继承modelId获取到的map
                                                     Map<Long,List<SubscriptionEntity>> subscriptionEntityMap, //根据继承deviceId,modelId获取到的map
                                                     Map<Long,List<DeviceModelInheritEntity>> deviceModelinheritEntityListMap,  // 根据deviceId
                                                     Map<Long,List<DeviceServiceEntity>> deviceServiceEntityMap, //根据deviceId)
                                                     List<LabelEntity> labelEntityList,
                                                     List<ChannelEntity> channelEntitieList,
                                                     List<LabelGroupEntity> labelGroupEntityList) {
        device.setLastSyncTime(LocalDateTime.now());
        Result<Device> deviceResult = Device.checkInfoToEventNew(
                device,
                labelBindRelationEntityMap,
                edgeGatewayEntityMap,
                resourceRelationEntityListMap,
                thingModelEntityMap,
                thingModelInheritEntityMap,
                thingServiceEntityMap,
                subscriptionEntityMap,
                deviceModelinheritEntityListMap,
                deviceServiceEntityMap,
                labelEntityList,
                channelEntitieList,
                labelGroupEntityList
        );
        return doSyncAction(tenant, device, deviceResult);
    }

    private <D extends DeviceEntity> Result<Void> doSyncAction(TenantIsolation tenant, D device, Result<Device> deviceResult) {
        if (deviceResult.getSignal()) {
            Device deviceRun = deviceResult.getResult();
            DeviceRuntimeMetadataField metadata = deviceRun.createRuntimeMetadata();

            // device.setRuntimeMetadata(metadata);
            //获取计算任务
            Result<ComputeTaskEntity> computeTaskResult = deviceRun.createComputeTask();
            if (!computeTaskResult.getSignal()) {
                throw new BizException(computeTaskResult.getServiceCode(), computeTaskResult.getMessage());
            }
            //删除旧计算任务，插入新的计算任务
            ComputeTaskEntity task = computeTaskResult.getResult();
            taskService.updateComputeTask(tenant.getTenantId(), device.getId(), task);
            device.setStatus(StatusEnum.ONLINE.getValue());
            device.setLastSyncTime(LocalDateTime.now());
            device.setSyncStatus(SyncStatusEnum.HAVE_SYNC.getValue());
            mapper.updateById(device);

            deviceService.initDeviceTwinData(deviceRun, device, tenant);

            RedisUtil redisUtil = new RedisUtil(redisTemplate, null);
            Set<String> finalKeys = new HashSet<>();
            // String deviceBindKey = String.format(RedisConstant.DEVICE_BIND, device.getId());
            // finalKeys.add(deviceBindKey);
            // Set<Object> deviceBind = redisTemplate.opsForSet().members(deviceBindKey);
            Set<String> deviceBind = MemoryCache.getDeviceBind(device.getId());
            if (CollectionUtil.isNotEmpty(deviceBind)) {
                deviceBind.forEach(bind -> redisUtil.hdel((String) bind,device.getId().toString()));
            }
            
            MemoryCache.deleteSubscriptionListByDeviceId(deviceRun.getId());

            finalKeys.add(String.format(RedisConstant.SUBSCRIPTION_ENABLE,tenant.getTenantId(),deviceRun.getId()));
            redisTemplate.delete(finalKeys);
            MemoryCache.deleteDeviceLabelPropertyMap(device.getId());
            MemoryCache.deleteDeviceBind(device.getId());
            //计算任务
            MemoryCache.setComputeTask(deviceRun.getId(), task);
            Set<String> deviceBindKeys = new HashSet<>();
            //分组-设备映射表
            deviceRun.getGroupMap().forEach((group,labelNames) -> {
                int i = group.indexOf(".");
                String channel = group.substring(0, i);
                String groupName = group.substring(i + 1);
                Map<String,Object> value = new HashMap<>();
                String groupDeviceMapping = String.format(RedisConstant.GROUP_DEVICE_MAPPING, deviceRun.getDeviceEntity().getEdgeGatewayId(), channel, groupName);
                value.put(deviceRun.getId().toString(),labelNames);
                MemoryCache.setGroupDeviceMap(groupDeviceMapping,value);
                deviceBindKeys.add(groupDeviceMapping);
            });
            if (!deviceBindKeys.isEmpty()) {
                MemoryCache.setDeviceBind(device.getId(), deviceBindKeys);
            }
            //标签-属性映射表
            MemoryCache.setDeviceLabelPropertyMap(deviceRun.getId(), deviceRun.getNameMap());


            deviceResult.getResult().getSubscriptionRegistry().forEach((k,v) -> {
                List<String> subscriptions = new ArrayList<>();
                v.forEach(subscription -> {
                    if (subscription.getEnable()) {
                        subscriptions.add(JSON.toJSONString(subscription));
                    }
                });
                if (CollUtil.isNotEmpty(subscriptions)) {
                    MemoryCache.addSubscriptionListByDeviceId(deviceResult.getResult().getId(),k, v);
                }
            });
            

            List<Subscription> subscriptions = deviceResult.getResult().getDeviceModel().getSubscriptions();
            if (CollectionUtil.isNotEmpty(subscriptions)) {
                Set<Long> set = subscriptions.stream().filter(subscription -> subscription.getEnable()).map(Subscription::getId).collect(Collectors.toSet());
                if (CollUtil.isNotEmpty(set)) {
                    redisTemplate.opsForSet().add(String.format(RedisConstant.SUBSCRIPTION_ENABLE,tenant.getTenantId(),deviceResult.getResult().getId()),set.toArray());
                }
            }
            MemoryCache.putDeviceEnable(deviceRun.getId(),true);
            subscriptions.stream().forEach(t -> {
                Integer eventType = t.getEventType();
                SubscriptionEventTypeEnum eventTypeEnum = SubscriptionEventTypeEnum.typeOfValue(eventType);
                if(SubscriptionEventTypeEnum.NO_CHANGE.equals(eventTypeEnum)){
                    Integer noChangeSeconds = t.getNoChangeSeconds();
                    String[] split = t.getProperties().split(",");
                    for(String s:split){
                        NoChangeInfo noChangeInfo = new NoChangeInfo(deviceRun.getId(), s);
                        String key = noChangeInfo.toKey();
                        redisUtil.set(key, true, noChangeSeconds);
                    }
                }
            });
            dataConversionService.codePreload(device.getId(),tenant);

            ModelDpo modelDpo = deviceResult.getResult().getDeviceModel().toDpo();
            changeNoticeService.changeNotice(tenant.getTenantId(), modelDpo, ChangeTypeEnum.MODEL, ChangeSubjectEnum.DEVICE);
            return Result.ok();
        }
        throw new BizException(deviceResult.getServiceCode(), deviceResult.getMessage());
    }


    @Override
    @Async("deviceOptionExecutor")
    public Future<Result<Long>> doDeviceSync(Integer type,TenantIsolation tenantIsolation, DeviceEntity entity,
                                             Map<Long,List<LabelBindRelationEntity>> labelBindRelationEntityMap, //根据设备ID
                                             Map<Long,EdgeGatewayEntity> edgeGatewayEntityMap,
                                             Map<Long,List<ResourceRelationEntity>> resourceRelationEntityListMap, //根据设备ID
                                             Map<Long,ThingModelEntity> thingModelEntityMap,//根据继承modelId获取到的map
                                             Map<Long,List<ThingModelInheritEntity>> thingModelInheritEntityMap, //根据继承modelId获取到的map
                                             Map<Long,List<ThingServiceEntity>> thingServiceEntityMap,   //根据继承modelId获取到的map
                                             Map<Long,List<SubscriptionEntity>> subscriptionEntityMap, //根据继承deviceId,modelId获取到的map
                                             Map<Long,List<DeviceModelInheritEntity>> deviceModelinheritEntityListMap,  // 根据deviceId
                                             Map<Long,List<DeviceServiceEntity>> deviceServiceEntityMap, //根据deviceId)
                                             List<LabelEntity> labelEntityList,
                                             List<ChannelEntity> channelEntitieList,
                                             List<LabelGroupEntity> labelGroupEntityList) {
        Result<Void> result = null;
        if(1== type){
            result = doActivationDevice(tenantIsolation, entity);
        }else {
            result = doActivationDeviceAll(tenantIsolation,
                    entity,
                    labelBindRelationEntityMap,
                    edgeGatewayEntityMap,
                    resourceRelationEntityListMap,
                    thingModelEntityMap,
                    thingModelInheritEntityMap,
                    thingServiceEntityMap,
                    subscriptionEntityMap,
                    deviceModelinheritEntityListMap,
                    deviceServiceEntityMap,
                    labelEntityList,
                    channelEntitieList,
                    labelGroupEntityList
            );
        }
        if (result.getSignal()) {
            return new AsyncResult<>(Result.ok(entity.getEdgeGatewayId()));
        }else {
            return new AsyncResult<>(Result.error(result.getServiceCode(),result.getMessage()));
        }
    }
    @Override
    public Result<Device> doDeviceSync2( TenantIsolation tenantIsolation, DeviceEntity device, DeviceCheckInfoContext context) {
        Result<Device> deviceResult = Device.checkInfoToEvent2( device, context );
        return deviceResult;//doSyncAction(tenant, device, deviceResult);
    }

    @Override
    @Transactional
    public Result<Void> doDeviceBatchOffline(TenantIsolation tenant, List<DeviceEntity> deviceEntities, Map<Long, String> gwNameMap) {
        Iterator<DeviceEntity> iterator = deviceEntities.iterator();
        while (iterator.hasNext()){
            DeviceEntity next = iterator.next();
            switch (Objects.requireNonNull(StatusEnum.typeOfValue(next.getStatus()))) {
                case OFFLINE:
                case INACTIVATED:
                    iterator.remove();
                    continue;
                default:
            }
            next.setStatus(StatusEnum.OFFLINE.getValue());

        }
        if (deviceEntities.isEmpty()) {
            return Result.ok();
        }
        Map<Long, Long> deviceEdgeGatewayIdMap = new HashMap<>();
        deviceEntities.forEach(t -> {
            deviceEdgeGatewayIdMap.put(t.getId(), t.getEdgeGatewayId());
        });
        List<Long> ids = BeanUtilsIntensifier.getIds(deviceEntities, DeviceEntity::getId);
        Result<Void> result = taskService.batchDisableDeviceComputeTask(tenant, ids);
        if (result.getSignal()) {
            updateDeviceStatus(tenant,ids,StatusEnum.OFFLINE.getValue());
            // Set<String> enableKeySet = ids.stream().map(id -> String.format(RedisConstant.DEVICE_ENABLE, id)).collect(Collectors.toSet());
            // stringRedisTemplate.delete(enableKeySet);
            MemoryCache.removeDeviceEnable(ids);

            ids.forEach(id -> {
                stringRedisTemplate.opsForHash().put(RedisConstant.DEVICE_STATUS + tenant.getTenantId(), id.toString(), DeviceStatusEnum.DEACTIVATED.getValue().toString());
            });
            
            long timeMillis = System.currentTimeMillis();
            ids.forEach(id -> {
                UpData upData = DeviceServiceImpl.buildUpData(id, timeMillis, "status", DeviceStatusEnum.DEACTIVATED.getValue());
                //设备状态变化写入孪生
                mapping2DeviceHandler.processWriteTwin(deviceEdgeGatewayIdMap.get(id), id, tenant.getTenantId(), upData);
            });
            
            //云管设备状态变化通知
            deviceService.deviceBatchStatusChangeNotify(tenant,ids,DeviceStatusEnum.DEACTIVATED);
            return Result.ok();
        }
        throw new BizException(result.getServiceCode(), result.getMessage());
    }

    private void updateDeviceStatus(TenantIsolation tenant, List<Long> ids, Integer value) {
        if (CollectionUtil.isNotEmpty(ids)) {
            new LambdaUpdateChainWrapper<>(mapper)
                    .eq(DeviceEntity::getTenantId,tenant.getTenantId())
                    .in(DeviceEntity::getId,ids)
                    .set(DeviceEntity::getStatus,value)
                    .update();
        }
    }

}
