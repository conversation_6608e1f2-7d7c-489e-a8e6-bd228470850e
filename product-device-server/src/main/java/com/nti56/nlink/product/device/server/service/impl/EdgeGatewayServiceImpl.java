package com.nti56.nlink.product.device.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.util.*;
import com.nti56.nlink.product.device.client.model.dto.json.CustomDriverRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.EdgeGatewayRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.GatherParamField;
import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import com.nti56.nlink.product.device.server.annotation.AuditLog;
import com.nti56.nlink.product.device.server.annotation.GatewayChange;
import com.nti56.nlink.product.device.server.constant.Constant;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.channel.Channel;
import com.nti56.nlink.product.device.server.domain.thing.edgegateway.EdgeGateway;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.*;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.enums.ActionEnum;
import com.nti56.nlink.product.device.server.enums.AuditTargetEnum;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewayControlProxy;
import com.nti56.nlink.product.device.server.loader.ConnectorInstanceLoader;
import com.nti56.nlink.product.device.server.mapper.*;
import com.nti56.nlink.product.device.server.model.*;
import com.nti56.nlink.product.device.server.model.connector.dto.CreateGatewayConnectorDTO;
import com.nti56.nlink.product.device.server.model.connector.dto.EditGatewayConnectorDTO;
import com.nti56.nlink.product.device.server.model.datasync.*;
import com.nti56.nlink.product.device.server.model.device.vo.DcmpEdgeGatewayVO;
import com.nti56.nlink.product.device.server.model.edgegateway.EdgeGatewayChangeState;
import com.nti56.nlink.product.device.server.model.edgegateway.EdgeGatewayExcel;
import com.nti56.nlink.product.device.server.model.edgegateway.Traffic5gInfo;
import com.nti56.nlink.product.device.server.model.edgegateway.dto.*;
import com.nti56.nlink.product.device.server.model.edgegateway.vo.*;
import com.nti56.nlink.product.device.server.openapi.domain.request.GetEdgeGatewayRequest;
import com.nti56.nlink.product.device.server.openapi.domain.request.ListDcmpEdgeGatewayRequest;
import com.nti56.nlink.product.device.server.service.*;
import com.nti56.nlink.product.device.server.service.cache.IEdgeGatewayCacheService;
import com.nti56.nlink.product.device.server.util.ApplicationContextUtil;
import com.nti56.nlink.product.device.server.util.SignUtil;

import java.net.URLEncoder;
import java.util.*;

import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.dozer.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.servlet.http.HttpServletResponse;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-15 14:11:44
 * @since JDK 1.8
 */
@Slf4j
@Service
public class EdgeGatewayServiceImpl extends BaseServiceImpl<EdgeGatewayMapper, EdgeGatewayEntity> implements IEdgeGatewayService {

    @Autowired
    EdgeGatewayMapper edgeGatewayMapper;

    @Autowired
    private ITagBindRelationService tagBindRelationService;

    @Autowired
    private Mapper dozerMapper;

    @Autowired
    LabelMapper labelMapper;

    @Autowired @Lazy
    private IDeviceService deviceService;

    @Value("${traffic5g.appKey}")
    private String traffic5gAppKey;

    @Value("${traffic5g.appScrect}")
    private String traffic5gAppScrect;
    
    @Lazy
    @Autowired
    private IChannelService channelService;

    @Autowired @Lazy
    private ITaskService taskService;

    @Autowired
    private CommonFetcherFactory commonFetcherFactory;

    @Autowired @Lazy
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    IEdgeGatewayCacheService edgeGatewayCacheService;


    @Autowired
    IEdgeGatewayControlProxy edgeGatewayControlProxy;

    @Autowired
    IChangeNoticeService changeNoticeService;
    
//    @Autowired
//    private IFeignUserService feignUserService;
    
    @Autowired
    private IUpgradePackageService upgradePackageService;
    
    @Autowired @Lazy
    private ISubscriptionService subscriptionService;

    @Autowired
    private IConnectorService connectorService;

    @Autowired
    private ConnectorInstanceLoader connectorInstanceLoader;

    @Override
    @Transactional
    @AuditLog(action = ActionEnum.CREATE,target = AuditTargetEnum.GATEWAY,details = "创建新网关")
    public Result<CreateEdgeGatewayVO> createEdgeGateway(CreateEdgeGatewayDTO dto, TenantIsolation tenantIsolation) {

        Result<EdgeGatewayEntity> edgeGatewayEntityResult = buildEdgeGateway(dto,tenantIsolation.getTenantId());
        if(!edgeGatewayEntityResult.getSignal()){
            return Result.error(edgeGatewayEntityResult.getMessage());
        }
        EdgeGatewayEntity edgeGatewayEntity = edgeGatewayEntityResult.getResult();
        Long edgeGatewayId = IdGenerator.generateId();
        edgeGatewayEntity.setId(edgeGatewayId);
        edgeGatewayMapper.insert(edgeGatewayEntity);
        CreateEdgeGatewayVO createEdgeGatewayVO = CreateEdgeGatewayVO.builder().build();
        if(dto.getType() == EdgeGatewayTypeEnum.CONNECTOR_GATEWAY.getValue()){
            List<CreateGatewayConnectorDTO> createGatewayConnectorDTOList = dto.getCreateGatewayConnectorDTOList();
            List<ConnectorEntity> connectorEntityList = new ArrayList<>();
            for(CreateGatewayConnectorDTO createGatewayConnectorDTO : createGatewayConnectorDTOList){
                ConnectorEntity connectorEntity = new ConnectorEntity();
                BeanUtil.copyProperties(createGatewayConnectorDTO,connectorEntity);
                Long id = IdGenerator.generateId();
                connectorEntity.setEdgeGatewayId(edgeGatewayId);
                connectorEntity.setName(String.valueOf(id));
                connectorEntityList.add(connectorEntity);
            }
            if(CollectionUtil.isNotEmpty(connectorEntityList)){
                connectorService.saveOrUpdateBatch(connectorEntityList);
                createEdgeGatewayVO.setConnectorEntityList(connectorEntityList);
            }
        }

        List<Long> tagIds = dto.getTagIds();
        if (CollectionUtils.isNotEmpty(tagIds)) {
            Result<Void> result = tagBindRelationService.saveList(tenantIsolation.getTenantId(), edgeGatewayEntity.getId(), tagIds, ResourceTypeEnum.EDGE_GATEWAY);
            if (!result.getSignal()) {
                throw new BizException(result.getMessage());
            }
        }
        changeNotice(edgeGatewayEntity,tenantIsolation, ChangeTypeEnum.ADD);
        createEdgeGatewayVO.setEdgeGatewayId(edgeGatewayEntity.getId());
        return Result.ok(createEdgeGatewayVO);
    }

    @Override
    public Result<EdgeGatewayEntity> buildEdgeGateway(CreateEdgeGatewayDTO dto,Long tenantId){
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantId);
        Result<EdgeGateway> edgeGatewayResult = EdgeGateway.checkCreate(
                dto.getName(), dto.getDescript(), dto.getType(),
                dto.getHost(), dto.getPort(), dto.getImei(), 
                dto.getTrafficCard(), dto.getOperators(),
                dto.getVisitPublicMqtt(),
                dto.getPublicMqttIp(), dto.getPublicMqttPort(),
                commonFetcher
        );
        if(!edgeGatewayResult.getSignal()){
            return Result.error(edgeGatewayResult.getMessage());
        }
        EdgeGateway edgeGateway = edgeGatewayResult.getResult();
        EdgeGatewayEntity edgeGatewayEntity = edgeGateway.toEntity();
        String targetVersion = upgradePackageService.getTargetVersion().getResult();
        edgeGatewayEntity.setTargetVersion(targetVersion);
        edgeGatewayEntity.setMemoryMonitor(dto.getMemoryMonitor());
        edgeGatewayEntity.setSpaceMonitor(dto.getSpaceMonitor());
        return Result.ok(edgeGatewayEntity);
    }

    @Autowired
    ChannelMapper channelMapper;

    @Autowired
    LabelBindRelationMapper labelBindRelationMapper;

    @Async
    @Transactional
    @Override
    public void syncLabelConfiguration(Long edgeGatewayId, SyncEdgeGatewayDto info, Long tenantId) {
        log.info("边缘网关同步标签配置");
        EdgeGatewayEntity one = new LambdaQueryChainWrapper<>(edgeGatewayMapper).eq(EdgeGatewayEntity::getId, edgeGatewayId).eq(EdgeGatewayEntity::getTenantId, tenantId).one();
        if (ObjectUtils.isEmpty(one)) {
            return;
        }

        //旧OT channelMap<Long,String>
        List<ChannelEntity> channelEntityList = channelService.listByEdgeGatewayId(edgeGatewayId,tenantId).getResult();
        Map<Long,String> otChannelMap = channelEntityList.stream().collect(Collectors.toMap(ChannelEntity::getId, ChannelEntity::getName, (key1, key2) -> key2));
        //边缘上报 channelMap<Long,String>
        Map<Long,String> gwChannelMap = info.getChannelDtoList().stream().collect(Collectors.toMap(SyncChannelDto::getId, SyncChannelDto::getName, (key1, key2) -> key2));
        //新OT channelMap channelMap<String,Long>
        Map<String,Long> newChannelMap = new HashMap<>();
        //旧id-新id
        Map<Long,Long> channelIdMap = new HashMap<>();

        {
            labelMapper.physicalDeleteByEdgeGatewayId(edgeGatewayId);
            labelGroupMapper.physicalDeleteByEdgeGatewayId(edgeGatewayId);
            channelParamMapper.physicalDeleteByEdgeGatewayId(edgeGatewayId);
            channelMapper.physicalDeleteByEdgeGatewayId(edgeGatewayId);
            log.info("物理删除旧数据！");
        }
        {
            Map<Long,Long> channelIdmap = new HashMap<>();
            Map<Long,Long> labelGroupIdmap = new HashMap<>();
            Map<Long,Long> groupChannelMap = new HashMap<>();
            Map<Long,String> channelMap = new HashMap<>();
            Map<Long,String> groupMap = new HashMap<>();
            Map<String,Map<String,Map<String,Long>>> allMap = new HashMap<>();
            List<ChannelEntity> channelEntities = BeanUtilsIntensifier.copyBeanList(info.getChannelDtoList(), ChannelEntity.class);
            List<ChannelParamEntity> channelParamEntities = BeanUtilsIntensifier.copyBeanList(info.getChannelParamDtoList(), ChannelParamEntity.class);
            List<LabelGroupEntity> labelGroupEntities = BeanUtilsIntensifier.copyBeanList(info.getLabelGroupDtoList(), LabelGroupEntity.class);
            List<LabelEntity> labelEntities = BeanUtilsIntensifier.copyBeanList(info.getLabelDtoList(), LabelEntity.class);
            List<LabelBindRelationEntity> relationEntities = new LambdaQueryChainWrapper<>(labelBindRelationMapper)
                    .eq(LabelBindRelationEntity::getEdgeGatewayId,edgeGatewayId)
                    .list();
            if (!channelEntities.isEmpty()) {
                channelEntities.forEach(channelEntity -> {
                    long newId = IdGenerator.generateId();
                    newChannelMap.put(channelEntity.getName(),newId);
                    channelMap.put(newId,channelEntity.getName());
                    allMap.put(channelEntity.getName(),new HashMap<>());
                    channelIdmap.put(channelEntity.getId(),newId);
                    channelEntity.setId(newId);
                    channelEntity.setEdgeGatewayId(edgeGatewayId);
                    channelEntity.setTenantId(tenantId);
                });
                log.info("批量新增通道！");
                channelMapper.insertBatchSomeColumn(channelEntities);
            }
            for (Map.Entry<Long, String> entry : otChannelMap.entrySet()) {
                String oldDataName = entry.getValue();
                Long oldDataId = entry.getKey();
                //找的到通过旧的name 来新的OT channelMap
                if(newChannelMap.get(oldDataName) != null){
                    channelIdMap.put(oldDataId,newChannelMap.get(oldDataName));
                }else { //通过旧id去边缘 map找名称name，再用name到新OT channelMap找id
                    if(gwChannelMap.get(oldDataId)!=null){
                        String gwName = gwChannelMap.get(oldDataId);
                        Long id = newChannelMap.get(gwName);
                        channelIdMap.put(oldDataId,id);
                    }
                }
            }
            if (!channelParamEntities.isEmpty()) {
                log.info("批量新增通道参数！");
                channelParamEntities.forEach(channelParamEntity -> {
                    channelParamEntity.setId(IdGenerator.generateId());
                    channelParamEntity.setChannelId(channelIdmap.get(channelParamEntity.getChannelId()));
                    channelParamEntity.setTenantId(tenantId);
                });
                channelParamMapper.insertBatchSomeColumn(channelParamEntities);
            }
            if (!labelGroupEntities.isEmpty()) {
                labelGroupEntities.forEach(labelGroupEntity -> {
                    long newId = IdGenerator.generateId();
                    groupMap.put(newId,labelGroupEntity.getName());
                    groupChannelMap.put(newId,channelIdmap.get(labelGroupEntity.getChannelId()));
                    allMap.get(channelMap.get(channelIdmap.get(labelGroupEntity.getChannelId()))).put(labelGroupEntity.getName(), new HashMap<>());
                    labelGroupIdmap.put(labelGroupEntity.getId(),newId);
                    labelGroupEntity.setId(newId);
                    labelGroupEntity.setChannelId(channelIdmap.get(labelGroupEntity.getChannelId()));
                    labelGroupEntity.setTenantId(tenantId);
                });
                log.info("批量新增分组！");
                labelGroupMapper.insertBatchSomeColumn(labelGroupEntities);
            }
            if (!labelEntities.isEmpty()) {
                labelEntities.forEach(label -> {
                    long newId = IdGenerator.generateId();
                    Long channelId = groupChannelMap.get(labelGroupIdmap.get(label.getLabelGroupId()));
                    allMap.get(channelMap.get(channelId)).get(groupMap.get(labelGroupIdmap.get(label.getLabelGroupId()))).put(label.getName(),newId);
                    label.setId(newId);
                    label.setLabelGroupId(labelGroupIdmap.get(label.getLabelGroupId()));
                    label.setTenantId(tenantId);
                });
                log.info("批量新增标签！");
                labelMapper.insertBatchSomeColumn(labelEntities);
            }
            if (CollectionUtil.isNotEmpty(relationEntities)) {
                labelBindRelationMapper.physicalDeleteByEdgeGatewayId(edgeGatewayId);
                relationEntities.forEach(relation -> {
                    Map<String, Map<String, Long>> groupAllMap = allMap.get(relation.getChannelName());
                    if (CollectionUtil.isNotEmpty(groupAllMap)) {
                        Map<String, Long> labelAllMap = groupAllMap.get(relation.getLabelGroupName());
                        if (CollectionUtil.isNotEmpty(labelAllMap)) {
                            Long newId = labelAllMap.get(relation.getLabelName());
                            relation.setLabelId(newId);
                            return;
                        }
                    }
                    relation.setLabelId(null);
                });
                labelBindRelationMapper.insertBatchSomeColumn(relationEntities);
            }
        }
        {
            List<SubscriptionEntity> subscriptionEntityList = subscriptionService.getChannelSubscriptionByEdgeGatewayId(edgeGatewayId).getResult();
            if(CollectionUtil.isNotEmpty(subscriptionEntityList)){
                List<SubscriptionEntity> updateSubscriptionEntityList = new ArrayList<>();
                for(SubscriptionEntity subscriptionEntity :subscriptionEntityList){
                    String fromIdStr = subscriptionEntity.getFromId();
                    if(StringUtils.isBlank(fromIdStr)){
                        continue;
                    }
                    SubscriptionEntity newSubscriptionEntity = new SubscriptionEntity();
                    BeanUtil.copyProperties(subscriptionEntity,newSubscriptionEntity);
                    List<Long> idList = Arrays.stream(subscriptionEntity.getFromId().split(",")).map(Long::parseLong).collect(Collectors.toList());
                    List<Long> newIdList = new ArrayList<>();
                    for(Long id :idList){
                        //删除旧通道状态缓存
                        String delKey = RedisConstant.EDGE_CHANNEL_STATUS_CACHE_PREFIX + subscriptionEntity.getTenantId() + ":" + edgeGatewayId;
                        if(stringRedisTemplate.opsForHash().hasKey(delKey,String.valueOf(id))){
                            stringRedisTemplate.opsForHash().delete(delKey,String.valueOf(id));
                        }
                        if(channelIdMap.get(id) != null){
                            newIdList.add(channelIdMap.get(id));
                            //更新订阅缓存
                            buildChannelSubscriptionCache(id,channelIdMap.get(id),0);
                        }else {
                            //删除订阅缓存
                            buildChannelSubscriptionCache(id,0L,0);
                        }
                    }
                    String newFormIdStr = newIdList.stream().map(Object::toString).collect(Collectors.joining(",")).trim();
                    newSubscriptionEntity.setFromId(newFormIdStr);
                    updateSubscriptionEntityList.add(newSubscriptionEntity);
                }
                if(CollectionUtil.isNotEmpty(updateSubscriptionEntityList)){
                    subscriptionService.saveOrUpdateBatch(updateSubscriptionEntityList);
                }

            }
        }
        TenantIsolation tenantIsolation = new TenantIsolation();
        tenantIsolation.setTenantId(tenantId);
        edgeGatewaySyncById(edgeGatewayId,tenantIsolation);
    }


    private void buildChannelSubscriptionCache(Long oldChannelId,Long newChannelId,Integer type){
        String redisKey = String.format(RedisConstant.SUBSCRIPTION_CHANNEL_REGISTRY,oldChannelId, StatusEventEnum.DISCONNECT.getName());
        //更新
        if(type == 0){
            List<String> channelEventNameList= new ArrayList<>();
            channelEventNameList.add(StatusEventEnum.CONNECT.getName());
            channelEventNameList.add(StatusEventEnum.DISCONNECT.getName());
            for(String name:channelEventNameList){
                Map<Object,Object> subscriptionMap = stringRedisTemplate.opsForHash().entries(redisKey);
                if(CollectionUtil.isNotEmpty(subscriptionMap)){
                    stringRedisTemplate.delete(redisKey);
                    redisKey = String.format(RedisConstant.SUBSCRIPTION_CHANNEL_REGISTRY,newChannelId, StatusEventEnum.DISCONNECT.getName());
                    stringRedisTemplate.opsForHash().putAll(redisKey,subscriptionMap);
                }
            }
        }else if(type == 1){//删除
            if(stringRedisTemplate.hasKey(redisKey)){
                stringRedisTemplate.delete(redisKey);
            }
        }
    }

    @Override
    @AuditLog(action = ActionEnum.FETCH,target = AuditTargetEnum.GATEWAY,details = "根据id拉取边缘网关配置")
    public Result<SyncEdgeGatewayDto> edgeGatewayPullById(Long id, TenantIsolation tenantIsolation) {
        Result<SyncEdgeGatewayDto> result = edgeGatewayControlProxy.pullConfig(id, tenantIsolation.getTenantId());
        if (result.getSignal()) {
            this.syncLabelConfiguration(id,result.getResult(),tenantIsolation.getTenantId());
            return Result.ok();
        }
        return result;
    }

    private void changeNotice(EdgeGatewayEntity edgeGatewayEntity, TenantIsolation tenantIsolation, ChangeTypeEnum changeType) {
        EdgeGatewayChangeState changeState = EdgeGatewayChangeState.builder().isEdgeGatewayChange(false).changeTime(LocalDateTime.now()).build();
        String stateKey = String.format(RedisConstant.OT_GATEWAY_CHANGE_STATE, edgeGatewayEntity.getId());
        switch (changeType) {
            case ADD:
                stringRedisTemplate.opsForValue().set(stateKey,JSON.toJSONString(changeState));
                break;
            case UPDATE:
                changeState.setIsEdgeGatewayChange(true);
                stringRedisTemplate.opsForValue().set(stateKey,JSON.toJSONString(changeState));
                break;
            case DELETE:
                stringRedisTemplate.delete(stateKey);
                break;
            default:
        }
        changeNoticeService.changeNotice(tenantIsolation.getTenantId(),edgeGatewayEntity,changeType, ChangeSubjectEnum.EDGE_GATEWAY);
    }

    @Override
    public Result<Page<EdgeGatewayVO>> getEdgeGatewayPage(EdgeGatewayDto edgeGateway, Page<EdgeGatewayVO> page, TenantIsolation tenantIsolation) {
        Page<EdgeGatewayVO> edgeGatewayPage = getEdgeGatewayVOPage(edgeGateway, page, tenantIsolation);
        List<EdgeGatewayVO> records = edgeGatewayPage.getRecords();
        if (Objects.isNull(records) || records.isEmpty()) {
            return Result.ok(edgeGatewayPage);
        }
        Map<Long, List<TagRsp>> tagMap = tagBindRelationService.getTagListGroupByTargetId(tenantIsolation.getTenantId(), ResourceTypeEnum.EDGE_GATEWAY, BeanUtilsIntensifier.getIds(records, EdgeGatewayVO::getId));
        for (EdgeGatewayVO record : records) {
            Boolean visitPublicMqtt = record.getVisitPublicMqtt();
            if(visitPublicMqtt == null){
                visitPublicMqtt = false;
            }
            if(visitPublicMqtt){
                if(edgeGateway.getPublicMqttIp() != null 
                    && !"".equals(edgeGateway.getPublicMqttIp())
                    && edgeGateway.getPublicMqttPort() != null
                ){
                    record.setMqttHost(edgeGateway.getPublicMqttIp());
                    record.setMqttPort(edgeGateway.getPublicMqttPort());
                }else{
                    record.setMqttHost(this.mqttPublicHost);
                    record.setMqttPort(this.mqttPublicPort);
                }
            }else{
                record.setMqttHost(this.mqttHost);
                record.setMqttPort(this.mqttPort);
            }
            if(record.getType() == EdgeGatewayTypeEnum.CONNECTOR_GATEWAY.getValue()){
                List<ConnectorEntity> connectorEntityList = connectorService.getConnectorByEdgeGatewayId(record.getId()).getResult();
                if(CollectionUtil.isNotEmpty(connectorEntityList)){
                    record.setConnectorStatus(connectorEntityList.get(0).getStatus());
                    record.setConnectorEntityList(connectorEntityList);
                }
            }
            
            record.setOtHost(this.otHost);
            record.setOtPort(this.otPort);
            record.setTags(tagMap.get(record.getId()));
        }
        return Result.ok(edgeGatewayPage);
    }

    @Override
    public Result<List<EdgeGatewayEntity>> listEdgeGateway(EdgeGatewayEntity edgeGateway, TenantIsolation tenantIsolation) {


        List<EdgeGatewayEntity> edgeGatewayEntityList = null;
        if (Optional.ofNullable(edgeGateway).isPresent() && Optional.ofNullable(edgeGateway.getName()).isPresent()) {
            edgeGatewayEntityList= edgeGatewayMapper.getGwList(tenantIsolation.getTenantId(),null,0,edgeGateway.getName());
        }else{
            edgeGatewayEntityList= edgeGatewayMapper.getGwList(tenantIsolation.getTenantId(),null,0,null);
        }
        return Result.ok(edgeGatewayEntityList);
    }

    @Override
    public Result<EdgeGatewayEntity> getByIdAndTenantIsolation(Long id, TenantIsolation tenantIsolation){
        return Result.ok(edgeGatewayMapper.getById(tenantIsolation.getTenantId(),id));
    }

    @Override
    @Transactional
    @AuditLog(action = ActionEnum.UPDATE,target = AuditTargetEnum.GATEWAY,details = "更新网关")
    public Result<Void> updateEdgeGateway(EditEdgeGatewayDTO dto, TenantIsolation tenantIsolation) {
        
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());
        Result<EdgeGateway> edgeGatewayResult = EdgeGateway.checkUpdate(
            dto.getId(),
            dto.getName(), dto.getDescript(), dto.getType(), 
            dto.getHost(), dto.getPort(), dto.getImei(), dto.getTrafficCard(), dto.getOperators(),
            dto.getVisitPublicMqtt(),
            dto.getPublicMqttIp(), dto.getPublicMqttPort(),
            commonFetcher
        );
        if(!edgeGatewayResult.getSignal()){
            return Result.error(edgeGatewayResult.getMessage());
        }
        EdgeGateway edgeGateway = edgeGatewayResult.getResult();
        EdgeGatewayEntity edgeGatewayEntity = edgeGateway.toEntity();

        edgeGatewayMapper.updateById(edgeGatewayEntity);

        if(dto.getType() == EdgeGatewayTypeEnum.CONNECTOR_GATEWAY.getValue()){
            List<EditGatewayConnectorDTO> editGatewayConnectorDTOList = dto.getEditGatewayConnectorDTOList();
            List<ConnectorEntity> connectorEntityList = new ArrayList<>();
            for(EditGatewayConnectorDTO editGatewayConnectorDTO : editGatewayConnectorDTOList){
                ConnectorEntity connectorEntity = new ConnectorEntity();
                BeanUtil.copyProperties(editGatewayConnectorDTO,connectorEntity);
                connectorEntityList.add(connectorEntity);
            }
            if(CollectionUtil.isNotEmpty(connectorEntityList)){
                connectorService.saveOrUpdateBatch(connectorEntityList);
            }
        }
        edgeGatewayCacheService.evictHeartbeatUuid(
            tenantIsolation.getTenantId(), 
            dto.getId(), 
            edgeGatewayEntity.getHeartbeatUuid()
        );

        Result<Void> result = tagBindRelationService.saveList(tenantIsolation.getTenantId(), edgeGatewayEntity.getId(), dto.getTagIds(), ResourceTypeEnum.EDGE_GATEWAY);
        if (!result.getSignal()) {
            throw new BizException(result.getMessage());
        }
        changeNotice(edgeGatewayEntity,tenantIsolation, ChangeTypeEnum.UPDATE);
        return Result.ok();
    }

    @Override
    @Transactional
    @AuditLog(action = ActionEnum.DELETE,target = AuditTargetEnum.GATEWAY,details = "指定ID删除网关")
    public Result<EdgeGatewayEntity> deleteEdgeGatewayById(Long edgeGatewayId, TenantIsolation tenantIsolation) {
        Result<EdgeGatewayEntity> result = this.edgeGatewayDeleteCheck(edgeGatewayId, tenantIsolation);
        if (!result.getSignal()){
            throw new BizException(result.getMessage());
        }
        edgeGatewayMapper.deleteById(edgeGatewayId);
        String delKey = RedisConstant.EDGE_GATEWAY_STATUS + tenantIsolation.getTenantId();
        if(stringRedisTemplate.opsForHash().hasKey(delKey,String.valueOf(edgeGatewayId))) {
            stringRedisTemplate.opsForHash().delete(delKey,String.valueOf(edgeGatewayId));
        }
        EdgeGatewayEntity edgeGatewayEntity = result.getResult();
        if(edgeGatewayEntity.getType() == EdgeGatewayTypeEnum.CONNECTOR_GATEWAY.getValue()){
            connectorService.deleteConnectorByEdgeGatewayId(edgeGatewayId,tenantIsolation);
            return Result.ok();
        }
        labelMapper.deleteByEdgeGatewayId(edgeGatewayId);

        labelGroupMapper.deleteByEdgeGatewayId(edgeGatewayId);

        channelService.deleteByEdgeGatewayId(edgeGatewayId);

        tagBindRelationService.deleteByTargetId(tenantIsolation.getTenantId(), ResourceTypeEnum.EDGE_GATEWAY,edgeGatewayId);
    
        subscriptionService.delByDirectlyModelId(edgeGatewayId,tenantIsolation.getTenantId(), ModelTypeEnum.GATEWAY_MODEL);

        changeNotice(EdgeGatewayEntity.builder().id(edgeGatewayId).build(),tenantIsolation,ChangeTypeEnum.DELETE);
        return Result.ok();
    }

    @Value("${mqtt.publicHost}")
    private String mqttPublicHost;

    @Value("${mqtt.publicPort}")
    private Integer mqttPublicPort;

    @Value("${mqtt.host}")
    private String mqttHost;

    @Value("${mqtt.port}")
    private Integer mqttPort;

    @Value("${ot.publicHost}")
    private String otHost;

    @Value("${ot.publicPort}")
    private Integer otPort;

    @Value("${mqtt.username}")
    private String mqttUsername;

    @Value("${mqtt.password}")
    private String mqttPassword;

    @Lazy
    @Autowired
    ILabelService labelService;
    
    @Override
    public Result<EdgeGatewayVO> getEdgeGatewayById(Long edgeGatewayId, TenantIsolation tenantIsolation) {
        EdgeGatewayEntity edgeGateway = this.getByIdAndTenantIsolation(edgeGatewayId,tenantIsolation).getResult();
        if (edgeGateway == null){
            return Result.ok();
        }
        EdgeGatewayVO edgeGatewayVO = BeanUtilsIntensifier.copyBean(edgeGateway, EdgeGatewayVO.class);
        edgeGatewayVO.setTags(tagBindRelationService.getTags(tenantIsolation.getTenantId(), ResourceTypeEnum.EDGE_GATEWAY, edgeGateway.getId()));
        Boolean visitPublicMqtt = edgeGateway.getVisitPublicMqtt();
        if(visitPublicMqtt == null){
            visitPublicMqtt = false;
        }
        if(visitPublicMqtt){
            if(edgeGateway.getPublicMqttIp() != null 
                && !"".equals(edgeGateway.getPublicMqttIp())
                && edgeGateway.getPublicMqttPort() != null
            ){
                edgeGatewayVO.setMqttHost(edgeGateway.getPublicMqttIp());
                edgeGatewayVO.setMqttPort(edgeGateway.getPublicMqttPort());
            }else{
                edgeGatewayVO.setMqttHost(this.mqttPublicHost);
                edgeGatewayVO.setMqttPort(this.mqttPublicPort);
            }
        }else{
            edgeGatewayVO.setMqttHost(this.mqttHost);
            edgeGatewayVO.setMqttPort(this.mqttPort);
        }
        edgeGatewayVO.setPublicMqttIp(edgeGateway.getPublicMqttIp());
        edgeGatewayVO.setPublicMqttPort(edgeGateway.getPublicMqttPort());
        edgeGatewayVO.setMqttUsername(this.mqttUsername);
        edgeGatewayVO.setMqttPassword(this.mqttPassword);

        edgeGatewayVO.setOtHost(this.otHost);
        edgeGatewayVO.setOtPort(this.otPort);
        edgeGatewayVO.setHeartbeatUuid(edgeGateway.getHeartbeatUuid());
        edgeGatewayVO.setTenantId(tenantIsolation.getTenantId());
        edgeGatewayVO.setMemoryMonitor(edgeGateway.getMemoryMonitor());
        edgeGatewayVO.setSpaceMonitor(edgeGateway.getSpaceMonitor());
        if(edgeGateway.getType() == EdgeGatewayTypeEnum.CONNECTOR_GATEWAY.getValue()){
            List<ConnectorEntity> connectorEntityList = connectorService.getConnectorByEdgeGatewayId(edgeGatewayId).getResult();
            if(CollectionUtil.isNotEmpty(connectorEntityList)){
                edgeGatewayVO.setConnectorEntityList(connectorEntityList);
            }
        }
        return Result.ok(edgeGatewayVO);
    }

    @Autowired
    LabelGroupMapper labelGroupMapper;

    @Override
    public Result<List<LabelGroupDto>> listLabelGroupByEdgeGateway(Long edgeGatewayId, TenantIsolation tenantIsolation) {
        List<LabelGroupDto> list = labelGroupMapper.listLabelGroupByEdgeGateway(edgeGatewayId,tenantIsolation);
        return Result.ok(list);
    }

    public Result<Boolean> edgeGatewayOnline(Long tenantId, Long edgeGatewayId){
        String timestampStr = stringRedisTemplate.opsForValue().get(RedisConstant.EDGE_GATEWAY_HEARTBEAT_PREFIX + tenantId + ":" + edgeGatewayId);
        if (StringUtils.isBlank(timestampStr)){
            return Result.ok(false, "无");
        }
        Long timeoutStamp = Long.parseLong(timestampStr);
        Long now = LocalDateTime.now()
                    .toInstant(ZoneOffset.of("+8"))
                    .toEpochMilli();
        LocalDateTime lastHeartbeat = LocalDateTime.ofEpochSecond(
            timeoutStamp/1000, 0, ZoneOffset.ofHours(8)
        ).minusSeconds(RedisConstant.HEARTBEAT_TTL);
        String lastHeartbeatStr = DatePattern.NORM_DATETIME_FORMATTER.format(lastHeartbeat);
        if(now <= timeoutStamp){
            return Result.ok(true, lastHeartbeatStr);
        }
        return Result.ok(false, lastHeartbeatStr);
    }

    @Override
    public Result<List<Channel>> listChannelByEdgeGatewayId(Long edgeGatewayId, TenantIsolation tenantIsolation) {
        List<Channel> channelWithLabelGroup = EdgeGateway.getChannelWithLabelGroup(edgeGatewayId, commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId()));
        return Result.ok(channelWithLabelGroup);
    }

    @Override
    public Result<Void> setEdgeGatewayNotSyncByLabelIds(List<Long> labelIds) {
        edgeGatewayMapper.setEdgeGatewayNotSyncByLabelIds(labelIds);
        return Result.ok();
    }

    public void checkEdgeGatewayById(Long id, TenantIsolation tenantIsolation) {

        Result<Boolean> online = edgeGatewayOnline(tenantIsolation.getTenantId(), id);
        if (!online.getResult()) {
            throw new BizException("离线状态的网关无法启用");
        }

        Result<List<ChannelEntity>> channelListResult = channelService.listByEdgeGatewayId(id, tenantIsolation.getTenantId(), false);
        List<ChannelEntity> channelList = channelListResult.getResult();
        if (CollectionUtils.isEmpty(channelList)) {
            return;
        }

        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantIsolation.getTenantId());
        for (ChannelEntity channelEntity : channelList) {
            Result<Channel> channelResult = Channel.checkInfo(channelEntity, commonFetcher);
            if (!channelResult.getSignal()) {
                throw new BizException(channelResult.getMessage());
            }

            Channel channel = channelResult.getResult();
            Result<Void> checkLabelInfoResult = channel.checkLabelInfo(commonFetcher);
            if (!checkLabelInfoResult.getSignal() &&
                    !ServiceCodeEnum.CHANNEL_NO_LABEL_BINDING.getCode().equals(checkLabelInfoResult.getServiceCode())) {
                throw new BizException(checkLabelInfoResult.getMessage());
            }
        }
    }

    
    @Override
    @Transactional
    public Result<Void> syncCustomDriver(TenantIsolation tenant, Long edgeGatewayId) {
        
        Result<Boolean> onlineResult = edgeGatewayOnline(tenant.getTenantId(), edgeGatewayId);
        if(!onlineResult.getSignal()){
            return Result.error("网关在线检测异常:" + onlineResult.getMessage());
        }
        if(!onlineResult.getResult()){
            return Result.error("操作失败：网关离线");
        }
        
        //更新自定义协议运行时
        Result<Integer> updateCustomDriverRuntimeInfoResult = taskService.updateCustomDriverRuntimeInfo(tenant.getTenantId());
        if (!updateCustomDriverRuntimeInfoResult.getSignal()){
            throw new BizException(updateCustomDriverRuntimeInfoResult.getMessage());
        }

        Result<Void> syncCustomDriverResult = taskService.syncCustomDriver(tenant, edgeGatewayId);
        if (!syncCustomDriverResult.getSignal()){
            throw new BizException(syncCustomDriverResult.getMessage());
        }
        return Result.ok();
    }

    @Override
    @Transactional
    @AuditLog(action = ActionEnum.SYNC,target = AuditTargetEnum.GATEWAY,details = "根据id进行网关同步")
    public Result<Void> edgeGatewaySyncById(Long id, TenantIsolation tenantIsolation) {
        EdgeGatewayEntity edgeGateway = this.getByIdAndTenantIsolation(id, tenantIsolation).getResult();
        if (edgeGateway == null){
            throw new BizException("该租户下不存在此网关");
        }
        taskService.updateChannelRuntimeInfoByEdgeGatewayId(tenantIsolation.getTenantId(), id);
        Result<String> result = this.syncLabel(edgeGateway, tenantIsolation);
        if (result.getSignal()) {
            EdgeGatewayEntity edgeGatewayEntity = edgeGatewayMapper.selectById(id);
            edgeGatewayEntity.setId(id);
            edgeGatewayEntity.setSyncStatus(SyncStatusEnum.HAVE_SYNC.getValue());
            edgeGatewayEntity.setSyncTime(LocalDateTime.now());
            edgeGatewayMapper.updateById(edgeGatewayEntity);
            String stateKey = String.format(RedisConstant.OT_GATEWAY_CHANGE_STATE, id);
            EdgeGatewayChangeState state =  EdgeGatewayChangeState.builder().isEdgeGatewayChange(false).changeTime(LocalDateTime.now()).build();
            stringRedisTemplate.opsForValue().set(stateKey,JSON.toJSONString(state));
        }
        return Result.ok();
    }

    @Autowired
    ChannelParamMapper channelParamMapper;

    @Autowired
    CustomDriverMapper customDriverMapper;

    @Getter
    @Value("${otaDownloadUrl}")
    private String otaDownloadUrl;


    private Result<String> syncLabel(EdgeGatewayEntity edgeGateway, TenantIsolation tenantIsolation) {
        log.info("同步标签配置到边缘网关");
        Result<List<ChannelEntity>> channelResult = channelService.listByEdgeGatewayId(edgeGateway.getId(), tenantIsolation.getTenantId(), false);
        List<SyncChannelDto> syncChannelDtos = null;
        List<SyncChannelParamDto> channelParamDtos = null;
        if (channelResult.getSignal()) {
            syncChannelDtos = BeanUtilsIntensifier.copyBeanList(channelResult.getResult(), SyncChannelDto.class);
        }
        if (CollectionUtil.isNotEmpty(syncChannelDtos)) {
            List<ChannelParamEntity> list = new LambdaQueryChainWrapper<>(channelParamMapper)
                    .in(ChannelParamEntity::getChannelId, BeanUtilsIntensifier.getIds(syncChannelDtos, SyncChannelDto::getId))
                    .list();
            channelParamDtos = BeanUtilsIntensifier.copyBeanList(list, SyncChannelParamDto.class);
        }
        Result<List<LabelGroupDto>> groupResult = listLabelGroupByEdgeGateway(edgeGateway.getId(), tenantIsolation);
        List<SyncLabelGroupDto> groupDtos = null;
        if (groupResult.getSignal()) {
            groupDtos = BeanUtilsIntensifier.copyBeanList(groupResult.getResult(),SyncLabelGroupDto.class);
        }
        List<LabelEntity> labelEntities = null;
        if (CollectionUtil.isNotEmpty(groupDtos)) {
             labelEntities = new LambdaQueryChainWrapper<>(labelMapper)
                    .in(LabelEntity::getLabelGroupId, BeanUtilsIntensifier.getIds(groupDtos, SyncLabelGroupDto::getId))
                    .list();
        }
        List<SyncCustomContentDto> customContentDtos = null;
        List<CustomDriverRuntimeInfoField> customDriverRuntimeInfoFields = customDriverMapper.listAllEnabledRuntimeInfo(tenantIsolation.getTenantId());
        if (CollectionUtil.isNotEmpty(customDriverRuntimeInfoFields)) {
            customContentDtos = customDriverRuntimeInfoFields.stream().map(customDriverRuntimeInfoField -> {
                return SyncCustomContentDto.builder()
                        .name(customDriverRuntimeInfoField.getDriverName())
                        .content(JSONObject.toJSONString(customDriverRuntimeInfoField))
                        .build();
            }).collect(Collectors.toList());
        }
        String downloadUrl = null;
        String md5Proofread = null;
        String operators = null;
        if (!org.springframework.util.StringUtils.isEmpty(edgeGateway.getTargetVersion())) {
            Result<UpgradePackageEntity> packageEntityResult = upgradePackageService.getUpgradePackageEntity(edgeGateway.getTargetVersion());
            if (packageEntityResult.getSignal() && Objects.nonNull(packageEntityResult.getResult())) {
                UpgradePackageEntity upgradePackageEntity = packageEntityResult.getResult();
                downloadUrl = otaDownloadUrl + upgradePackageEntity.getId() + Constant.ZIP_SUFFIX;
                md5Proofread = upgradePackageEntity.getMd5Proofread();
            }
        }
        if (Objects.nonNull(edgeGateway.getOperators())) {
            operators = edgeGateway.getOperators().toString();
        }
        SyncEdgeGatewayDto syncEdgeGatewayDto = SyncEdgeGatewayDto.builder()
                .operators(operators)
                .upgradeVersion(edgeGateway.getTargetVersion())
                .md5Proofread(md5Proofread)
                .downloadUrl(downloadUrl)
                .traffic5gAppKey(traffic5gAppKey)
                .traffic5gAppScrect(traffic5gAppScrect)
                .trafficCard(edgeGateway.getTrafficCard())
                .channelDtoList(syncChannelDtos)
                .channelParamDtoList(channelParamDtos)
                .labelDtoList(BeanUtilsIntensifier.copyBeanList(labelEntities, SyncLabelDto.class))
                .labelGroupDtoList(groupDtos)
                .customContentDtoList(customContentDtos)
                .memoryMonitor(edgeGateway.getMemoryMonitor())
                .spaceMonitor(edgeGateway.getSpaceMonitor())
                .build();
        return edgeGatewayControlProxy.otSyncData(edgeGateway.getId(), tenantIsolation.getTenantId(), syncEdgeGatewayDto);
    }

    public Result<Void> updateGatewayRunTimeInfo(Long id, TenantIsolation tenantIsolation){
        Result<EdgeGatewayRuntimeInfoField> runTimeInfo = calculationGatewayDevices(id,tenantIsolation);
        EdgeGatewayEntity edgeGatewayEntity = edgeGatewayMapper.selectById(id);
        edgeGatewayEntity.setId(id);
        edgeGatewayEntity.setRuntimeInfo(runTimeInfo.getResult());
        edgeGatewayMapper.updateById(edgeGatewayEntity);
        return Result.ok();
    }

    @Override
    public Result<List<EdgeGatewayEntity>> getAllEdgeGateway() {
        List<EdgeGatewayEntity> edgeGatewayEntityList= edgeGatewayMapper.getAllEdgeGateway();
        return Result.ok(edgeGatewayEntityList);
    }

    @Override
    public Result<List<EdgeGatewayEntity>> getAllEdgeGatewayByTenantId(Long tenantId) {
        List<EdgeGatewayEntity> edgeGatewayEntityList= edgeGatewayMapper.listAll(tenantId);
        return Result.ok(edgeGatewayEntityList);
    }

    @Override
    public Result<List<DcmpEdgeGatewayVO>> listEdgeGatewayForDcmp(TenantIsolation tenantIsolation, ListDcmpEdgeGatewayRequest request) {
        LambdaQueryWrapper<EdgeGatewayEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(EdgeGatewayEntity::getTenantId,tenantIsolation.getTenantId())
                .like(StringUtils.isNotBlank(request.getEdgeGatewayName()),EdgeGatewayEntity::getName,request.getEdgeGatewayName());
        List<EdgeGatewayEntity> list = edgeGatewayMapper.selectList(lqw);
        log.info("获取当前租户网关信息:{}",JSONObject.toJSONString(list));
        if (CollectionUtils.isEmpty(list)){
            return Result.ok();
        }
        List<DcmpEdgeGatewayVO> dcmpEdgeGatewayVOS = BeanUtilsIntensifier.copyBeanList(list, DcmpEdgeGatewayVO.class);
        // 获取网关在线和离线状态
        IdListDTO idListDTO = new IdListDTO();
        List<Long> idList = list.stream().map(i -> i.getId()).collect(Collectors.toList());
        idListDTO.setIdList(idList);
        Result<List<EdgeGatewayCurrentTimeInfoVO>> listResult = this.listEdgeGatewayCurrentTimeInfo(idListDTO, tenantIsolation);
        log.info("获取网关在线和离线状态:{}",JSONObject.toJSONString(listResult));
        Map<Long, List<TagRsp>> tagMap = tagBindRelationService.getTagListGroupByTargetId(tenantIsolation.getTenantId(), ResourceTypeEnum.EDGE_GATEWAY, idList);
        if (CollectionUtil.isNotEmpty(listResult.getResult())){
            Map<Long, Integer> collect = listResult.getResult().stream().collect(Collectors.toMap(i -> i.getId(), i -> i.getOnline()));
            for (DcmpEdgeGatewayVO dcmpEdgeGatewayVO : dcmpEdgeGatewayVOS) {
                dcmpEdgeGatewayVO.setOnline(collect.getOrDefault(dcmpEdgeGatewayVO.getId(),0));
                dcmpEdgeGatewayVO.setTags(tagMap.get(dcmpEdgeGatewayVO.getId()));
            }
        }

        return Result.ok(dcmpEdgeGatewayVOS);
    }
    
    @Override
    public Result<Void> updateEdgeGatewayInfo(EditEdgeGatewayDTO dto, TenantIsolation tenantIsolation) {
        if (ObjectUtil.isEmpty(dto.getId()) || ObjectUtil.isEmpty(dto.getName()) || ObjectUtil.isEmpty(tenantIsolation.getTenantId())) {
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantIsolation.getTenantId());
        UniqueConstraint nameUniqueConstraint = new UniqueConstraint("name");
        //名称唯一性检查，要排除自己
        UniqueConstraint.Unique nameUnique = nameUniqueConstraint.buildUnique(new FieldValue(dto.getName()));
        EdgeGatewayEntity sameNameEntity = commonFetcher.get(nameUnique, EdgeGatewayEntity.class);
        if(sameNameEntity != null){
            if (!sameNameEntity.getId().equals(dto.getId())) {
                return Result.error("网关名称重复");
            }else {
                return Result.ok();
            }
        }
        boolean update = new LambdaUpdateChainWrapper<>(edgeGatewayMapper)
                .eq(EdgeGatewayEntity::getTenantId, tenantIsolation.getTenantId())
                .eq(EdgeGatewayEntity::getId, dto.getId())
                .set(EdgeGatewayEntity::getName, dto.getName())
                .update();
        if (update) {
            return Result.ok();
        }else {
            return Result.error(ServiceCodeEnum.CODE_UPDATE_FAIL);
        }
    }

    @Override
    @Transactional
    public Result<Void> batchEdgeGatewaySync(EdgeGatewayDto edgeGatewayDto,TenantIsolation tenantIsolation) {
        LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapper<EdgeGatewayEntity> lqw = new LambdaQueryWrapper<>();
        lqw.like(StringUtils.isNotBlank(edgeGatewayDto.getName()),EdgeGatewayEntity::getName,edgeGatewayDto.getName())
                .eq(edgeGatewayDto.getType() != null ,EdgeGatewayEntity::getType,edgeGatewayDto.getType())
                .eq(EdgeGatewayEntity::getTenantId ,tenantIsolation.getTenantId());
        List<EdgeGatewayEntity> edgeGateways = edgeGatewayMapper.selectList(lqw);
        StringBuilder errorMsg = new StringBuilder();
        for (EdgeGatewayEntity edgeGateway : edgeGateways) {
            Result<Integer> integerResult = channelService.countByEdgeGatewayId(edgeGateway.getId());
            if (integerResult.getResult() == null || integerResult.getResult().compareTo(1)<0){
                errorMsg.append("网关：【")
                        .append(edgeGateway.getName())
                        .append("】未绑定通道，无法同步\n");
                continue;
            }

            Result<List<GatherParamField>> updateGatherTaskResult = taskService.updateGatherParamByEdgeGatewayId(tenantIsolation.getTenantId(), edgeGatewayDto.getId());
            if (!updateGatherTaskResult.getSignal()){
                throw new BizException(updateGatherTaskResult.getMessage());
            }
            List<GatherParamField> gatherParamList = updateGatherTaskResult.getResult();
            labelService.batchUpdateGatherParam(tenantIsolation.getTenantId(), gatherParamList);
            
            Result<Void> syncTaskResult = taskService.syncTask(tenantIsolation, edgeGateway.getId());
            if (!syncTaskResult.getSignal()){
                throw new BizException(syncTaskResult.getMessage());
            }
            Result<EdgeGatewayRuntimeInfoField> runTimeInfo = calculationGatewayDevices(edgeGateway.getId(),tenantIsolation);
            EdgeGatewayEntity edgeGatewayEntity =  edgeGatewayMapper.selectById(edgeGateway.getId());
            edgeGatewayEntity.setId(edgeGateway.getId());
            edgeGatewayEntity.setSyncTime(now);
            edgeGatewayEntity.setRuntimeInfo(runTimeInfo.getResult());
            edgeGatewayMapper.updateById(edgeGatewayEntity);
        }

        if (errorMsg.length() > 0){
            return Result.error(errorMsg.toString());
        }
        return Result.ok();
    }

    @Override
    @GatewayChange(value = "#edgeGatewayId",state = true)
    public Result<Void> setNotSyncById(Long edgeGatewayId) {
        if (edgeGatewayId == null){
            return Result.ok();
        }
        return Result.ok();
    }

    @Override
    public Result<Void> setEdgeGatewayNotSyncByChannelId(Long channelId) {
        Result<ChannelEntity> channelResult = channelService.getChannelById(channelId);
        if (channelResult.getSignal() && channelResult.getResult() != null ){
            Long edgeGatewayId = channelResult.getResult().getEdgeGatewayId();
            if (edgeGatewayId != null){
                EdgeGatewayServiceImpl impl = ApplicationContextUtil.getBean("edgeGatewayServiceImpl", EdgeGatewayServiceImpl.class);
                impl.setNotSyncById(edgeGatewayId);
            }
        }
        return Result.ok();
    }

    @Override
    public Result<Void> setEdgeGatewayNotSyncByLabelGroupId(Long labelGroupId) {
        Result<ChannelEntity> channelResult = channelService.getByLabelGroupId(labelGroupId);
        if (channelResult.getSignal() && channelResult.getResult() != null ){
            Long edgeGatewayId = channelResult.getResult().getEdgeGatewayId();
            if (edgeGatewayId != null){
                EdgeGatewayServiceImpl impl = ApplicationContextUtil.getBean("edgeGatewayServiceImpl", EdgeGatewayServiceImpl.class);
                impl.setNotSyncById(edgeGatewayId);
            }
        }
        return Result.ok();
    }

    @Override
    public Result<List<EdgeGatewayCurrentTimeInfoVO>> listEdgeGatewayCurrentTimeInfo(IdListDTO dto, TenantIsolation tenantIsolation) {
        Set<Long> edgeGatewayIdList = new HashSet<>(dto.getIdList());
        List<EdgeGatewayVO> edgeGatewayVOList = edgeGatewayMapper.listByIds(edgeGatewayIdList,tenantIsolation.getTenantId());
        if(CollectionUtil.isEmpty(edgeGatewayVOList)){
            return Result.ok(new ArrayList<>());
        }
        Map<Long,String> targetVersionMap = edgeGatewayVOList.stream().collect(HashMap::new,(map,item)->map.put(item.getId(),item.getTargetVersion()),HashMap::putAll);
        Map<Long, List<ConnectorEntity>> connectorEntityMap = connectorService.getAllConnectorEntity().getResult().stream().collect(Collectors.groupingBy(ConnectorEntity::getEdgeGatewayId));
        Map<Long,EdgeGatewayVO> edgeGatewayVOMap = edgeGatewayVOList.stream().collect(HashMap::new,(map,item)->map.put(item.getId(),item),HashMap::putAll);

        List<Long> idList = dto.getIdList();
        ArrayList<EdgeGatewayCurrentTimeInfoVO> edgeGatewayOnlineList = new ArrayList<>();
        for (Long id : idList) {

            EdgeGatewayCurrentTimeInfoVO edgeGatewayOnlineVO = new EdgeGatewayCurrentTimeInfoVO();
            edgeGatewayOnlineVO.setId(id);
            EdgeGatewayVO edgeGatewayVO = edgeGatewayVOMap.get(id);
            Long connectorId = 0L;
            Result<Boolean> online = null;
            if(edgeGatewayVO.getType() == EdgeGatewayTypeEnum.CONNECTOR_GATEWAY.getValue()){
                List<ConnectorEntity> connectorEntityList = connectorEntityMap.get(edgeGatewayVO.getId());
                if(CollectionUtil.isNotEmpty(connectorEntityList)){
                    connectorId = connectorEntityList.get(0).getId();
                    online = connectorInstanceLoader.getConnectStatus(connectorId);
                }
            }else {
               online = edgeGatewayOnline(tenantIsolation.getTenantId(), id);
            }

            if (online != null && online.getResult()){
                edgeGatewayOnlineVO.setOnline(1);
                edgeGatewayOnlineVO.setHeartbeatTime(online.getMessage());

            }else{
                edgeGatewayOnlineVO.setOnline(0);
//                log.info("online message is {}",online.getMessage());
//                edgeGatewayOnlineVO.setHeartbeatTime(online.getMessage());
            }
            String edgeChangeStr = stringRedisTemplate.opsForValue().get(String.format(RedisConstant.EDGE_GATEWAY_CHANGE_STATE, id));
            String otChangeStr = stringRedisTemplate.opsForValue().get(String.format(RedisConstant.OT_GATEWAY_CHANGE_STATE, id));
            EdgeGatewayChangeState newChangeState = EdgeGatewayChangeState.builder().isEdgeGatewayChange(false).build();
            EdgeGatewayChangeState edgeGatewayChangeState = newChangeState;
            EdgeGatewayChangeState otChangeState = newChangeState;
            if (Objects.nonNull(edgeChangeStr)) {
                edgeGatewayChangeState = JSONObject.parseObject(edgeChangeStr,EdgeGatewayChangeState.class);
            }
            if (Objects.nonNull(otChangeStr)) {
                otChangeState = JSONObject.parseObject(otChangeStr,EdgeGatewayChangeState.class);
            }
            edgeGatewayOnlineVO.setEdgeGatewayChangeState(edgeGatewayChangeState);
            edgeGatewayOnlineVO.setOtGatewayChangeState(otChangeState);
            String gather = stringRedisTemplate.opsForValue().get(RedisConstant.GATHER_LABEL_COUNT_PREFIX + tenantIsolation.getTenantId() + ":" + id);
            if(StringUtils.isNotBlank(gather)){
                edgeGatewayOnlineVO.setGatherLabelCount(gather);
            }else {
                edgeGatewayOnlineVO.setGatherLabelCount("0");
            }

            String compute = stringRedisTemplate.opsForValue().get(RedisConstant.COMPUTE_TASK_COUNT_PREFIX + tenantIsolation.getTenantId() + ":" + id);
            if(StringUtils.isNotBlank(compute)){
                edgeGatewayOnlineVO.setComputeTaskCount(compute);
            }else {
                edgeGatewayOnlineVO.setComputeTaskCount("0");
            }

            String gathering = stringRedisTemplate.opsForValue().get(RedisConstant.GATHERING_PREFIX + tenantIsolation.getTenantId() + ":" + id);
            if(StringUtils.isNotBlank(gathering)){
                edgeGatewayOnlineVO.setGathering(Boolean.parseBoolean(gathering));
            }else {
                edgeGatewayOnlineVO.setGathering(false);
            }
            if(targetVersionMap.get(id) != null){
                String targetVersion = targetVersionMap.get(id);
                Boolean newVersion = haveNewVersion(targetVersion,id,tenantIsolation.getTenantId()).getResult();
                edgeGatewayOnlineVO.setNewVersion(newVersion);
            }else {
                edgeGatewayOnlineVO.setNewVersion(false);
            }
            edgeGatewayOnlineList.add(edgeGatewayOnlineVO);
        }
        return Result.ok(edgeGatewayOnlineList);
    }

    private Result<Void> uniqueName(String name, TenantIsolation tenantIsolation) {
        return this.uniqueName(null, name, tenantIsolation);
    }

    /**
     * 判断名称是否唯一
     *
     * @param id
     * @param name
     * @param tenantIsolation
     * @return
     */
    private Result<Void> uniqueName(Long id, String name, TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<EdgeGatewayEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(id != null, EdgeGatewayEntity::getId, id)
                .eq(EdgeGatewayEntity::getName, name)
                .eq(EdgeGatewayEntity::getTenantId, tenantIsolation.getTenantId());

        if (edgeGatewayMapper.selectCount(lqw) > 0) {
            return Result.error("已经存在该名称的网关,名称：" + name);
        }

        return Result.ok();
    }



    private Result<Void> uniqueHostAndPort(String host,Integer port, TenantIsolation tenantIsolation) {
        return this.uniqueHostAndPort(null, host, port,tenantIsolation);
    }

    /**
     * 判断host和port是否唯一
     *
     * @param id
     * @param host
     * @param tenantIsolation
     * @return
     */
    private Result<Void> uniqueHostAndPort(Long id, String host,Integer port,TenantIsolation tenantIsolation) {
        LambdaQueryWrapper<EdgeGatewayEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(id != null, EdgeGatewayEntity::getId, id)
                .eq(EdgeGatewayEntity::getPort, port)
                .eq(EdgeGatewayEntity::getHost, host)
                .eq(EdgeGatewayEntity::getTenantId, tenantIsolation.getTenantId());

        if (edgeGatewayMapper.selectCount(lqw) > 0) {
            return Result.error("已经存在【"+host+":"+port+"】的网关");
        }

        return Result.ok();
    }

    @Override
    public Result<String> heartbeat(TenantIsolation tenant, Long edgeGatewayId, HeartbeatInfo info) {
        //校验uuid是否一致
        String realHeartbeatUuid = edgeGatewayCacheService.getHeartbeatUuid(tenant.getTenantId(), edgeGatewayId);
        if(realHeartbeatUuid == null){
            return Result.error("heartbeatUuid不一致");
        }
        //不一致
        if(!realHeartbeatUuid.equals(info.getHeartbeatUuid())){
            return Result.error("heartbeatUuid不一致");
        }

        //一致，保存心跳信息
        Long timeoutTimestamp = LocalDateTime.now()
                                    .plusSeconds(RedisConstant.HEARTBEAT_TTL)
                                    .toInstant(ZoneOffset.of("+8"))
                                    .toEpochMilli();
        stringRedisTemplate.opsForValue().set(
            RedisConstant.EDGE_GATEWAY_HEARTBEAT_PREFIX + tenant.getTenantId() + ":" + edgeGatewayId, 
            timeoutTimestamp.toString()
        );
        stringRedisTemplate.opsForValue().set(
            RedisConstant.GATHER_LABEL_COUNT_PREFIX + tenant.getTenantId() + ":" + edgeGatewayId,
            info.getGatherLabelCount().toString(),
            RedisConstant.HEARTBEAT_TTL,
            TimeUnit.SECONDS
        );
        if(info.getGathering() == null){
            info.setGathering(false);
        }
        stringRedisTemplate.opsForValue().set(
            RedisConstant.GATHERING_PREFIX + tenant.getTenantId() + ":" + edgeGatewayId,
            info.getGathering().toString(),
            RedisConstant.HEARTBEAT_TTL,
            TimeUnit.SECONDS
        );
        stringRedisTemplate.opsForValue().set(
                RedisConstant.EDGE_GATEWAY_CHANNEL_STATUS_PREFIX + tenant.getTenantId() + ":" + edgeGatewayId,
                JSONObject.toJSONString(info.getChannelStatusMap()),
                RedisConstant.CHANNEL_STATUS_TTL,
                TimeUnit.SECONDS
        );
        if(StringUtils.isNotEmpty(info.getCurrentVersion())){
            stringRedisTemplate.opsForValue().set(
                RedisConstant.CURRENT_VERSION_PREFIX + tenant.getTenantId() + ":" + edgeGatewayId,
                info.getCurrentVersion(),
                RedisConstant.HEARTBEAT_TTL,
                TimeUnit.SECONDS
            );
        }
        if(StringUtils.isNotEmpty(info.getDownloadStatus())){
            stringRedisTemplate.opsForValue().set(
                RedisConstant.DOWNLOAD_STATUS + tenant.getTenantId() + ":" + edgeGatewayId,
                info.getDownloadStatus(),
                RedisConstant.HEARTBEAT_TTL,
                TimeUnit.SECONDS
            );
        }
        if(StringUtils.isNotEmpty(info.getUpgradeVersion())){
            stringRedisTemplate.opsForValue().set(
                RedisConstant.DOWNLOAD_UPGRADE_VERSION + tenant.getTenantId() + ":" + edgeGatewayId,
                info.getUpgradeVersion(),
                RedisConstant.HEARTBEAT_TTL,
                TimeUnit.SECONDS
            );
        }
        if(StringUtils.isNotEmpty(info.getInstance())){
            stringRedisTemplate.opsForValue().set(
                RedisConstant.INSTANCE + tenant.getTenantId() + ":" + edgeGatewayId,                info.getInstance(),
                RedisConstant.HEARTBEAT_TTL,
                TimeUnit.SECONDS
            );
        }
        if (Objects.nonNull(info.getEdgeGatewayChangeState()) && Objects.nonNull(info.getEdgeGatewayChangeState().getIsEdgeGatewayChange()) && Objects.nonNull(info.getEdgeGatewayChangeState().getChangeTime())) {
            stringRedisTemplate.opsForValue().set(
                String.format(RedisConstant.EDGE_GATEWAY_CHANGE_STATE,edgeGatewayId), JSON.toJSONString(info.getEdgeGatewayChangeState()),
                RedisConstant.HEARTBEAT_TTL,
                TimeUnit.SECONDS
            );
        }
        String s = stringRedisTemplate.opsForValue().get(String.format(RedisConstant.OT_GATEWAY_CHANGE_STATE, edgeGatewayId));
        return Result.ok(s);
    }

    @Override
    @AuditLog(action = ActionEnum.UPDATE,target = AuditTargetEnum.GATEWAY,details = "开启网关采集")
    public Result<Void> startGather(Long edgeGatewayId, TenantIsolation tenant) {
        return edgeGatewayControlProxy.startProxy(edgeGatewayId, tenant.getTenantId());
    }

    @Override
    @AuditLog(action = ActionEnum.UPDATE,target = AuditTargetEnum.GATEWAY,details = "关闭网关采集")
    public Result<Void> stopGather(Long edgeGatewayId, TenantIsolation tenant) {
        return edgeGatewayControlProxy.stopProxy(edgeGatewayId, tenant.getTenantId());
    }

    @Override
    public Result<EdgeGatewayEntity> edgeGatewayDeleteCheck(Long id, TenantIsolation tenantIsolation) {
        EdgeGatewayEntity edgeGateway = this.getByIdAndTenantIsolation(id, tenantIsolation).getResult();
        if (edgeGateway == null){
            return Result.error("该租户下不存在此网关");
        }

        Result<Integer> deviceCountResult = deviceService.countByEdgeGatewayId(id);
        if (deviceCountResult.getResult() > 0){
            return Result.error("该网关已经被设备使用，无法删除");
        }
        return Result.ok(edgeGateway);
    }

    @Override
    public Result<EdgeGatewayRuntimeInfoField> calculationGatewayDevices(Long edgeGatewayId, TenantIsolation tenantIsolation) {
        EdgeGatewayRuntimeInfoField edgeGatewayRuntimeInfoField = new EdgeGatewayRuntimeInfoField();
        List<DeviceChannelBo> deviceChannelBoList = deviceService.listGatewayDevices(edgeGatewayId,tenantIsolation).getResult();
        if(CollectionUtil.isEmpty(deviceChannelBoList)){
            return Result.ok(edgeGatewayRuntimeInfoField);
        }
        Map<String,List<Long>> channelDeviceIdMap = deviceChannelBoList.stream().collect(Collectors.groupingBy(e -> String.valueOf(e.getChannelId()),Collectors.mapping(DeviceChannelBo ::getDeviceId,Collectors.toList())));
        edgeGatewayRuntimeInfoField.setChannelDeviceIds(channelDeviceIdMap);
        return Result.ok(edgeGatewayRuntimeInfoField);
    }

    @Override
    public Result<DcmpEdgeGatewayVO> getEdgeGatewayForDcmp(TenantIsolation tenantIsolation, GetEdgeGatewayRequest request) {
        List<EdgeGatewayEntity> list = new LambdaQueryChainWrapper<>(edgeGatewayMapper)
                .eq(request.getEdgeGatewayId() != null, EdgeGatewayEntity::getId, request.getEdgeGatewayId())
                .eq(StringUtils.isNotBlank(request.getImei()), EdgeGatewayEntity::getImei, request.getImei())
                .eq(EdgeGatewayEntity::getTenantId, tenantIsolation.getTenantId()).list();
        if (CollectionUtil.isEmpty(list)){
            return Result.error("该租户下，不存在此网关");
        }
        return Result.ok(BeanUtilsIntensifier.copyBean(list.get(0),DcmpEdgeGatewayVO.class));
    }
    
    @Override
    public Result<Page<EdgeGatewayVersionVO>> listEdgeGatewayVersion(PageParam pageParam,VersionQueryDTO versionQueryDTO,Long tenantId) {
        List<Long> edgeGatewayIds = new ArrayList<>();
        if (versionQueryDTO.getOnline() != null && versionQueryDTO.getOnline() != -1) {
            List<EdgeGatewayEntity> edgeGatewayEntityList = edgeGatewayMapper.getAllEdgeGateway();
            if(CollectionUtil.isEmpty(edgeGatewayEntityList)){
                edgeGatewayIds.add(-1L);
            }
            Map<Long, List<EdgeGatewayEntity>> groupBy = edgeGatewayEntityList.stream().filter(e->e.getTenantId()!=null).collect(Collectors.groupingBy(EdgeGatewayEntity::getTenantId));
            for (Map.Entry<Long, List<EdgeGatewayEntity>> entry : groupBy.entrySet()) {
                List<Long> ids = getPagedEdgeGatewayIds(entry.getKey(), versionQueryDTO.getOnline());
                edgeGatewayIds.addAll(ids);
            }
            versionQueryDTO.setIdList(edgeGatewayIds);
            versionQueryDTO.setType(1);
        }
        Page<EdgeGatewayEntity> edgeGatewayEntityPage = edgeGatewayMapper.pageEdgeGatewayEntity(pageParam.toPage(EdgeGatewayEntity.class),versionQueryDTO);
        Page<EdgeGatewayVersionVO> packageVOPage = new Page<>();
        packageVOPage.setTotal(edgeGatewayEntityPage.getTotal());
        packageVOPage.setCurrent(edgeGatewayEntityPage.getCurrent());
        packageVOPage.setSize(edgeGatewayEntityPage.getSize());
        packageVOPage.setPages(edgeGatewayEntityPage.getPages());
        if(CollectionUtil.isEmpty(edgeGatewayEntityPage.getRecords())){
            packageVOPage.setRecords(new ArrayList<>());
            return Result.ok(packageVOPage);
        }
         Map<Long,String> tenantMap = getTenantMap(tenantId).getResult();
        List<EdgeGatewayVersionVO> edgeGatewayVersionVOList = new ArrayList<>();
        for(EdgeGatewayEntity edgeGatewayEntity : edgeGatewayEntityPage.getRecords()){
            EdgeGatewayVersionVO edgeGatewayVersionVO = new EdgeGatewayVersionVO();
            BeanUtil.copyProperties(edgeGatewayEntity,edgeGatewayVersionVO);
            String currentVersion = edgeGatewayCurrentVersion(edgeGatewayEntity.getId(),edgeGatewayEntity.getTenantId()).getResult();
            edgeGatewayVersionVO.setCurrentVersion(currentVersion);
             if(tenantMap.get(edgeGatewayEntity.getTenantId()) != null){
                 edgeGatewayVersionVO.setTenantName(tenantMap.get(edgeGatewayEntity.getTenantId()));
             }else {
                 edgeGatewayVersionVO.setTenantName("-");
             }
            EdgeGatewayOTAInfoVO edgeGatewayOTAInfoVO = checkEdgeGatewayOTAInfoVO(edgeGatewayEntity,currentVersion).getResult();
            edgeGatewayVersionVO.setUpgradeStatus(edgeGatewayOTAInfoVO.getUpgradeStatus());
            edgeGatewayVersionVO.setOnline(edgeGatewayOTAInfoVO.getOnline());
            edgeGatewayVersionVO.setDownloadStatus(edgeGatewayOTAInfoVO.getDownloadStatus());
            edgeGatewayVersionVOList.add(edgeGatewayVersionVO);
        }
        packageVOPage.setRecords(edgeGatewayVersionVOList);
        return Result.ok(packageVOPage);
    }


    @Value("${user-center.base-url}")
    private String baseUrl;

    @Value("${user-center.tenant-list}")
    private String tenantListUrl;
    @Autowired
    private RestTemplate restTemplate;

    @Override
    public Result<Map<Long,String>> getTenantMap(Long tenantId) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/json; charset=UTF-8"));
            headers.add(Constant.LOGIN_TOKEN, JwtUserInfoUtils.getAuthorization());
            headers.add("clientId", tenantId.toString());
            headers.add("appcode", Constant.APPCODE_HEADER);

            Map<String, String> map = new HashMap<>();
            HttpEntity<String> httpEntity = new HttpEntity<>(JSON.toJSONString(map), headers);
            String result = restTemplate.postForEntity(baseUrl + tenantListUrl, httpEntity, String.class).getBody();
            log.info("获取租户列表返回结果：{}", result);
            if (StringUtils.isNotEmpty(result)) {
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (jsonObject.getInteger("code") == 200) {
                    List<TenantVo> tenantList = JSONArray.parseArray(jsonObject.get("data").toString(), TenantVo.class);
                    Map<Long, String> tenantMap = tenantList.stream().collect(Collectors.toMap(i -> Long.parseLong(i.getClientId()), i -> i.getClientName()));
                    return Result.ok(tenantMap);
                } else {
                    log.error("获取租户列表失败：{}", jsonObject.getString("message"));
                    return Result.error(jsonObject.getString("message"));
                }
            } else {
                log.error("获取租户列表失败");
                return Result.error("获取租户列表失败");
            }
        }catch (Exception e){
            log.error("获取租户列表异常：{}", e.getMessage());
            return Result.error(e.getMessage());
        }
    }
    
    @Override
    public Result<EdgeGatewayOTAInfoVO> getOtaInfo(Long edgeGatewayId, TenantIsolation tenantIsolation) {
        EdgeGatewayEntity edgeGatewayEntity = edgeGatewayMapper.getById(tenantIsolation.getTenantId(),edgeGatewayId);
        if(edgeGatewayEntity.getType() == EdgeGatewayTypeEnum.CONNECTOR_GATEWAY.getValue()){
            return Result.ok();
        }
        Result<String> currentVersionResult = edgeGatewayCurrentVersion(edgeGatewayId,tenantIsolation.getTenantId());
        return checkEdgeGatewayOTAInfoVO(edgeGatewayEntity,currentVersionResult.getResult());
    }
    
    public Result<EdgeGatewayOTAInfoVO> checkEdgeGatewayOTAInfoVO(EdgeGatewayEntity edgeGatewayEntity,String currentVersion){
        EdgeGatewayOTAInfoVO edgeGatewayOTAInfoVO = new EdgeGatewayOTAInfoVO();
        edgeGatewayOTAInfoVO.setId(edgeGatewayEntity.getId());
        edgeGatewayOTAInfoVO.setCurrentVersion(currentVersion);
        edgeGatewayOTAInfoVO.setUpgradeStatus(edgeGatewayEntity.getUpgradeStatus());
        edgeGatewayOTAInfoVO.setTargetVersion(edgeGatewayEntity.getTargetVersion());
        Boolean online = edgeGatewayOnline(edgeGatewayEntity.getTenantId(),edgeGatewayEntity.getId()).getResult();
        edgeGatewayOTAInfoVO.setOnline(online);
        //如果当前版本等于目标版本
        if(currentVersion.equals(edgeGatewayEntity.getTargetVersion())){
            if(UpgradeStatusEnum.UPGRADE_STATUS_INIT.getValue().equals(edgeGatewayEntity.getUpgradeStatus())){
                edgeGatewayEntity.setUpgradeStatus(UpgradeStatusEnum.UPGRADE_STATUS_INIT.getValue());
                edgeGatewayMapper.updateById(edgeGatewayEntity);
            }
            if(edgeGatewayEntity.getTargetVersion() != null){
                edgeGatewayOTAInfoVO.setNewVersion(false);
            }
            edgeGatewayOTAInfoVO.setUpgradeStatus(UpgradeStatusEnum.UPGRADE_STATUS_INIT.getValue());
        }else {
            /**如果不相等**/
            String downloadUpgradeVersionStr = stringRedisTemplate.opsForValue().get(RedisConstant.DOWNLOAD_UPGRADE_VERSION + edgeGatewayEntity.getTenantId() + ":" + edgeGatewayEntity.getId());
            //如果跟网关上报的更新版本不一致，则前端置为下载状态
            if(StringUtils.isNotBlank(downloadUpgradeVersionStr) && !downloadUpgradeVersionStr.equals(edgeGatewayEntity.getTargetVersion())){
                edgeGatewayOTAInfoVO.setNewVersion(true);
                edgeGatewayOTAInfoVO.setUpgradeStatus(UpgradeStatusEnum.DOWNLOAD_STATUS.getValue());
                edgeGatewayOTAInfoVO.setDownloadStatus(Integer.parseInt(DownloadStatusEnum.DOWNLOAD_INIT.getValue()));
                return Result.ok(edgeGatewayOTAInfoVO);
            }else {
                if(ObjectUtil.isNull(edgeGatewayEntity.getTargetVersion())){
                    edgeGatewayOTAInfoVO.setNewVersion(false);
                    edgeGatewayOTAInfoVO.setUpgradeStatus(UpgradeStatusEnum.UPGRADE_STATUS_INIT.getValue());
                    return Result.ok(edgeGatewayOTAInfoVO);
                }
                edgeGatewayOTAInfoVO.setNewVersion(true);
                String downloadStatusStr = stringRedisTemplate.opsForValue().get(RedisConstant.DOWNLOAD_STATUS + edgeGatewayEntity.getTenantId() + ":" + edgeGatewayEntity.getId());
                DownloadStatusEnum downloadStatus = DownloadStatusEnum.typeOfValue(downloadStatusStr);
                if(!online || ObjectUtil.isNull(downloadStatus)){
                    return Result.ok(edgeGatewayOTAInfoVO);
                }
                Integer updateStatus = null;
                switch (downloadStatus) {
                    case DOWNLOAD_FAIL:
                    case UN_ZIP_FAIL:
                    case DOWNLOAD_INIT: {
                        //如果下载状态为初始化，则升级状态为下载提示
                        updateStatus = UpgradeStatusEnum.DOWNLOAD_STATUS.getValue();
                        break;
                    }
                    case DOWNLOADING: {
                        //如果下载状态为下载中，则升级状态为下载中
                        updateStatus = UpgradeStatusEnum.DO_DOWNLOAD_STATUS.getValue();
                        break;
                    }
                    case UN_ZIP_SUCCESSFUL: {
                        updateStatus = UpgradeStatusEnum.UPGRADE_STATUS.getValue();
                        // if(UpgradeStatusEnum.DO_UPGRADE_STATUS.getValue().equals(edgeGatewayEntity.getUpgradeStatus())){
                        //     //如果下载状态为下载成功，则升级状态为执行升级
                        //     updateStatus = UpgradeStatusEnum.UPGRADE_STATUS.getValue();
                        // }else {
                        //     updateStatus = edgeGatewayEntity.getUpgradeStatus();
                        // }
                        break;
                    }
//                    case DOWNLOAD_FAIL: {
//                        //如果下载状态为下载失败，则升级状态为置为初始化
//                        updateStatus = UpgradeStatusEnum.UPGRADE_STATUS_INIT.getValue();
//                        break;
//                    }
                    default:
                        break;
                }
                edgeGatewayOTAInfoVO.setUpgradeStatus(updateStatus);
                if(downloadStatus != null){
                    edgeGatewayOTAInfoVO.setDownloadStatus(Integer.parseInt(downloadStatus.getValue()));
                }
                if(updateStatus != null && updateStatus.equals(edgeGatewayEntity.getUpgradeStatus())){
                    edgeGatewayEntity.setUpgradeStatus(updateStatus);
                    edgeGatewayMapper.updateById(edgeGatewayEntity);
                }
            }
        }
        return Result.ok(edgeGatewayOTAInfoVO);
    }
    
    @Override
    public Result<List<EdgeGatewayEntity>> listEdgeGatewayByIds(Set<Long> ids) {
        return Result.ok(edgeGatewayMapper.listEdgeGatewayByIds(ids));
    }
    
    private Result<String> edgeGatewayCurrentVersion(Long edgeGatewayId,Long tenantId){
        String currentVersion = stringRedisTemplate.opsForValue().get(RedisConstant.CURRENT_VERSION_PREFIX + tenantId + ":" + edgeGatewayId);
        if(StringUtils.isNotBlank(currentVersion)){
            return Result.ok(currentVersion);
        }
        return Result.ok("");
    }
    
    private Result<Boolean> haveNewVersion(String targetVersion,Long edgeGatewayId,Long tenantId){
        String currentVersion = edgeGatewayCurrentVersion(edgeGatewayId,tenantId).getResult();
        if("".equals(targetVersion) || "".equals(currentVersion)){
            return Result.ok(false);
        }
        if(!targetVersion.equals(currentVersion)){
            return Result.ok(true);
        }
        return Result.ok(false);
    }
    
    @Override
    public Result<Traffic5gInfo> getTraffic5g(TenantIsolation tenantIsolation, Long edgeGatewayId) {
        Result<EdgeGatewayEntity> edgeGatewayEntityResult = this.getByIdAndTenantIsolation(edgeGatewayId, tenantIsolation);
        if(!edgeGatewayEntityResult.getSignal()){
            return Result.error(edgeGatewayEntityResult.getMessage());
        }
        EdgeGatewayEntity edgeGatewayEntity = edgeGatewayEntityResult.getResult();
        String iccid = edgeGatewayEntity.getTrafficCard();
        if(iccid == null || "".equals(iccid)){
            return Result.error("网关流量卡id为空，网关id:" + edgeGatewayId);
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        Result<Traffic5gInfo> result = getTraffic5gInfo(now, iccid, traffic5gAppKey, traffic5gAppScrect);
        return result;
    }
    
    private static final DateTimeFormatter yyyyMMddHHmmss = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    
    private static SSLConnectionSocketFactory ssf;

    static {
        try {
            SSLContext ctx = SSLContext.getInstance("TLS");    // 创建一个上下文（此处指定的协议类型似乎不是重点）
            X509TrustManager tm = new X509TrustManager() {     // 创建一个跳过SSL证书的策略
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
        
                public void checkClientTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
                }
        
                public void checkServerTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
                }
            };
            ctx.init(null, new TrustManager[] { tm }, null);
            ssf = new SSLConnectionSocketFactory(
                ctx, new String[] { "SSLv3", "TLSv1", "TLSv1.1", "TLSv1.2" }, null, NoopHostnameVerifier.INSTANCE
            );
        }catch(Exception e){
            log.error("初始化ssl异常: {}", e.getMessage());
        }

    }

    public static Result<Traffic5gInfo> getTraffic5gInfo(LocalDateTime time, String iccid, String traffic5gAppKey, String traffic5gAppScrect){
        String timestamp = yyyyMMddHHmmss.format(time);
        String params = "iccid=" + iccid + "{}" + traffic5gAppScrect + timestamp;
        String sign = "";
        try{
            sign = SignUtil.signMD5(params);
            log.debug("signMD5: {}", sign);
        }catch(Exception e){
            return Result.error("signMD5失败，" + e.getMessage());
        }
        
        String url = "https://cmp-api.ctwing.cn:20164/api/v1/openbill/queryTraffic?iccid=" + iccid;
        log.info(
            "traffic5g iccid:{},url:{},traffic5gAppKey:{},traffic5gAppScrect:{},timestamp:{},sign:{}",
            iccid, url, traffic5gAppKey, traffic5gAppScrect, timestamp, sign
        );
        
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        httpClientBuilder.setSSLSocketFactory(ssf);
        // 创建httpPost远程连接实例
        HttpPost post = new HttpPost(url);
        post.setHeader("Content-type", "application/json");
        post.setHeader("Accept", "*/*");
        post.setHeader("Accept-Encoding", "gzip,deflate,br");
        post.setHeader("Connection", "keep-alive");
        post.setHeader("AppKey", traffic5gAppKey);
        post.setHeader("Timestamp", timestamp);
        post.setHeader("Sign", sign);
        post.setEntity(new StringEntity("{}", "utf-8"));
        InputStream inputStream = null;
        try {
            CloseableHttpClient closeableHttpClient = httpClientBuilder.build();
            
            HttpResponse resp = closeableHttpClient.execute(post);
            inputStream = resp.getEntity().getContent();
        } catch (Exception e) {
            return Result.error("请求流量接口失败，" + e.getMessage());
        }
        
        try {
            return parseTraffic5gInfo(inputStream);
        } catch (DocumentException e){
            return Result.error("解析流量xml失败，" + e.getMessage());
        } catch(Exception e){
            return Result.error("解析流量xml异常，" + e.getMessage());
        }
    }
    
    public static Result<Traffic5gInfo> parseTraffic5gInfo(InputStream inputStream) throws DocumentException, IOException{
        

        byte[] bs = IOUtils.toByteArray(inputStream);
        String trafficStr = new String(bs, Charset.forName("UTF-8"));
        //"<?xml version="1.0" encoding="UTF-8"?><root><web:NEW_DATA_TICKET_QRsp xmlns:web="http://www.example.org/webservice"><IRESULT>0</IRESULT><TOTALCOUNT>1</TOTALCOUNT><TOTALPAGE>1</TOTALPAGE><TOTAL_BYTES_CNT>490.53MB</TOTAL_BYTES_CNT><GROUP_TRANSACTIONID>100000036520230329d5b9dbf33e</GROUP_TRANSACTIONID><number>8986112223304704261</number></web:NEW_DATA_TICKET_QRsp></root>"
        //"{"result":"-1","msg":"系统异常"}"
        //"<?xml version="1.0" encoding="UTF-8"?><root><web:NEW_DATA_TICKET_QRsp xmlns:web="http://www.example.org/webservice"><IRESULT>-1</IRESULT><SMSG>当前用户无权限查询该卡！</SMSG><GROUP_TRANSACTIONID>100000036520230329b9aae1cdf1</GROUP_TRANSACTIONID><number>8986112223304704555</number></web:NEW_DATA_TICKET_QRsp></root>"
        log.info("traffic5g info input: {}", trafficStr);
        if(trafficStr.startsWith("{")){
            return Result.error("流量接口异常：" + trafficStr);
        }
        Document document = new SAXReader().read(new ByteArrayInputStream(trafficStr.getBytes("UTF-8")));
        //<root>
        Element rootElement = document.getRootElement();
        Element web = rootElement.element("NEW_DATA_TICKET_QRsp");
        
        Traffic5gInfo info = new Traffic5gInfo();
        
        String iResultStr = web.element("IRESULT").getText();
        Integer iResult = Integer.parseInt(iResultStr);
        if(iResult != 0){
            String smsg = web.element("SMSG").getText();
            return Result.error("流量接口返回错误，IRESULT:" + iResultStr + ", SMSG:" + smsg);
        }
        info.setIresult(iResult);
        
        String totalCountStr = web.element("TOTALCOUNT").getText();
        Integer totalCount = Integer.parseInt(totalCountStr);
        info.setTotalCount(totalCount);
        
        String totalPageStr = web.element("TOTALPAGE").getText();
        Integer totalPage = Integer.parseInt(totalPageStr);
        info.setTotalPage(totalPage);
        
        String totalBytesCnt = web.element("TOTAL_BYTES_CNT").getText();
        info.setTotalBytesCnt(totalBytesCnt);
        
        String groupTransactionId = web.element("GROUP_TRANSACTIONID").getText();
        info.setGroupTransactionId(groupTransactionId);
        
        String number = web.element("number").getText();
        info.setNumber(number);
        
        return Result.ok(info);
    }
    
    private List<Long> getPagedEdgeGatewayIds(Long tenantId,Integer online) {
        String key = RedisConstant.EDGE_GATEWAY_STATUS + tenantId;
        Map<Object, Object> dataMap = stringRedisTemplate.opsForHash().entries(key);
        if(CollectionUtil.isEmpty(dataMap)){
            return new ArrayList<>();
        }
        List<Long> edgeGatewayIds = new ArrayList<>();
        for (Map.Entry<Object, Object> entry : dataMap.entrySet()) {
            if(String.valueOf(online).equals(String.valueOf(entry.getValue()))){
                edgeGatewayIds.add(Long.parseLong(String.valueOf(entry.getKey())));
            }
        }
        if(CollectionUtil.isEmpty(edgeGatewayIds)){
            edgeGatewayIds.add(-1L);
        }
        return edgeGatewayIds;
    }
    
    @Override
    public Result<List<Long>> selectEdgeGatewayIdList(List<Long> labelIds) {
        List<Long> edgeGatewayIdList =  edgeGatewayMapper.selectEdgeGatewayIdList(labelIds);
        return Result.ok(edgeGatewayIdList);
    }

    @Override
    public Result<HardwareInfo> hardwareInfo(TenantIsolation tenantIsolation, Long edgeGatewayId) {
        Result<Boolean> online = edgeGatewayOnline(tenantIsolation.getTenantId(), edgeGatewayId);
        if (!online.getResult()) {
            throw new BizException("离线状态的网关无法获取硬件详情");
        }
        Result<HardwareInfo> hardwareInfoResult = edgeGatewayControlProxy.hardwareInfo(edgeGatewayId,tenantIsolation.getTenantId());
        if(hardwareInfoResult.getSignal()){
            HardwareInfo hardwareInfo = hardwareInfoResult.getResult();
            hardwareInfo.setCurrentTime(LocalDateTime.now());
            return Result.ok(hardwareInfo);
        }
        return hardwareInfoResult;
    }

    @Override
    public Result<Void> updateEdgeGatewayMonitor(EditEdgeGatewayMonitorDTO dto,Integer type, TenantIsolation tenantIsolation) {
        EdgeGatewayEntity edgeGatewayEntity = edgeGatewayMapper.getById(tenantIsolation.getTenantId(),dto.getEdgeGatewayId());
        if(ObjectUtil.isEmpty(edgeGatewayEntity)){
            return Result.error("该租户下不存在该网关");
        }
        if(type == 0){
            if(ObjectUtil.isEmpty(dto.getSpaceMonitor())){
                return Result.error("网关空间监控比例不可为空");
            }
            edgeGatewayEntity.setSpaceMonitor(dto.getSpaceMonitor());
        }else {
            if(ObjectUtil.isEmpty(dto.getMemoryMonitor())){
                return Result.error("网关内存监控比例不可为空");
            }
            edgeGatewayEntity.setMemoryMonitor(dto.getMemoryMonitor());
        }
        edgeGatewayMapper.updateById(edgeGatewayEntity);
        return Result.ok();
    }

    @Override
    public Result<List<ExcelMessageDTO>> edgeGatewayBatchInput(TenantIsolation tenantIsolation, Integer exportType, List<EdgeGatewayExcel> list) {
        if(CollectionUtil.isEmpty(list)){
            return Result.error("导入的内容为空!");
        }
        List<ExcelMessageDTO> excelMessageDTOList = new ArrayList<>();
        List<EdgeGatewayEntity> insertEdgeGatewayEntityList = new ArrayList<>();
        List<EdgeGatewayEntity> edgeGatewayEntityList = edgeGatewayMapper.listAll(tenantIsolation.getTenantId());
        Set<String> nameSet = new HashSet();
        if(CollectionUtil.isNotEmpty(edgeGatewayEntityList)){
            nameSet = edgeGatewayEntityList.stream().map(EdgeGatewayEntity::getName).collect(Collectors.toSet());
        }
        for(EdgeGatewayExcel edgeGatewayExcel : list){
            String edgeGatewayName = edgeGatewayExcel.getName();
            //0:不保留;1:保留两者
            if(exportType == 0 && nameSet.contains(edgeGatewayExcel.getName())){
                if(excelMessageDTOList.size() >= 100){
                    continue;
                }
                ExcelMessageDTO excelMessageDTO = new ExcelMessageDTO(edgeGatewayExcel.getName(),"导入模式为不保留，网关名重复!");
                excelMessageDTOList.add(excelMessageDTO);
                //不保留类型，给错误提示
                continue;
            }
            if(exportType == 1 && nameSet.contains(edgeGatewayExcel.getName())){
                String randomString = RandomUtil.randomString(5);
                if(randomString.length() >= 58){
                    edgeGatewayName = edgeGatewayExcel.getName().subSequence(0,58) + randomString;
                }else {
                    edgeGatewayName = edgeGatewayExcel.getName() + randomString;
                }
            }
            edgeGatewayExcel.setName(edgeGatewayName);
            CreateEdgeGatewayDTO createEdgeGatewayDTO = new CreateEdgeGatewayDTO();
            BeanUtil.copyProperties(edgeGatewayExcel,createEdgeGatewayDTO);
            Result<EdgeGatewayEntity> edgeGatewayEntityResult = buildEdgeGateway(createEdgeGatewayDTO,tenantIsolation.getTenantId());
            if(!edgeGatewayEntityResult.getSignal()){
                if(excelMessageDTOList.size() >= 100){
                    continue;
                }
                ExcelMessageDTO excelMessageDTO = new ExcelMessageDTO(createEdgeGatewayDTO.getName(),edgeGatewayEntityResult.getMessage());
                excelMessageDTOList.add(excelMessageDTO);
                continue;
            }
            EdgeGatewayEntity edgeGatewayEntity = edgeGatewayEntityResult.getResult();
            Long edgeGatewayId = IdGenerator.generateId();
            edgeGatewayEntity.setId(edgeGatewayId);
            insertEdgeGatewayEntityList.add(edgeGatewayEntity);
        }
        if(CollectionUtil.isNotEmpty(insertEdgeGatewayEntityList)){
            edgeGatewayMapper.insertBatchSomeColumn(insertEdgeGatewayEntityList);
        }
        return Result.ok(excelMessageDTOList);
    }

    @Override
    public void exportEdgeGateway(HttpServletResponse response,Long tenantId ,Set<Long> edgeGatewayIds) throws IOException {
        List<EdgeGatewayEntity> edgeGatewayEntityList = edgeGatewayMapper.listEdgeGatewayByIds(edgeGatewayIds);
        List<EdgeGatewayExcel> edgeGatewayExcelList = new ArrayList<>();
        for(EdgeGatewayEntity edgeGatewayEntity: edgeGatewayEntityList){
            EdgeGatewayExcel edgeGatewayExcel = new EdgeGatewayExcel();
            BeanUtil.copyProperties(edgeGatewayEntity,edgeGatewayExcel);
            edgeGatewayExcelList.add(edgeGatewayExcel);
        }
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = tenantId.toString();
            fileName = URLEncoder.encode(fileName + "_edgeGateways", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EdgeGatewayExcel.class).sheet("edgeGateways")
                    .doWrite(edgeGatewayExcelList);
        } catch (Exception e) {
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            Map<String, String> map = MapUtils.newHashMap();
            map.put("status", "failure");
            map.put("message", "网关下载文件失败" + e.getMessage());
            response.getWriter().println(JSON.toJSONString(map));
        }
    }

    @Override
    public Result<List<EdgeGatewayEntity>> notConnectList(Long tenantId) {
        List<EdgeGatewayEntity> edgeGatewayEntityList = edgeGatewayMapper.getGwList(tenantId,EdgeGatewayTypeEnum.CONNECTOR_GATEWAY.getValue(),0,null);
        return Result.ok(edgeGatewayEntityList);
    }

    private Page<EdgeGatewayVO> getEdgeGatewayVOPage(EdgeGatewayDto edgeGatewayDto, Page<EdgeGatewayVO> page, TenantIsolation tenantIsolation) {
        if (edgeGatewayDto.getOnline() != null && edgeGatewayDto.getOnline() != -1) {
            List<Long> ids = getPagedEdgeGatewayIds(tenantIsolation.getTenantId(), edgeGatewayDto.getOnline());
            if(CollectionUtil.isNotEmpty(ids)){
                edgeGatewayDto.setIdList(ids);
            }
        }
        Page<EdgeGatewayVO> edgeGatewayPage = edgeGatewayMapper.pageEdgeGateway(page, edgeGatewayDto, tenantIsolation);
        return edgeGatewayPage;
    }

}
