package com.nti56.nlink.product.device.server.service;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.device.Device;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.model.device.dto.DeviceCheckInfoContext;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:11:44
 * @since JDK 1.8
 */
public interface IDeviceOptionService {

    Future<Result<Long>> doDeviceSync(Integer type,TenantIsolation tenantIsolation, DeviceEntity entity,
                                      Map<Long,List<LabelBindRelationEntity>> labelBindRelationEntityMap, //根据设备ID
                                      Map<Long,EdgeGatewayEntity> edgeGatewayEntityMap,
                                      Map<Long,List<ResourceRelationEntity>> resourceRelationEntityListMap, //根据设备ID
                                      Map<Long,ThingModelEntity> thingModelEntityMap,//根据继承modelId获取到的map
                                      Map<Long,List<ThingModelInheritEntity>> thingModelInheritEntityMap, //根据继承modelId获取到的map
                                      Map<Long,List<ThingServiceEntity>> thingServiceEntityMap,   //根据继承modelId获取到的map
                                      Map<Long,List<SubscriptionEntity>> subscriptionEntityMap, //根据继承deviceId,modelId获取到的map
                                      Map<Long,List<DeviceModelInheritEntity>> deviceModelinheritEntityListMap,  // 根据deviceId
                                      Map<Long,List<DeviceServiceEntity>> deviceServiceEntityMap, //根据deviceId)
                                      List<LabelEntity> labelEntityList,
                                      List<ChannelEntity> channelEntitieList,
                                      List<LabelGroupEntity> labelGroupEntityList);

    Result<Device> doDeviceSync2(TenantIsolation tenantIsolation,DeviceEntity device,DeviceCheckInfoContext context);

    Result<Void> doDeviceBatchOffline(TenantIsolation tenantIsolation, List<DeviceEntity> deviceEntities, Map<Long, String> gwNameMap);



}
