package com.nti56.nlink.product.device.server.handler.websocket;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ConcurrentHashSet;
import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.device.AccessElm;
import com.nti56.nlink.product.device.server.config.websocket.WsSessionManager;
import com.nti56.nlink.product.device.server.domain.thing.label.Label;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;
import com.nti56.nlink.product.device.server.entity.LabelEntity;
import com.nti56.nlink.product.device.server.entity.LabelGroupEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.service.IChannelParamService;
import com.nti56.nlink.product.device.server.service.IChannelService;
import com.nti56.nlink.product.device.server.service.IEdgeGatewayService;
import com.nti56.nlink.product.device.server.service.ILabelGroupService;
import com.nti56.nlink.product.device.server.service.ILabelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.ConcurrentWebSocketSessionDecorator;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName KeepCollectingHandler
 * @date 2022/9/22 18:38
 * @Version 1.0
 */
@Slf4j
@Component
public class LabelCollectingHandler extends TextWebSocketHandler {

    @Autowired
    IEdgeGatewayService edgeGatewayService;

    @Lazy
    @Autowired
    IChannelService channelService;

    @Autowired
    ILabelGroupService labelGroupService;

    @Autowired
    ILabelService labelService;
    @Autowired
    IChannelParamService channelParamService;
    
    /**
     * socket 建立成功事件
     *
     * @param session
     * @throws Exception
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        Long edgeGatewayId = (Long) session.getAttributes().get("edgeGatewayId");
        Long tenantId = (Long) session.getAttributes().get("tenantId");
        String sessionId = (String) session.getAttributes().get("sessionId");
        String key = WsSessionManager.LABEL_SESSION_KEY_PREFIX + edgeGatewayId.toString() + "#" + sessionId;
        ConcurrentWebSocketSessionDecorator decorator = new ConcurrentWebSocketSessionDecorator(session,500,20 * 1024 * 1024);
        if (tenantId != null) {
            WsSessionManager.add(key, decorator);
            log.info("websocket客户端创建连接成功，edgeGatewayId：{}，tenantId：{}，sessionId：{}",edgeGatewayId,tenantId,sessionId);
        } else {
            throw new BizException("websocket无效的会话！租户ID为空！");
        }
        Result<Boolean> result = edgeGatewayService.edgeGatewayOnline(tenantId, edgeGatewayId);
        if (result.getSignal() && result.getResult() && !WsSessionManager.isInit(sessionId)) {
            Result<List<ChannelEntity>> listResult = channelService.listByEdgeGatewayId(edgeGatewayId,tenantId,true);
            Set<Long> ids = listResult.getResult().stream().map(ChannelEntity::getId).collect(Collectors.toSet());
            WsSessionManager.initSessionPool(sessionId,new ConcurrentHashSet<>(ids));
            // 查询网关下的所有标签分组
            List<LabelGroupEntity> allChannelLabelGroupList = labelGroupService.lambdaQuery().in(LabelGroupEntity::getChannelId, ids).eq(LabelGroupEntity::getTenantId, tenantId).list();
            // 标签分组根据通道进行分组
            Map<Long, List<LabelGroupEntity>> channelLabelGroupMap = allChannelLabelGroupList.stream().collect(Collectors.groupingBy(LabelGroupEntity::getChannelId));
            Set<Long> allLabelGroupIdList = allChannelLabelGroupList.stream().map(LabelGroupEntity::getId).collect(Collectors.toSet());

            // 查询所有标签分组下的标签
            List<LabelEntity> allChannelLabelList = labelService.lambdaQuery().in(LabelEntity::getLabelGroupId, allLabelGroupIdList).eq(LabelEntity::getTenantId, tenantId).list();
            // 标签根据标签分组id二次分组
            Map<Long, List<LabelEntity>> labelGroupLabelMap = allChannelLabelList.stream().collect(Collectors.groupingBy(LabelEntity::getLabelGroupId));
            // 查询通道参数并根据通道id分组
            List<ChannelParamEntity> allChannelParamList = channelParamService.lambdaQuery().in(ChannelParamEntity::getChannelId, ids).eq(ChannelParamEntity::getTenantId, tenantId).list();
            Map<Long, List<ChannelParamEntity>> channelParamMap = allChannelParamList.stream().collect(Collectors.groupingBy(ChannelParamEntity::getChannelId));
            listResult.getResult().stream().forEach(channelEntity -> {
                try {
                    Long channelId = channelEntity.getId();
                    // 获取当前通道下的标签分组信息
                    List<LabelGroupEntity> labelGroupEntities = channelLabelGroupMap.get(channelId);
                    List<AccessElm> accessElmList = new ArrayList<>();
                    List<LabelEntity> labelList = new ArrayList<>();
                    if (CollUtil.isNotEmpty(labelGroupEntities)){
                        // 将每个标签分组的标签取出
                        for (LabelGroupEntity labelGroupEntity : labelGroupEntities) {
                            labelList.addAll(labelGroupLabelMap.getOrDefault(labelGroupEntity.getId(),new ArrayList<>()));
                        }
                    }
                    if (!labelList.isEmpty()) {
                        Label.label2Access(accessElmList,labelList,channelId);
                    }
                    List<ChannelParamEntity> paramEntities = channelParamMap.getOrDefault(channelId, new ArrayList<>());
                    channelService.collectingOnce(sessionId,key,decorator,channelEntity,accessElmList,paramEntities);
                } catch (Exception e) {
                    log.error("websocket线程池异常：{}",e.getMessage());
                    try {
                        session.sendMessage(new TextMessage(JSON.toJSONString(R.error(ServiceCodeEnum.WEBSOCKET_THREAD_POOL_FULL))));
                    }catch (Exception ignored){
                    } finally{
                        WsSessionManager.removeSessionInit(sessionId);
                        WsSessionManager.removeAndClose(key);
                    }
                }
            });
            log.info("websocket客户端提交第一次采集任务，edgeGatewayId：{}，tenantId：{}，sessionId：{}",edgeGatewayId,tenantId,sessionId);
        }

    }

    /**
     * 接收消息事件
     *
     * @param session
     * @param message
     * @throws Exception
     */
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        log.debug("websocket接收消息：{}",payload);
        if ("test".equals(payload)) {
            session.sendMessage(new TextMessage(payload));
        }
        String sessionId = (String) session.getAttributes().get("sessionId");
        WsSessionManager.removeSessionInit(sessionId);
    }



    /**
     * socket 断开连接时
     *
     * @param session
     * @param status
     * @throws Exception
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        Long edgeGatewayId = (Long) session.getAttributes().get("edgeGatewayId");
        log.info("websocket客户端已关闭：{}",session.getAttributes().get("sessionId"));
        if (edgeGatewayId != null) {
            WsSessionManager.removeAndClose(edgeGatewayId.toString());
        }
    }
}
