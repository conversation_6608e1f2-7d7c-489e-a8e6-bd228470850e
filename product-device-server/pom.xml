<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>nti-nlink-product-device-service</artifactId>
        <groupId>com.nti56.nlink</groupId>
        <version>2.4.10</version>
    </parent>

    <artifactId>product-device-server</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <app.main.class>com.nti56.nlink.product.device.server.ProductDeviceServerApplication</app.main.class>
        <server.port>18801</server.port>
        <maven.jib.skip>true</maven.jib.skip>
        <tomcat.version>9.0.30</tomcat.version>
        <sonar.login>admin</sonar.login>
        <sonar.password>nti56.com</sonar.password>
        <sonar.host>http://***********:9000/</sonar.host>
        <sonar.skip>true</sonar.skip>
        <skipTests>true</skipTests>
    </properties>

    <dependencies>
     <!--   <dependency>
            <groupId>com.nti56.cloud</groupId>
            <artifactId>nti-cloudnest-starter</artifactId>
        </dependency>-->
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-juli</artifactId>
            <version>${tomcat.version}</version>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-gson</artifactId>
        </dependency>
        <dependency>
            <groupId>io.vertx</groupId>
            <artifactId>vertx-web-proxy</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>com.nti56.nlink</groupId>
            <artifactId>nti-nlink-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.3.0</version>
        </dependency>




        <!--aliyun短信服务-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dysmsapi20170525</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.9</version>
        </dependency>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.5.21</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.5.21</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-models</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.alibaba</groupId>-->
        <!--            <artifactId>druid-spring-boot-starter</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>io.vertx</groupId>
            <artifactId>vertx-mqtt</artifactId>
        </dependency>
        <dependency>
            <groupId>io.vertx</groupId>
            <artifactId>vertx-redis-client</artifactId>
        </dependency>


        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.4.1</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.paho</groupId>
            <artifactId>org.eclipse.paho.mqttv5.client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>

        <!--   <dependency>
               <groupId>com.baomidou</groupId>
               <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
               <version>3.5.1</version>
           </dependency>-->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.craftsman</groupId>
            <artifactId>dozer-jdk8-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.nti56.csplice</groupId>
            <artifactId>nti-csplice-spring-orm-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>druid</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.taosdata.jdbc</groupId>
            <artifactId>taos-jdbcdriver</artifactId>
            <version>2.0.34</version>
        </dependency>

        <dependency>
            <groupId>com.influxdb</groupId>
            <artifactId>influxdb-client-reactive</artifactId>
            <version>6.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.influxdb</groupId>
            <artifactId>flux-dsl</artifactId>
            <version>6.4.0</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-common</artifactId>
        </dependency>
        <dependency>
            <groupId>io.vertx</groupId>
            <artifactId>vertx-web-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>1.3.9</version>
        </dependency>

       <dependency>
           <groupId>com.nti56.nlink</groupId>
           <artifactId>product-device-client</artifactId>
       </dependency>

        <dependency>
            <groupId>com.nti56.nlink</groupId>
            <artifactId>rule-engine-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.nti56.nlink</groupId>
                    <artifactId>common-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- <dependency>
             <groupId>com.nti56.nlink</groupId>
             <artifactId>alarm-client</artifactId>
         </dependency>-->


        <dependency>
            <groupId>com.nti56.nlink</groupId>
            <artifactId>jwt-token-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>net.minidev</groupId>
                    <artifactId>json-smart</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.minidev</groupId>
            <artifactId>json-smart</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
        </dependency>
        <!--        <dependency>
                    <groupId>com.nti56</groupId>
                    <artifactId>nti-ms-oss-api</artifactId>
                </dependency>-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>net.devh</groupId>-->
<!--            <artifactId>grpc-client-spring-boot-starter</artifactId>-->
<!--            <version>2.13.1.RELEASE</version>-->
<!--        </dependency>-->

        <!--        <dependency>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-neo4j</artifactId>
                </dependency>-->

        <dependency>
            <groupId>com.github.mengweijin</groupId>
            <artifactId>db-migration-dm</artifactId>
            <version>1.1.6</version>
        </dependency>
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
            <version>4.16.0</version>
        </dependency>
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
            <version>8.1.3.140</version>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>19.3.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>9.4.0.jre8</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>runtime</id>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <excludes>
                            <exclude>export.sql</exclude>
                        </excludes>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>sonar</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <sonar.host.url>${sonar.host}</sonar.host.url>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot-maven-plugin.version}</version>
                <configuration>
                    <mainClass>${app.main.class}</mainClass>
                    <layout>ZIP</layout>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--使用jib插件-->
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <!--from节点用来设置镜像的基础镜像，相当于Docerkfile中的FROM关键字-->
                    <from>
                        <!--使用openjdk官方镜像，tag是8-jdk-stretch，表示镜像的操作系统是debian9,装好了jdk8-->
                        <image>${docker.registry}/nlink/${docker.base.arm}</image>
                        <auth>
                            <username>${docker.username}</username>
                            <password>${docker.password}</password>
                        </auth>
                    </from>
                    <to>
                        <image>${docker.registry}/nlink/${project.artifactId}:${project.version}</image>
                        <auth>
                            <username>${docker.username}</username>
                            <password>${docker.password}</password>
                        </auth>
                        <tags>
                            <tag>latest</tag>
                        </tags>
                    </to>
                    <!--容器相关的属性-->
                    <container>
                        <!--jvm内存参数-->
                        <jvmFlags>
                            <jvmFlag>-Xms4g</jvmFlag>
                            <jvmFlag>-Xmx4g</jvmFlag>
                            <jvmFlag>-Dfile.encoding=UTF-8</jvmFlag>
                        </jvmFlags>
                        <mainClass>${app.main.class}</mainClass>
                        <!--要暴露的端口-->
                        <ports>
                            <port>${server.port}</port>
                        </ports>
                        <!--配置使用的时区-->
                        <environment>
                            <TZ>Asia/Shanghai</TZ>
                        </environment>
                        <creationTime>USE_CURRENT_TIMESTAMP</creationTime>
                        <appRoot>/opt/nti-server</appRoot>
                    </container>
                    <allowInsecureRegistries>true</allowInsecureRegistries>
                    <skip>${maven.jib.skip}</skip>
                </configuration>

                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.6.0.1398</version>
                <configuration>
                    <skip>${sonar.skip}</skip>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>sonar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
