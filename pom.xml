<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.nti56.csplice</groupId>
        <artifactId>nti-csplice-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <groupId>com.nti56.nlink</groupId>
    <artifactId>nti-nlink-product-device-service</artifactId>
    <version>2.4.10</version>
    <modules>
        <module>product-device-client</module>
        <module>product-device-server</module>
    </modules>
    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <docker.registry>10.1.21.215:5000</docker.registry>
        <docker.base.arm>openjdk:1.8u332</docker.base.arm>
        <docker.base.amd>openjdk:1.8u332</docker.base.amd>
        <docker.username>admin</docker.username>
        <docker.password>Nti56@com</docker.password>
        <nlink-common.version>1.1.1-SNAPSHOT</nlink-common.version>
        <logback-classic.version>1.2.3</logback-classic.version>
        <vertx.version>4.3.3</vertx.version>
        <fastjson.version>1.2.83</fastjson.version>
        <protobuf.version>3.19.3</protobuf.version>
        <cloudnest-starter-version>1.0.8-SNAPSHOT</cloudnest-starter-version>
    </properties>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.nti56.cloud</groupId>
                <artifactId>nti-cloudnest-starter</artifactId>
                <version>${cloudnest-starter-version}</version>
            </dependency>
            <dependency>
                <groupId>com.nti56.nlink</groupId>
                <artifactId>nti-nlink-common</artifactId>
                <version>${nlink-common.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>2.2.6.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>io.craftsman</groupId>
                <artifactId>dozer-jdk8-support</artifactId>
                <version>1.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.nti56.csplice</groupId>
                <artifactId>nti-csplice-spring-orm-starter</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>io.vertx</groupId>
                <artifactId>vertx-dependencies</artifactId>
                <version>${vertx.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.eclipse.paho</groupId>
                <artifactId>org.eclipse.paho.mqttv5.client</artifactId>
                <version>1.2.5</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback-classic.version}</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.13.1</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.22</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>1.3.9</version>
            </dependency>

            <dependency>
                <groupId>com.nti56.nlink</groupId>
                <artifactId>product-device-client</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.nti56.nlink</groupId>
                <artifactId>jwt-token-spring-boot-starter</artifactId>
                <version>1.0.4-SNAPSHOT</version>
            </dependency>

            <!--aliyun短信服务-->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>2.0.9</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibaba-dingtalk-service-sdk</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.8</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>3.17.0</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>2.10.3</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>3.17.0</version>
            </dependency>
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>javax.mail</artifactId>
                <version>1.6.2</version>
            </dependency>
            <dependency>
                <groupId>net.minidev</groupId>
                <artifactId>json-smart</artifactId>
                <version>2.3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.19</version>
            </dependency>
            <dependency>
                <groupId>com.nti56</groupId>
                <artifactId>nti-ms-oss-api</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>2.12.6</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.nti56.nlink</groupId>
                <artifactId>rule-engine-client</artifactId>
                <version>1.8.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.nti56.nlink</groupId>
                <artifactId>alarm-client</artifactId>
                <version>1.8.2-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.3.2</version>
                <configuration>
                    <charset>UTF-8</charset>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
